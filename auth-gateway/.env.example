# PostgreSQL envs
POSTGRES_HOST="postgres-auth-gateway" #Could be used in the future if we will ditch dc
POSTGRES_PORT="5432"
POSTGRES_SSLMODE="disable"
POSTGRES_PASSWORD="postgres"
POSTGRES_USER="postgres"
POSTGRES_DB="auth-gateway"
CLIENT_ID=
CLIENT_SECRET=
REDIRECT_URI="https://polity.quantumobile.com/oauth/callback"
REDIS_HOST="redis"
REDIS_PORT="6379"
REDIS_PASSWORD=26ecfc64-50d1-4317-b6a2-2f39aa521f23
SENDGRID_API_KEY=
APPLICATION_HOST="https://polity-stage.quantumobile.com"
POLITY_EMAIL_ADDRESS="<EMAIL>"
SLACK_WEBHOOK_FOR_TECHNICAL=
SLACK_WEBHOOK_FOR_BUSINESS=
AUTH_HOST="http://localhost:3000"
POLITY_VAULT_HOST=**************
POLITY_VAULT_PORT=8000
