package com.dcs.authgateway.exception

import com.dcs.authgateway.controller.UsernameAlreadyTakenException
import io.jsonwebtoken.ExpiredJwtException
import jakarta.servlet.ServletException
import jakarta.servlet.http.HttpServletRequest
import jakarta.validation.ConstraintViolationException
import net.sf.jsqlparser.util.validation.metadata.DatabaseException
import org.apache.coyote.BadRequestException
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.client.HttpStatusCodeException
import java.io.IOException
import java.net.SocketTimeoutException
import java.time.LocalDateTime

@ControllerAdvice
class ApiExceptionHandler {

    @ExceptionHandler(value = [ExpiredJwtException::class])
    fun handleExpiredJwtException (exception: ExpiredJwtException): ResponseEntity<Any> {
        return ResponseEntity(HttpStatus.UNAUTHORIZED)
    }

    @ExceptionHandler(value = [HttpMessageNotReadableException::class])
    fun handleHttpMessageNotReadableException (
        exception: HttpMessageNotReadableException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [BadRequestException::class])
    fun handleBadRequestException (
        exception: BadRequestException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [CustomerRouteException::class])
    fun handleCustomerRouteNotFoundException (
        exception: CustomerRouteException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [TokenRefreshException::class])
    fun handleTokenRefreshException (
        exception: HttpMessageNotReadableException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [Exception::class])
    fun handleException (
        exception: Exception,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.INTERNAL_SERVER_ERROR, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.INTERNAL_SERVER_ERROR)
    }

    @ExceptionHandler(value = [HttpStatusCodeException::class])
    fun handleHttpStatusCodeException (
        exception: HttpStatusCodeException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [ConstraintViolationException::class])
    fun handleConstraintViolationException (
        exception: ConstraintViolationException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [SocketTimeoutException::class])
    fun handleSocketTimeoutException (
        exception: SocketTimeoutException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.GATEWAY_TIMEOUT, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.GATEWAY_TIMEOUT)
    }

    @ExceptionHandler(value = [UsernameNotFoundException::class])
    fun handleUsernameNotFoundException (
        exception: UsernameNotFoundException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [CodeToUUIDException::class])
    fun handleCodeToUUIDException (
        exception: CodeToUUIDException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [DatabaseException::class])
    fun handleDatabaseException (
        exception: DatabaseException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [ServletException::class])
    fun handleServletException (
        exception: ServletException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [IOException::class])
    fun handleIOException (
        exception: IOException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [UsernameAlreadyTakenException::class])
    fun handleUsernameAlreadyTakenException (
        exception: UsernameAlreadyTakenException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [RuntimeException::class])
    fun handleUsernameRuntimeException (
        exception: RuntimeException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(value = [EmailTemplateException::class])
    fun handleEmailTemplateException (
        exception: EmailTemplateException,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {

        val exceptionResponse = buildErrorResponse(httpRequest, HttpStatus.BAD_REQUEST, exception)
        return ResponseEntity(exceptionResponse, HttpStatus.BAD_REQUEST)
    }



    private fun buildErrorResponse(
        httpRequest: HttpServletRequest, httpStatus: HttpStatus, throwable: Throwable
    ): ApiException {
        return ApiException(
            timestamp = LocalDateTime.now(),
            status = httpStatus.value(),
            path = httpRequest.servletPath,
            error = httpStatus.name,
            message = throwable.message,
        )
    }
}