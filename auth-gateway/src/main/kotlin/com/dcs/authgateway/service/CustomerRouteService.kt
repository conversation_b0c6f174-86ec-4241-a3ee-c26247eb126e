package com.dcs.authgateway.service

import com.dcs.authgateway.dto.AdviserResponseDto
import com.dcs.authgateway.dto.AvatarResponseDto
import com.dcs.authgateway.dto.NetworkOperatorStatistics
import com.dcs.authgateway.entity.enum.Role
import com.dcs.authgateway.exception.CustomerRouteException
import com.dcs.authgateway.entity.CustomerRoute
import com.dcs.authgateway.repository.CustomerRouteRepository
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class CustomerRouteService(private val customerRouteRepository: CustomerRouteRepository) {

    fun findAllByCustomerIdAndRole(customerId: Long, role: Role): List<CustomerRoute> {
        return customerRouteRepository.findAllByCustomerIdAndRole(customerId, role) ?: emptyList()
    }

    fun findCustomerRoutesByRole(role: Role): List<CustomerRoute> {
        return customerRouteRepository.findByRole(role)
    }


    fun findByUsernameAndFetchRoutesEagerly(username: String): CustomerRoute {
        return customerRouteRepository.findByUsername(username) ?: throw CustomerRouteException("Route hasn't been found")
    }

    fun isExistsByUsername(username: String): Boolean {
        return customerRouteRepository.existsCustomerRouteByUsername(username)
    }

    fun existsByFullName(fullName: String): Boolean {
        return customerRouteRepository.existsByFullName(fullName)
    }

    fun findByUuid(uuid: UUID): CustomerRoute {
        return customerRouteRepository.findByUuid(uuid) ?: throw CustomerRouteException("Route hasn't been found")
    }

    @Throws(CustomerRouteException::class)
    fun getCustomerRouteById(id: Long): CustomerRoute {
        return customerRouteRepository.findByIdOrNull(id)
            ?: throw CustomerRouteException("Route hasn't been found")

    }


    fun save(
        username: String,
        picture: String?,
        role: Role,
        fullName: String? = null,
        customerId: Long? = null,
        hasAcquiredNode: Boolean = false,
        redirectUrl: String? = System.getenv("POLITY_HOST"),
        uuid: UUID = UUID.randomUUID(),
        element_id: String?
    ): CustomerRoute {

        if (customerRouteRepository.existsCustomerRouteByUsername(username))
            throw CustomerRouteException("username: $username already in use")

        if (customerRouteRepository.existsCustomerRouteByElementId(element_id?:""))
            throw CustomerRouteException("element_id: $element_id already in use")

        val customerRoute = CustomerRoute(
            role = role,
            customerId = customerId,
            username = username,
            profilePicture = picture,
            fullName = fullName,
            hasAcquiredNode = hasAcquiredNode,
            route = redirectUrl,
            uuid = uuid,
            elementId = element_id
        )

        return customerRouteRepository.save(customerRoute)
    }

    @Transactional
    fun update(customerRoute: CustomerRoute): CustomerRoute? {
        customerRouteRepository.save(customerRoute)
        return customerRoute
    }


    fun entityToAdviserDto(customerRoute: CustomerRoute): AdviserResponseDto {
        return AdviserResponseDto(
            customerRoute.id!!,
            customerRoute.role,
            customerRoute.username,
            customerRoute.profilePicture,
            customerRoute.elementId
        )
    }

    fun entityToAvatarDto(customerRoute: CustomerRoute): AvatarResponseDto {
        return AvatarResponseDto(
            customerRoute.id!!,
            customerRoute.route,
            customerRoute.role,
            customerRoute.username,
            customerRoute.profilePicture,
            customerRoute.hasAcquiredNode,
            customerRoute.elementId
        )
    }

    fun findCustomerRoutesByRoleContainingName(name: String): List<CustomerRoute> {
        return customerRouteRepository.findByUsernameContainingAndRole(name, Role.ADVISER)
    }

    fun getByUsername(username: String): CustomerRoute {
        return customerRouteRepository.findByUsername(username) ?: throw CustomerRouteException("Route hasn't been found")
    }

    fun countCustomersByRole(): NetworkOperatorStatistics {
        val statistics = customerRouteRepository.countCustomersByRole()
            .associate { it.role to it.total }
//        val adviserToAvatarCount = customerRouteRepository.countByRoleAndHasAcquiredNode(Role.AVATAR, true)
        val advisers = customerRouteRepository.findByRole(Role.ADVISER)
            .associate { it.route to it.username }
        var avatarCount = 0L
        val adviserByAvatars: Map<String, Long> = customerRouteRepository.avatarsByRoute().map {
            avatarCount += it.total
            advisers[it.route] to it.total
        }.associate {
            it.first!! to it.second
        }
        return NetworkOperatorStatistics(
            investorsCount = statistics[Role.INVESTOR] ?: 0,
            adviserCount = statistics[Role.ADVISER] ?: 0,
            avatarsCount = statistics[Role.AVATAR] ?: 0,
            avatarsWithNodes = avatarCount,
            avatarPerAdviser = adviserByAvatars
        )
    }
}
