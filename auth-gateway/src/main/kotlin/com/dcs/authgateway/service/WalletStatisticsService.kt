package com.dcs.authgateway.service

import com.dcs.authgateway.dto.WalletStatistics
import com.dcs.authgateway.dto.WalletStatisticsResult
import com.dcs.authgateway.dto.networkOperator.WalletType

interface WalletStatisticsService {

    fun updateWalletStatistics(
        walletType: WalletType,
        count: Long
    ): WalletStatistics

    fun getWalletStatistics(walletType: WalletType): WalletStatistics

    fun getAllWalletStatistics(): WalletStatisticsResult
}