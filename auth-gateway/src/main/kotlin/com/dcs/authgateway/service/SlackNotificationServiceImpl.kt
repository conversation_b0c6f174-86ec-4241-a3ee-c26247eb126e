package com.dcs.authgateway.service

import com.dcs.authgateway.dto.SlackNotification
import com.dcs.authgateway.entity.slack.SlackNotificationEntity
import com.dcs.authgateway.entity.slack.SlackNotificationType
import com.dcs.authgateway.repository.SlackNotificationRepository
import org.springframework.stereotype.Service

@Service
class SlackNotificationServiceImpl(
    private val slackNotificationRepository: SlackNotificationRepository
) : SlackNotificationService {

    override fun saveSlackNotification(type: SlackNotificationType, text: String) {
        val entity = SlackNotificationEntity(
            id = null,
            slackNotificationType = type,
            text = text
        )

        slackNotificationRepository.save(entity)
    }

    private fun SlackNotificationEntity.toSlackNotification(): SlackNotification {
        return SlackNotification(
            id = id!!,
            createdAt = createdAt,
            slackNotificationType = slackNotificationType,
            text = text
        )
    }
}