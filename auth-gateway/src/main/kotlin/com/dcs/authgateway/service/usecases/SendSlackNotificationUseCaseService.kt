package com.dcs.authgateway.service.usecases

import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatusCode

interface SendSlackNotificationUseCaseService {
    fun sendSlackBusinessNotification(
        investorName: String, avatarName: String, adviserName: String
    ): HttpStatusCode
    fun sendSlackTechNotification(
        avatarName: String, request_uuid: String, wallet_type: String
    ): HttpStatusCode
}