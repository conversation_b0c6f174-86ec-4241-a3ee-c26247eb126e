package com.dcs.authgateway.service

import com.dcs.authgateway.entity.enum.EmailTemplate
import com.sendgrid.Method
import com.sendgrid.Request
import com.sendgrid.Response
import com.sendgrid.SendGrid
import com.sendgrid.helpers.mail.Mail
import com.sendgrid.helpers.mail.objects.ClickTrackingSetting
import com.sendgrid.helpers.mail.objects.Content
import com.sendgrid.helpers.mail.objects.Email
import com.sendgrid.helpers.mail.objects.TrackingSettings
import org.springframework.stereotype.Service


@Service
class EmailServiceImpl(
    private val codeToUUIDService: CodeToUUIDService
) : EmailService {

    companion object {
        const val EMAIL_CONTENT_TYPE = "text/plain"
        const val HTML_CONTENT_TYPE = "text/html"
        val SENDGRID_API_KEY: String = System.getenv("SENDGRID_API_KEY")
        val APPLICATION_HOST: String = System.getenv("APPLICATION_HOST")
        val POLITY_EMAIL_ADDRESS: String = System.getenv("POLITY_EMAIL_ADDRESS")
    }


    override fun sendEmail(name: String, emailTemplate: EmailTemplate, sendTo: String, passCode: String?): Response {

        val sg = SendGrid(SENDGRID_API_KEY)

        val mail = createMail(name, emailTemplate, sendTo, passCode)

        // Disable click tracking
        val clickTrackingSetting = ClickTrackingSetting()
        clickTrackingSetting.enable = false
        val trackingSettings = TrackingSettings()
        trackingSettings.clickTrackingSetting=clickTrackingSetting
        mail.setTrackingSettings(trackingSettings)
        mail.replyTo = Email("<EMAIL>")
        val request = Request()
        request.method = Method.POST
        request.endpoint = "mail/send"
        request.body = mail.build()
        return sg.api(request)
    }

    private fun createMail(name: String, emailTemplate: EmailTemplate, sendTo: String, passCode: String?): Mail {
        val code = codeToUUIDService.generateCode()

        val from = Email(POLITY_EMAIL_ADDRESS)

        val subject = "Invitation to Participate in Closed Testing of the Polity MVP Release Platform"
        val to = Email(sendTo)

        val content = getTextContent(emailTemplate, name, code, passCode)

        val htmlContent = getHtmlContent(emailTemplate, name, code, passCode)

        val mail = Mail(from, subject, to, content)
        mail.addContent(htmlContent)
        return mail
    }

    private fun getHtmlContent(emailTemplate: EmailTemplate, name: String, code: Int, passCode: String?): Content {
        return when(emailTemplate) {
            EmailTemplate.INVESTOR -> {
                Content(
                    HTML_CONTENT_TYPE,
                    "<i> <p>Dear $name,\n<br>" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "Bastion Team kindly invites you to participate in the trial operation of the private minimum viable <br>\n" +
                            "\n" +
                            "product release (Private MVP) of the Polity network - a new generation decentralised wealth <br>\n" +
                            "\n" +
                            "(DeWealth) management marketplace for professional service providers and merchants and <br>\n" +
                            "\n" +
                            "their customers. <br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "You could pass the registration procedures by clicking the link: <br>\n" +
                            "\n" +
                            "$APPLICATION_HOST/?code=$code and following the instructions on the Polity Portal.<br>\n" +
                            "\n" +
                            "Please note that this link is issued to you personally and should not be shared with third parties.<br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "In order to on-board, you will need to set up accounts with the two of our partners: <br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "1. Fractal ID – this is our chosen partner for verifying an individual’s identity.  For this <br>\n" +
                            "\n" +
                            "demonstration version, you can supply dummy documentation rather than your true <br>\n" +
                            "\n" +
                            "legal documents.  You will however need to take a selfie to verify you are not <br>\n" +
                            "\n" +
                            "a machine. <br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "2. Element.io account – this is our chosen partner for secure communication between <br>\n" +
                            "\n" +
                            "parties within the Polity network. <br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "Once on-boarded, you will need to do the following before being able to transact: <br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "1. Access the platform as an Investor – this is the persona that we would like you to <br>\n" +
                            "\n" +
                            "provide feedback on. <br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "2. Create an Avatar – this will be an identity that you use to interact with other users within <br>\n" +
                            "\n" +
                            "the network (you can have up to 3 Avatars in this release).  Please ensure that you have <br>\n" +
                            "\n" +
                            "an image to hand that can be uploaded and used to recognise the Avatar you create. <br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "3. Set-up and fund at least one wallet for at least one Avatar.  At this stage, as the Private <br>\n" +
                            "\n" +
                            "MVP is undergoing a security review, we recommend to limit funding to USDT 100 or <br>\n" +
                            "\n" +
                            "equivalent per wallet. <br>\n" +
                            "\n" +
                            "<br>\n" +
                            "\n" +
                            "We would be grateful for your feedback on the network features and user experience. <br>\n" +
                            "<br>\n" +
                            "With best regards,<br>\n" +
                            "Bastion Team.</p> </i>"
                )
            }
            EmailTemplate.ADVISER, EmailTemplate.NETWORK_OPERATOR -> {
                Content(
                    HTML_CONTENT_TYPE,
                    "<p>Dear $name,\n<br>" +
                            "\n" +
                            "Bastion team kindly invites you to participate in the trial operation of the private release of the Polity platform - a new generation crypto wealth management network for professional advisors and qualified investors.<br>\n" +
                            "\n" +
                            "You could pass the registration procedures by clicking the link: $APPLICATION_HOST/?code=$code and following the instructions on the Polity Portal.<br>\n" +
                            "\n" +
                            "By your request we are granting you demo access to another two key user accounts on the platform: Advisor and Operator.<br>\n" +
                            "You can access those demo accounts with your personal authentication code $passCode just after the registration process is completed.<br>\n" +
                            "\n" +
                            "We would be grateful for your feedback on the platform features and user experience.<br>\n" +
                            "\n" +
                            "With best regards,<br>\n" +
                            "Bastion Team.</p>"
                )
            }
        }
    }

    private fun getTextContent(emailTemplate: EmailTemplate, name: String, code: Int, passCode: String?): Content {
        return when(emailTemplate) {
            EmailTemplate.INVESTOR -> {
                Content(
                    EMAIL_CONTENT_TYPE,
                    "Dear $name,\n" +
                            "\n" +
                            "Bastion team kindly invites you to participate in the trial operation of the private release of the Polity platform - a new generation crypto wealth management network for professional advisors and qualified investors.\n" +
                            "\n" +
                            "You could pass the registration procedures by clicking the link: $APPLICATION_HOST/?code=$code and following the instructions on the Polity Portal.\n" +
                            "\n" +
                            "We would be grateful for your feedback on the platform features and user experience.\n" +
                            "\n" +
                            "With best regards,\n" +
                            "Bastion Team."
                )
            }
            EmailTemplate.ADVISER, EmailTemplate.NETWORK_OPERATOR -> {
                Content(
                    EMAIL_CONTENT_TYPE,
                    "Dear $name,\n" +
                            "\n" +
                            "Bastion team kindly invites you to participate in the trial operation of the private release of the Polity platform - a new generation crypto wealth management network for professional advisors and qualified investors.\n" +
                            "\n" +
                            "You could pass the registration procedures by clicking the link: $APPLICATION_HOST/?code=$code and following the instructions on the Polity Portal.\n" +
                            "\n" +
                            "By your request we are granting you demo access to another two key user accounts on the platform: Advisor and Operator.\n" +
                            "You can access those demo accounts with your personal authentication code $passCode just after the registration process is completed.\n" +
                            "\n" +
                            "We would be grateful for your feedback on the platform features and user experience.\n" +
                            "\n" +
                            "With best regards,\n" +
                            "Bastion Team."
                )
            }
        }
    }
}