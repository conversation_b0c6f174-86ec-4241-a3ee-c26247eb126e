package com.dcs.authgateway.service

import com.dcs.authgateway.exception.CodeToUUIDException
import com.dcs.authgateway.entity.CodeToUUID
import com.dcs.authgateway.repository.CodeToUUIDRepository
import org.springframework.stereotype.Service
import java.util.*

@Service
class CodeToUUIDServiceImpl(
    private val codeToUUIDRepository: CodeToUUIDRepository
) : CodeToUUIDService {

    override fun generateCode(): Int {

        var generatedCode: Int

        while (true) {
            generatedCode = generateRandom6DigitNumber()
            if (!codeToUUIDRepository.existsById(generatedCode)) {
                codeToUUIDRepository.save(CodeToUUID(generatedCode, ""))
                break
            }
        }

        return generatedCode
    }

    override fun verifyCode(code: Int): <PERSON><PERSON><PERSON> {
        val codeToUUID = codeToUUIDRepository.findById(code).orElseThrow { CodeToUUIDException("Invalid code") }
        return codeToUUID.uuid.isBlank()
    }

    override fun setUUIDToCode(code: Int, uuid: String): CodeToUUID {
        val res = codeToUUIDRepository.findById(code).orElseThrow { CodeToUUIDException("Invalid code") }
        if (res.uuid.isBlank()) {
            return codeToUUIDRepository.save(CodeToUUID(code, uuid))
        } else {
            throw CodeToUUIDException("UUID for code: $code already set")
        }
    }

    private fun generateRandom6DigitNumber(): Int {
        val rnd = Random()
        return rnd.nextInt(999999)
    }
}