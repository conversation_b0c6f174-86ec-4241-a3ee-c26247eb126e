package com.dcs.authgateway.service.usecases

import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.coyote.BadRequestException
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException.BadRequest
import org.springframework.web.client.RestTemplate
import java.util.HashMap

@Service
class TurnOnCoSignerServiceImpl(
    private val polityVaultRestTemplate: RestTemplate,
    private val objectMapper: ObjectMapper,
) : TurnOnCoSignerService {

    companion object {
        val POLITY_VAULT_HOST: String = System.getenv("POLITY_VAULT_HOST")
        val POLITY_VAULT_PORT: String = System.getenv("POLITY_VAULT_PORT")
    }


    override fun notify(uuid: String) {
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON

        val notifyCoSignerTurnOnRequest = NotifyCoSignerTurnOnRequest(uuid)
        val request = HttpEntity(objectMapper.writeValueAsString(notifyCoSignerTurnOnRequest), headers)

        try {
            polityVaultRestTemplate.postForEntity(
                "http://$POLITY_VAULT_HOST:$POLITY_VAULT_PORT/api/v1/safeheron/trigger/co-signer/", request, HashMap::class.java
            )
        } catch (e: Exception) {
            throw BadRequestException("Something went wrong with co-signer")
        }

    }

    data class NotifyCoSignerTurnOnRequest(
        val user_uuid: String
    )
}

