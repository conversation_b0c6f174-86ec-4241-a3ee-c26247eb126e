package com.dcs.authgateway.service

import com.dcs.authgateway.entity.CustomerRoute
import io.jsonwebtoken.Claims
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.stereotype.Service
import java.util.*

@Service
class JwtService {
    @Value("\${urls.authGatewayUrl}")
    var authGatewayUrl: String? = ""

    fun getUserName(token: String): String? {
        return getClaims(token).subject
    }

    fun getExpiration(token: String): Date {
        return getClaims(token).expiration
    }

    fun getClaims(token: String): Claims {
        return Jwts
            .parserBuilder()
            .setSigningKey(SECRET_KEY.toByteArray())
            .build()
            .parseClaimsJws(token)
            .body
    }

//    fun generateToken(userDetails: UserDetails): String {
//        return generateToken(emptyMap(), userDetails)
//    }

    fun isTokenValid(token: String, userDetails: UserDetails): Boolean {
        val username = getUserName(token)
        return username == userDetails.username && !isTokenExpired(token)
    }

    fun isTokenExpired(token: String): Boolean {
        return getExpiration(token).before(Date())
    }

    fun generateToken(customerRoute: CustomerRoute, userDetails: UserDetails): String {
        return buildToken(customerRoute, userDetails, JWT_EXPIRATION)
    }

    fun generateRefreshToken(customerRoute: CustomerRoute, userDetails: UserDetails): String {
        return buildToken(customerRoute, userDetails, REFRESH_EXPIRATION)
    }

    private fun buildToken(customerRoute: CustomerRoute, userDetails: UserDetails, expiration: Long): String {
        val photo = customerRoute.profilePicture?.replace("uploads/", "$authGatewayUrl/uploads/")

        val claimMap = mapOf(
            "username" to customerRoute.username,
            "photo" to photo,
            "role" to customerRoute.role,
            "uuid" to customerRoute.uuid
        )

        return Jwts
            .builder()
            .setClaims(claimMap)
            .setSubject(userDetails.username)
            .setIssuedAt(Date(System.currentTimeMillis()))
            .setExpiration(Date(System.currentTimeMillis() + expiration))
            .signWith(SignatureAlgorithm.HS512, SECRET_KEY.toByteArray())
            .compact()
    }


    private companion object {
        const val SECRET_KEY = "404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970"
        const val JWT_EXPIRATION = 86400000L // 1000 * 60 * 60 * 24 // one day
        const val REFRESH_EXPIRATION = 604800000L // 7 days
    }
}

