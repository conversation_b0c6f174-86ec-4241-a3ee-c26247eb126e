package com.dcs.authgateway.service

import org.springframework.core.io.Resource
import org.springframework.core.io.UrlResource
import org.springframework.stereotype.Service
import org.springframework.util.FileSystemUtils
import org.springframework.web.multipart.MultipartFile
import java.io.IOException
import java.net.MalformedURLException
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.security.MessageDigest
import java.util.UUID
import java.util.stream.Stream

@Service
class FileService {
    private val root: Path = Paths.get("uploads")

    init {
        try {
            if (!Files.exists(root)) Files.createDirectory(root)
        } catch (e: IOException) {
            throw RuntimeException("Could not initialize folder for upload!")
        }
    }

    fun save(file: MultipartFile): String {
        val filename = "${UUID.randomUUID()}-${file.originalFilename?.replace(" ","")}"
        try {
            file.originalFilename ?: throw RuntimeException("The file seems to have issue with original filename")
            Files.copy(file.inputStream, this.root.resolve(filename))
        } catch (e: Exception) {
            throw RuntimeException("Could not store the file. Error: " + e.message)
        }
        return this.root.resolve(filename).toString()
    }

    fun load(filename: String?): Resource {
        return try {
            val file: Path = root.resolve(filename)
            val resource: Resource = UrlResource(file.toUri())
            if (resource.exists() || resource.isReadable()) {
                resource
            } else {
                throw RuntimeException("Could not read the file!")
            }
        } catch (e: MalformedURLException) {
            throw RuntimeException("Error: " + e.message)
        }
    }

    fun deleteAll() {
        FileSystemUtils.deleteRecursively(root.toFile())
    }

    fun loadAll(): Stream<Path> {
        return try {
            Files.walk(this.root, 1).filter { path -> !path.equals(this.root) }.map(this.root::relativize)
        } catch (e: IOException) {
            throw RuntimeException("Could not load the files!")
        }
    }
}
