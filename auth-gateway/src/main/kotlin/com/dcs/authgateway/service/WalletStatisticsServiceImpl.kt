package com.dcs.authgateway.service

import com.dcs.authgateway.dto.WalletStatistics
import com.dcs.authgateway.dto.WalletStatisticsResult
import com.dcs.authgateway.dto.networkOperator.WalletType
import com.dcs.authgateway.entity.WalletStatisticsEntity
import com.dcs.authgateway.repository.WalletStatisticsRepository
import org.springframework.stereotype.Service

@Service
class WalletStatisticsServiceImpl(
    private val walletStatisticsRepository: WalletStatisticsRepository
) : WalletStatisticsService {
    override fun updateWalletStatistics(walletType: WalletType, count: Long): WalletStatistics {
        return walletStatisticsRepository.findById(walletType).let { walletStatisticsEntity ->
            val countBefore = walletStatisticsEntity.get().count
            walletStatisticsEntity.get().count = count + countBefore
            walletStatisticsRepository.save(walletStatisticsEntity.get())
        }.toWalletStatistics()
    }

    override fun getWalletStatistics(walletType: WalletType): WalletStatistics {
        return walletStatisticsRepository.getById(walletType).toWalletStatistics()
    }

    override fun getAllWalletStatistics(): WalletStatisticsResult {
        return WalletStatisticsResult(walletStatisticsRepository.findAll().map { it.toWalletStatistics() })
    }

    private fun WalletStatisticsEntity.toWalletStatistics(): WalletStatistics {
        return WalletStatistics(
            walletType = walletType,
            count = count
        )
    }
}