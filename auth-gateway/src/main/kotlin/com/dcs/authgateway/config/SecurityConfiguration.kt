package com.dcs.authgateway.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.authentication.AuthenticationProvider
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.security.web.authentication.logout.LogoutHandler
import org.springframework.web.cors.CorsConfiguration

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
class SecurityConfiguration(
    private val jwtAuthenticationFilter: JwtAuthentication<PERSON>ilter,
    private val authenticationProvider: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private val logoutHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private val jwtAuthenticationEntrypoint: JwtAuthenticationEntrypoint
) {

    @Bean
    fun securityFilterChain(http: HttpSecurity): SecurityFilterChain {
        http
            .cors {
                it.configurationSource { request ->
                    val configuration = CorsConfiguration()
                    configuration.allowCredentials = false
                    configuration.allowedOrigins = listOf("*")
                    configuration.allowedMethods = listOf("*")
                    configuration.allowedHeaders = listOf("*")
                    configuration
                }
            }
//            .cors {
//                it.configurationSource { request ->
//                    val corsConfiguration = CorsConfiguration()
//                    if (request.pathInfo == "/api/v1/fractal-auth/refresh-token/") {
//                        corsConfiguration.applyPermitDefaultValues()
//                    }
//                    corsConfiguration
//                }
//            }
            .csrf { it.disable() }
            .authorizeHttpRequests {
                it.requestMatchers(
                    "/api/v1/wallet-statistics/addresses-statistics",
                    "/swagger-ui/**", "/v3/api-docs/**", "/uploads/**",
                    "/api/v1/healthcheck",
                    "/api/v1/auth/**",
                    "/api/v1/fractal-auth/obtain-token/**",
                    "/api/v1/fractal-auth/registration/",
                    "/api/v1/fractal-auth/login/",
                    "/api/v1/fractal-auth/refresh-token/",
                ).permitAll()
//                    .requestMatchers("/management/**").hasAnyRole(Role.ADMIN.name, Role.MANAGER.name)
//                    .requestMatchers(HttpMethod.GET, "/management/**").hasAnyAuthority(Permission.ADMIN_READ.name, Permission.MANAGER_READ.name)
//                    .requestMatchers(HttpMethod.POST, "/management/**").hasAnyAuthority(Permission.ADMIN_CREATE.name, Permission.MANAGER_CREATE.name)
//                    .requestMatchers(HttpMethod.PUT, "/management/**").hasAnyAuthority(Permission.ADMIN_UPDATE.name, Permission.MANAGER_UPDATE.name)
//                    .requestMatchers(HttpMethod.DELETE, "/management/**").hasAnyAuthority(Permission.ADMIN_DELETE.name, Permission.MANAGER_DELETE.name)
//                    // .requestMatchers("/admin/**").hasRole(Role.ADMIN.name)
//                    // .requestMatchers(HttpMethod.GET, "/admin/**").hasAuthority(Permission.ADMIN_READ.name)
//                    // .requestMatchers(HttpMethod.POST, "/admin/**").hasAuthority(Permission.ADMIN_CREATE.name)
//                    // .requestMatchers(HttpMethod.PUT, "/admin/**").hasAuthority(Permission.ADMIN_UPDATE.name)
//                    // .requestMatchers(HttpMethod.DELETE, "/admin/**").hasAuthority(Permission.ADMIN_DELETE.name)
                    .anyRequest().authenticated()

            }
            .sessionManagement {
                it.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            }
            .exceptionHandling {
                it.authenticationEntryPoint(jwtAuthenticationEntrypoint)
            }
            .authenticationProvider(authenticationProvider)
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter::class.java)
            .logout {
                it.logoutUrl("/auth/logout")
                    .addLogoutHandler(logoutHandler)
                    .logoutSuccessHandler { _, _, _ -> SecurityContextHolder.clearContext() }
            }
        return http.build()
    }
}
