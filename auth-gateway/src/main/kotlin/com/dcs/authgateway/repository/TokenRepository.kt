package com.dcs.authgateway.repository

import com.dcs.authgateway.entity.TokenEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface TokenRepository : JpaRepository<TokenEntity, Long> {

    @Query(
        """
            SELECT t
            FROM TokenEntity t
            INNER JOIN UserEntity u ON t.user.id = u.id
            WHERE u.id = :userId
            AND (t.expired = FALSE OR t.revoked = FALSE)
    """
    )
    fun findAllValidTokensByUserId(userId: Long): List<TokenEntity>

    fun findByToken(token: String): TokenEntity?
}
