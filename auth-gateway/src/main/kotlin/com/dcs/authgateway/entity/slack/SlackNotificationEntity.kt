package com.dcs.authgateway.entity.slack

import com.fasterxml.jackson.annotation.JsonFormat
import java.time.LocalDateTime
import java.util.*
import jakarta.persistence.*

@Entity
@Table(name = "slack_notifications")
class SlackNotificationEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    var id: Long? = null,

    @Column(name = "notification_type")
    @Enumerated(EnumType.STRING)
    var slackNotificationType: SlackNotificationType,

    @Column(name = "created_at")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "text")
    var text: String
) {


}