package com.dcs.authgateway.entity

import com.dcs.authgateway.entity.enum.Role
import java.util.UUID
import jakarta.persistence.*

@Entity
@Table(name = "customer_route", uniqueConstraints = [UniqueConstraint(columnNames = ["username"])])
class CustomerRoute(
    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false)
    var role: Role,

    @Column(name = "customer_id")
    var customerId: Long? = null,

    @Column(nullable = false, unique = true)
    var username: String,

    @Column(name = "full_name")
    var fullName: String? = null,

    @Column(nullable = true)
    var profilePicture: String? = null,

    @Column(nullable = false)
    var hasAcquiredNode: Boolean = false,

    @Column(nullable = true)
    var route: String? = null,

    @Column(name = "uuid", nullable = false, unique = true)
    var uuid: UUID,

    @Column(name = "element_id", unique = true)
    var elementId: String?,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    var id: Long? = null
) {
    override fun toString(): String {
        return "CustomerRoute(role=$role, customerId=$customerId, username='$username', profilePicture=$profilePicture, hasAcquiredNode=$hasAcquiredNode, route=$route, uuid=$uuid, elementId=$elementId, id=$id)"
    }
}
