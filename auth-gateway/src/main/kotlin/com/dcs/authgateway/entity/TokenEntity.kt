package com.dcs.authgateway.entity

import com.dcs.authgateway.entity.enum.TokenType
import jakarta.persistence.*
import java.util.UUID

@Entity
@Table(name = "tokens")
class TokenEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var token: String,

    @Enumerated(EnumType.STRING)
    var tokenType: TokenType,

    var expired: Boolean,
    var revoked: Boolean,

    @ManyToOne
    @JoinColumn(name = "user_id")
    var user: UserEntity
)
