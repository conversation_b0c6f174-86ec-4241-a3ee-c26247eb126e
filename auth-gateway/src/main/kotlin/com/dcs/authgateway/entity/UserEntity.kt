package com.dcs.authgateway.entity

import com.dcs.authgateway.entity.enum.Role
import com.dcs.authgateway.entity.enum.getAuthorities
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.userdetails.UserDetails
import java.util.UUID

@Entity
@Table(name = "users")
class UserEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    private var username: String,
    private var password: String? = null,

    @Enumerated(EnumType.STRING)
    var role: Role,

    @OneToMany(mappedBy = "user")
    var tokens: List<TokenEntity> = emptyList()
) : UserDetails {

    override fun getAuthorities(): MutableCollection<out GrantedAuthority> = role.getAuthorities().toMutableList()

    override fun getPassword(): String? = password

    override fun getUsername(): String = username

    override fun isAccountNonExpired(): Boolean = true

    override fun isAccountNonLocked(): Boolean = true

    override fun isCredentialsNonExpired(): Boolean = true

    override fun isEnabled(): Boolean = true
}