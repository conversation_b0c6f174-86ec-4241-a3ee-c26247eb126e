package com.dcs.authgateway.controller

import com.dcs.authgateway.entity.CodeToUUID
import com.dcs.authgateway.repository.CodeToUUIDRepository
import com.dcs.authgateway.service.CodeToUUIDService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/code")
class CodeToUUIDController(
    private val codeToUUIDRepository: CodeToUUIDRepository,
    private val codeToUUIDService: CodeToUUIDService
) {

    @PostMapping
    fun set(
        @RequestHeader(name = "Authorization") authHeader: String
    ): ResponseEntity<Int> {
        return ResponseEntity.ok(codeToUUIDService.generateCode())
    }

    @GetMapping
    fun get(
        @RequestHeader(name = "Authorization") authHeader: String
    ): ResponseEntity<Iterable<CodeToUUID>> {
        return ResponseEntity.ok(codeToUUIDRepository.findAll())
    }

}