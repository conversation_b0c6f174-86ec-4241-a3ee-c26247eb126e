package com.dcs.authgateway.controller

import com.dcs.authgateway.dto.WalletStatistics
import com.dcs.authgateway.dto.networkOperator.WalletType
import com.dcs.authgateway.service.WalletStatisticsService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/wallet-statistics")
class WalletStatisticsController(
    private val walletStatisticsService: WalletStatisticsService
) {

    @PostMapping("/addresses-statistics")
    fun postNotification(
        @RequestBody walletRequest: WalletStatistics
    ): ResponseEntity<*> {

        if (walletRequest.count <= 0) {
            return ResponseEntity.badRequest().body("Value 'count' must be more than 0")
        }
        val result = walletStatisticsService.updateWalletStatistics(
            walletRequest.walletType, walletRequest.count
        )

        return ResponseEntity.ok(result)
    }

    @GetMapping("/addresses-statistics")
    fun getWalletStatistics(): ResponseEntity<*> {
        val result = walletStatisticsService.getAllWalletStatistics()
        return ResponseEntity.ok(result)
    }

    @GetMapping("/addresses-statistics/{walletType}")
    fun getAddressesStatisticsById(
        @RequestHeader(name = "Authorization") authHeader: String,
        @PathVariable walletType: WalletType,
    ): ResponseEntity<WalletStatistics> {
        return ResponseEntity.ok(
            walletStatisticsService.getWalletStatistics(walletType)
        )
    }
}