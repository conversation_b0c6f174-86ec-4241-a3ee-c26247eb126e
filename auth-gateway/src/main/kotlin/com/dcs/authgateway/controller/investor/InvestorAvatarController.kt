package com.dcs.authgateway.controller.investor

import com.dcs.authgateway.controller.dto.request.AuthenticationRequest
import com.dcs.authgateway.controller.dto.request.RegisterRequest
import com.dcs.authgateway.controller.dto.response.AuthenticationResponse
import com.dcs.authgateway.dto.AvatarResponseDto
import com.dcs.authgateway.dto.AvatarLoginDto
import com.dcs.authgateway.dto.AvatarRenameDto
import com.dcs.authgateway.entity.enum.Role
import com.dcs.authgateway.entity.CustomerRoute
import com.dcs.authgateway.repository.UserRepository
import com.dcs.authgateway.service.*
import com.dcs.authgateway.service.usecases.TurnOnCoSignerService
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.util.*


@RestController
@RequestMapping("/api/v1/investor/avatars/")
class InvestorAvatarController(
    private val customerRouteService: CustomerRouteService,
    private val turnOnCoSignerService: TurnOnCoSignerService,
    private val fileService: FileService,
    private val jwtService: JwtService,
    private val authenticationService: AuthenticationService,
    private val userRepository: UserRepository
) {

    @PostMapping(consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @ResponseBody
    fun createAvatar(
        @RequestParam photo: MultipartFile,
        @RequestParam name: String,
        @RequestParam element_id: String,
        @RequestHeader(name = "Authorization") authHeader: String
    ): ResponseEntity<AvatarResponseDto?>? {
        val jwt = authHeader.substring(7)
        val decodedJwt = jwtService.getClaims(jwt)

        val uuid = decodedJwt["uuid"] as String
        val investor = customerRouteService.findByUuid(UUID.fromString(uuid))

        val path = fileService.save(photo)
        val customerRoute = customerRouteService.save(
            name, path, Role.AVATAR, uuid = UUID.randomUUID(), element_id = element_id, customerId = investor.customerId
        )
        return ResponseEntity(
            customerRouteService.entityToAvatarDto(customerRoute),
            HttpStatus.OK
        )
    }

    @GetMapping
    @ResponseBody
    fun getAvatarList(@RequestHeader(name = "Authorization") authHeader: String): ResponseEntity<List<AvatarResponseDto>> {

        val jwt = authHeader.substring(7)
        val decodedJwt = jwtService.getClaims(jwt)

        val uuid = decodedJwt["uuid"] as String
        val investor = customerRouteService.findByUuid(UUID.fromString(uuid))
        val avatarList = customerRouteService.findAllByCustomerIdAndRole(investor.customerId!!, Role.AVATAR)
        val avatarListDTOs = avatarList.map { customerRouteService.entityToAvatarDto(it) }
        return ResponseEntity(
            avatarListDTOs,
            HttpStatus.OK
        )
    }

    @GetMapping("{id}/")
//    @Throws(ResourceNotFoundException::class)
    fun getAvatar(
        @PathVariable(value = "id") avatarId: Long,
    ): ResponseEntity<AvatarResponseDto?>? {
        val customerRoute = customerRouteService.getCustomerRouteById(avatarId)
        return ResponseEntity(
            customerRouteService.entityToAvatarDto(customerRoute),
            HttpStatus.OK
        )
    }

    @PatchMapping("{id}/")
    fun updateAvatarPartially(
        @PathVariable(value = "id") avatarId: Long,
        @RequestBody avatarRenameDto: AvatarRenameDto
    ): ResponseEntity<AvatarResponseDto?>? {
        val avatar = customerRouteService.getCustomerRouteById(avatarId)
        avatar.username = avatarRenameDto.name
        val customerRoute: CustomerRoute = customerRouteService.update(avatar)!!
        return ResponseEntity(
            customerRouteService.entityToAvatarDto(customerRoute),
            HttpStatus.OK
        )
    }

    @PostMapping("login/")
    fun loginAvatar(
        @RequestBody avatarLoginDto: AvatarLoginDto
    ): ResponseEntity<AuthenticationResponse> {
        val customerRoute = customerRouteService.getCustomerRouteById(avatarLoginDto.id)

        if (userRepository.existsByUsername(customerRoute.username)) {
            val request = AuthenticationRequest(
                username = customerRoute.username,
                password = null
            )
            println(customerRoute.toString())
            val resp = authenticationService.authenticate(customerRoute, request, customerRoute.route)
//            turnOnCoSignerService.notify(customerRoute.uuid.toString())
            return ResponseEntity(resp, HttpStatus.FOUND)
        } else {
            val request = RegisterRequest(
                username = customerRoute.username,
                password = null,
                role = Role.AVATAR
            )
            println(customerRoute.toString())
            val resp = authenticationService.register(customerRoute, request, customerRoute.route)
            turnOnCoSignerService.notify(customerRoute.uuid.toString())
            return ResponseEntity(resp, HttpStatus.FOUND)
        }
    }
}

