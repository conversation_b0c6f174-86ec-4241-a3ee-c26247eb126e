package com.dcs.authgateway.controller

import com.dcs.authgateway.dto.NetworkOperatorStatistics
import com.dcs.authgateway.service.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.util.*


@RestController
@RequestMapping("/api/v1/operator")
class NetworkOperatorController(
    private val customerRouteService: CustomerRouteService
) {
    @Autowired
    private val jwtService: JwtService? = null


    @GetMapping("/statistics")
    @ResponseBody
    fun getStatistics(
        @RequestHeader(name = "Authorization") authHeader: String,
    ): ResponseEntity<NetworkOperatorStatistics> {
        val statistics = customerRouteService.countCustomersByRole()
        return ResponseEntity(
            statistics,
            HttpStatus.OK
        )
    }
}