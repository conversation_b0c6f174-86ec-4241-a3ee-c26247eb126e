package com.dcs.authgateway.controller

import com.dcs.authgateway.dto.AvatarNodeAcquisitionDto
import com.dcs.authgateway.dto.AvatarResponseDto
import com.dcs.authgateway.entity.CustomerRoute
import com.dcs.authgateway.service.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.util.UUID


@RestController
@RequestMapping("/api/v1/adviser")
class AdviserController(
    private val customerRouteService: CustomerRouteService,
    private val jwtService: JwtService
) {



    @PostMapping("/avatars/acquire-node")
    @ResponseBody
    fun getAdviserList(
        @RequestHeader(name = "Authorization") authHeader: String,
        @RequestBody acquisitionDto: AvatarNodeAcquisitionDto
    ): ResponseEntity<AvatarResponseDto> {

        val jwt = authHeader.substring(7)
        val decodedJwt = jwtService.getClaims(jwt)
        val uuid = decodedJwt["uuid"] as String
        val adviser = customerRouteService.findByUuid(UUID.fromString(uuid))

        val avatar = customerRouteService.getByUsername(acquisitionDto.avatarName)
        avatar.hasAcquiredNode = true
        avatar.route = adviser.route
        val customerRoute: CustomerRoute = customerRouteService.update(avatar)!!
        return ResponseEntity(
            customerRouteService.entityToAvatarDto(customerRoute),
            HttpStatus.OK
        )
    }
}