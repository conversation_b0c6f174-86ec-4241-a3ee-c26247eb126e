package com.dcs.authgateway

import com.dcs.authgateway.dto.CustomerRegistrationDto
import com.dcs.authgateway.entity.enum.Role
import com.dcs.authgateway.service.AuthenticationService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.context.annotation.Profile
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.stereotype.Component
@Profile("default")
@Component
class DemoData(
    private val authenticationService: AuthenticationService
) : CommandLineRunner {




    override fun run(vararg args: String?) {
        val bob = CustomerRegistrationDto(
            "bob",
            Role.INVESTOR,
            "123"
        )
        val adviser = CustomerRegistrationDto(
            "fatima_advisor",
            Role.ADVISER,
            "123"
        )
        val operator = CustomerRegistrationDto(
            "operator",
            Role.NETWORK_OPERATOR,
            "123"
        )
        val basicAdviser = CustomerRegistrationDto(
            "Bastion Cooperative",
            Role.ADVISER,
            "test"
        )
        val basicOperator = CustomerRegistrationDto(
            "basic-OPERATOR",
            Role.NETWORK_OPERATOR,
            "test"
        )

//        authenticationService.register()
    }
}
