package com.dcs.authgateway.fractalId.dto

data class Details(
    val date_of_birth: String?,
    val full_name: String?,
    val identification_document_back_file: String?,
    val identification_document_country: String?,
    val identification_document_date_of_expiry: String?,
    val identification_document_date_of_issue: Any?,
    val identification_document_front_file: String?,
    val identification_document_number: String?,
    val identification_document_type: String?,
    val liveness: Boolean?,
    val liveness_audit_best_file: String?,
    val liveness_audit_least_similar_file: String?,
    val liveness_audit_open_eyes_file: String?,
    val liveness_audit_quality1_file: String?,
    val liveness_audit_quality2_file: String?,
    val liveness_audit_quality3_file: String?,
    val mrz: Mrz?,
    val residential_address_country: String?
)