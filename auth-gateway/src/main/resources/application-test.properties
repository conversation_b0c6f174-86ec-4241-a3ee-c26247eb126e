spring.datasource.url=jdbc:postgresql://${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
spring.datasource.username=${POSTGRES_USER}
spring.datasource.password=${POSTGRES_PASSWORD}

spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=update

spring.liquibase.enabled=false

jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
urls.redirectUrl=${POLITY_HOST}
urls.authGatewayUrl=${AUTH_HOST:https://mvp-stg.polity.network}