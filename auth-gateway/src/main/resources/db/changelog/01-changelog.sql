CREATE TABLE customer
(
    id                  BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    password            VARCHAR(255) NOT NULL
);


CREATE TABLE customer_route
(
    id                  BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    has_acquired_node   BOOLEAN NOT NULL,
    uuid                UUID NOT NULL CONSTRAINT uk_1wv204x2mhlmri5um26jw9ukc UNIQUE,
    profile_picture     VARCHAR(255),
    role                VARCHAR(255) NOT NULL,
    route               VARCHAR(255),
    username            VARCHAR(255) NOT NULL CONSTRAINT uk_m3tn4tn6xgln3vfjmu9vm0053 UNIQUE,
    customer_id         BIGINT CONSTRAINT fkmbjkpe9gs2r4i49wt5d9nh1ry REFERENCES customer
);

CREATE TABLE refresh_token
(
    id                  BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    expiry_date         TIMESTAMP NOT NULL,
    token               VARCHAR(255) NOT NULL CONSTRAINT uk_r4k4edos30bx9neoq81mdvwph UNIQUE,
    customer_route_id   BIGINT CONSTRAINT fkdr852kxjno49agfuvapql0kdi REFERENCES customer_route
);

CREATE TABLE wallet_statistics
(
    wallet_type         VARCHAR(255) NOT NULL PRIMARY KEY,
    count               BIGINT
);

INSERT INTO wallet_statistics (wallet_type, count) VALUES ('DFNS', 0);
INSERT INTO wallet_statistics (wallet_type, count) VALUES ('SAFEHERON', 0);