ALTER TABLE customer_route DROP CONSTRAINT fkmbjkpe9gs2r4i49wt5d9nh1ry;

DROP TABLE customer;

create table if not exists users(
    id serial primary key,
    username varchar(120),
    password varchar(256),
    role varchar(24)
    );

create table if not exists tokens(
    id serial primary key,
    user_id bigint,
    token varchar(1024),
    role varchar(24),
    expired bool,
    revoked bool,

    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id)
);
