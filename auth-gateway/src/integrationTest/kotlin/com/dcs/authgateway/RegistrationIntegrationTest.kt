//package com.dcs.authgateway
//
//import com.dcs.authgateway.dto.CustomerRegistrationDto
//import com.dcs.authgateway.entity.enum.Role
//import com.dcs.authgateway.service.CustomerRouteService
//import com.dcs.authgateway.service.CustomerService
//import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
//import net.sf.jsqlparser.util.validation.metadata.DatabaseException
//import org.junit.jupiter.api.Assertions.assertEquals
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.boot.test.web.client.TestRestTemplate
//import org.springframework.http.HttpEntity
//import org.springframework.http.HttpHeaders
//import org.springframework.http.HttpStatus
//import org.springframework.http.MediaType
//import org.springframework.jdbc.core.JdbcTemplate
//import org.springframework.test.context.ActiveProfiles
//import org.springframework.test.jdbc.JdbcTestUtils
////import org.springframework.transaction.annotation.Transactional
//
//
//
//@ActiveProfiles("test")
//@SpringBootTest(
//    classes = arrayOf(AuthGatewayApplication::class),
//    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//class RegistrationIntegrationTest(
//    private val customerRouteService: CustomerRouteService
//) {
//
//    @Autowired
//    lateinit var restTemplate: TestRestTemplate
//
//    @Autowired
//    lateinit var jdbcTemplate: JdbcTemplate
//
//    val mapper = jacksonObjectMapper()
//    val customerDto = CustomerRegistrationDto("JOHN SMITH", Role.ADVISER, "123ING456")
//
//    @BeforeEach
//    @Throws(DatabaseException::class)
//    fun tearDown() {
//        val res = JdbcTestUtils.deleteFromTables(jdbcTemplate, "refresh_token", "customer_route", "customer")
//    }
//    @Test
//    fun whenRegistrated_thenShouldReturnStatus200AndPersistCustomer() {
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_JSON
//        val request = HttpEntity(mapper.writeValueAsString(customerDto), headers)
//        val result = restTemplate.postForEntity("/api/v1/auth/registration", request, HashMap::class.java)
//        assertEquals(HttpStatus.OK, result?.statusCode)
//        val persistedCustomerProfile = customerRouteService.getByUsername(customerDto.username)
//        assertEquals(persistedCustomerProfile.username, customerDto.username)
//        val persistedCustomer = customerRouteService.getByUsername(customerDto.username)
//        val routes = persistedCustomer.routes
//        if (routes != null) {
//            assert(routes.size == 1)
//        }
//    }
//}