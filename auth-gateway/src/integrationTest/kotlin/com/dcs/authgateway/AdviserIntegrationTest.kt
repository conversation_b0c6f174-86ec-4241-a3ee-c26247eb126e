//package com.dcs.authgateway
//
//import com.dcs.authgateway.dto.AvatarNodeAcquisitionDto
//import com.dcs.authgateway.dto.CustomerRegistrationDto
//import com.dcs.authgateway.entity.enum.Role
//import com.dcs.authgateway.service.CustomerRouteService
//import com.dcs.authgateway.service.CustomerService
//import com.dcs.authgateway.service.JwtService
//import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
//import net.sf.jsqlparser.util.validation.metadata.DatabaseException
//import org.junit.jupiter.api.Assertions
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.boot.test.web.client.TestRestTemplate
//import org.springframework.http.HttpEntity
//import org.springframework.http.HttpHeaders
//import org.springframework.http.HttpStatus
//import org.springframework.http.MediaType
//import org.springframework.jdbc.core.JdbcTemplate
//import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
//import org.springframework.test.context.ActiveProfiles
//import org.springframework.test.jdbc.JdbcTestUtils
//
//
//@ActiveProfiles("test")
//@SpringBootTest(
//    classes = arrayOf(AuthGatewayApplication::class),
//    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
//)
//class AdviserIntegrationTest {
//    @Autowired
//    lateinit var customerRouteService: CustomerRouteService
//
//    @Autowired
//    lateinit var passwordEncoder: BCryptPasswordEncoder
//
//    @Autowired
//    lateinit var restTemplate: TestRestTemplate
//
//    @Autowired
//    lateinit var customerService: CustomerService
//
//    @Autowired
//    lateinit var jwtService: JwtService
//
//    @Autowired
//    lateinit var jdbcTemplate: JdbcTemplate
//
//    val mapper = jacksonObjectMapper()
//    val acquireNodeRequestDto = AvatarNodeAcquisitionDto("AvatarA")
//    val investorDto = CustomerRegistrationDto(
//        "bob",
//        Role.INVESTOR,
//        "123"
//    )
//    val adviserDto = CustomerRegistrationDto(
//        "alice",
//        Role.ADVISER,
//        "123"
//    )
//
//    @BeforeEach
//    @Throws(DatabaseException::class)
//    fun tearDown() {
//        val res = JdbcTestUtils.deleteFromTables(jdbcTemplate, "refresh_token", "customer_route", "customer")
//    }
//
//    @Test
//    fun whenRegistrated_thenShouldReturnStatus200AndPersistCustomer() {
//        val headers = HttpHeaders()
//        val investor = customerService.save(investorDto, passwordEncoder)
//        val adviser = customerService.save(adviserDto, passwordEncoder)
//
//        val avatar = customerRouteService.save(acquireNodeRequestDto.avatarName, "path", investor!!, Role.AVATAR, element_id = "")
//
//        val jwtToken = jwtService.generateToken(customerRouteService.findCustomerRoutesByRole(adviserDto.role).first())
//        headers.set("Authorization", "Bearer $jwtToken")
//        headers.contentType = MediaType.APPLICATION_JSON
//
//        val request = HttpEntity(mapper.writeValueAsString(acquireNodeRequestDto), headers)
//        val result = restTemplate.postForEntity("/api/adviser/avatars/acquire-node", request, HashMap::class.java)
//        Assertions.assertEquals(HttpStatus.OK, result?.statusCode)
//        val updatedAvatar = customerService.loadProfileByUsername(avatar.username)
//        Assertions.assertEquals(true, updatedAvatar.hasAcquiredNode)
//    }
//}
