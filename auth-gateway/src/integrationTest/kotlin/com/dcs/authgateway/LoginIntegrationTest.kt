//package com.dcs.authgateway
//
//import com.dcs.authgateway.dto.CustomerRegistrationDto
//import com.dcs.authgateway.entity.enum.Role
//import com.dcs.authgateway.payload.response.LoginResponse
//import com.dcs.authgateway.service.CustomerService
//import com.dcs.authgateway.service.JwtService
//import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
//import net.sf.jsqlparser.util.validation.metadata.DatabaseException
//import org.junit.jupiter.api.Assertions
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.boot.test.web.client.TestRestTemplate
//import org.springframework.http.HttpEntity
//import org.springframework.http.HttpHeaders
//import org.springframework.http.HttpStatus
//import org.springframework.http.MediaType
//import org.springframework.jdbc.core.JdbcTemplate
//import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
//import org.springframework.test.context.ActiveProfiles
//import org.springframework.test.jdbc.JdbcTestUtils
//
//
//@ActiveProfiles("test")
//@SpringBootTest(
//    classes = arrayOf(AuthGatewayApplication::class),
//    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
//)
//class LoginIntegrationTest {
//
//    @Autowired
//    lateinit var restTemplate: TestRestTemplate
//
//    @Autowired
//    lateinit var customerService: CustomerService
//
//    @Autowired
//    lateinit var passwordEncoder: BCryptPasswordEncoder
//
//    @Autowired
//    lateinit var jdbcTemplate: JdbcTemplate
//
//
//    @Autowired
//    lateinit var jwtService: JwtService
//
//    val mapper = jacksonObjectMapper()
//    val customerDto = CustomerRegistrationDto("JOHN SMITH", Role.ADVISER, "123ING456")
////    val customer = Customer(customerDto.username, customerDto.password)
//
//    @BeforeEach
//    @Throws(DatabaseException::class)
//    fun tearDown() {
//        val res = JdbcTestUtils.deleteFromTables(jdbcTemplate, "refresh_token", "customer_route", "customer")
//    }
//
//    @Test
//    fun whenRegistrated_thenShouldReturnStatus200AndPersistCustomer() {
//        customerService.save(customerDto, passwordEncoder)
//        val persistedCustomer = customerService.loadProfileByUsername(customerDto.username)
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_JSON
//        val request = HttpEntity(mapper.writeValueAsString(customerDto), headers)
//        val result = restTemplate.postForEntity("/api/v1/auth/login", request, LoginResponse::class.java)
//        val deserializedResult = result.body!!
//        Assertions.assertEquals(HttpStatus.FOUND, result?.statusCode)
//        val decodedJwt = jwtService.getAllClaimsFromToken(deserializedResult.access_token)
//        print(deserializedResult.access_token)
////        Assertions.assertEquals(decodedJwt.subject, persistedCustomer!!.id.toString())
//        Assertions.assertEquals(decodedJwt["role"], customerDto.role.toString())
//        Assertions.assertEquals(decodedJwt["username"], persistedCustomer.username)
//        assert(refreshTokenService.existsByToken(deserializedResult.refresh_token))
//        Assertions.assertEquals(customerService.redirectUrl, deserializedResult.redirect_url)
//    }
//}