//package com.dcs.authgateway
//
//import com.dcs.authgateway.dto.CustomerRegistrationDto
//import com.dcs.authgateway.dto.NetworkOperatorStatistics
//import com.dcs.authgateway.entity.enum.Role
//import com.dcs.authgateway.service.CustomerRouteService
//import com.dcs.authgateway.service.CustomerService
//import com.dcs.authgateway.service.JwtService
//import net.sf.jsqlparser.util.validation.metadata.DatabaseException
//import org.junit.jupiter.api.Assertions
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.boot.test.web.client.TestRestTemplate
//import org.springframework.http.*
//import org.springframework.jdbc.core.JdbcTemplate
//import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
//import org.springframework.test.context.ActiveProfiles
//import org.springframework.test.jdbc.JdbcTestUtils
//
//@ActiveProfiles("test")
//@SpringBootTest(
//    classes = arrayOf(AuthGatewayApplication::class),
//    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
//)
//class NetworkOperatorIntegrationTest {
//
//    @Autowired
//    lateinit var restTemplate: TestRestTemplate
//
//    @Autowired
//    lateinit var customerRouteService: CustomerRouteService
//
//    @Autowired
//    lateinit var customerService: CustomerService
//
//    @Autowired
//    lateinit var passwordEncoder: BCryptPasswordEncoder
//
//    @Autowired
//    lateinit var jdbcTemplate: JdbcTemplate
//
//    @Autowired
//    lateinit var jwtService: JwtService
//
//    @BeforeEach
//    @Throws(DatabaseException::class)
//    fun tearDown() {
//        val res = JdbcTestUtils.deleteFromTables(jdbcTemplate, "refresh_token", "customer_route", "customer")
//    }
//
//    @Test
//    fun whenGetStatisticsWithPersistedCustomer_thenShouldReturnStatus200AndProperResponse() {
//        val operatorDto = CustomerRegistrationDto(
//            "operator",
//            Role.NETWORK_OPERATOR,
//            "123"
//        )
//        val investorDto = CustomerRegistrationDto(
//            "investor",
//            Role.INVESTOR,
//            "123"
//        )
//        val adviserDto = CustomerRegistrationDto(
//            "adviser",
//            Role.ADVISER,
//            "123"
//        )
//        val investor = customerService.save(investorDto, passwordEncoder)
//        customerService.save(adviserDto, passwordEncoder)
//        val adviser = customerRouteService.findCustomerRoutesByRole(Role.ADVISER).first()
//        customerService.save(operatorDto, passwordEncoder)
//        customerRouteService.save("avatar1", "path", investor!!, Role.AVATAR, element_id = "element_id")
//        customerRouteService.save(
//            "avatar2",
//            "path",
//            investor,
//            Role.AVATAR,
//            hasAcquiredNode = true,
//            redirectUrl = adviser.route,
//            element_id = "element_id"
//        )
//
//        val operatorProfile = customerService.loadProfileByUsername(operatorDto.username)
//        val headers = HttpHeaders()
//        headers.contentType = MediaType.APPLICATION_JSON
//        val jwtToken = jwtService.generateToken(operatorProfile)
//
//        headers.set("Authorization", "Bearer $jwtToken")
//
//        val request = HttpEntity<Void>(headers)
//        val result = restTemplate.exchange(
//            "/api/operator/statistics", HttpMethod.GET, request, NetworkOperatorStatistics::class.java
//        )
//        val body = result.body ?: Assertions.fail("Body of request is null")
//        Assertions.assertEquals(HttpStatus.OK, result.statusCode)
//        Assertions.assertEquals(
//            NetworkOperatorStatistics(
//                investorsCount = 1,
//                avatarsCount = 2,
//                adviserCount = 1,
//                avatarsWithNodes = 1,
//                avatarPerAdviser = mapOf(
//                    adviser.username to 1
//                )
//            ),
//            body
//        )
//
//    }
//}

