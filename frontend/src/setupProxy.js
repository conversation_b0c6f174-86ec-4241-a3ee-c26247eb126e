const { createProxyMiddleware } = require('http-proxy-middleware')

module.exports = app => {
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*')
    next()
  })

  const { API_PROXY_TARGET, BWI_API_PROXY_TARGET, BIDDING_API_PROXY_TARGET } =
    process.env

  if (BWI_API_PROXY_TARGET) {
    // Separate host for this API set
    app.use(
      ['/bwi/api'],
      createProxyMiddleware({
        target: BWI_API_PROXY_TARGET,
        changeOrigin: true,
        pathRewrite: {
          '^/bwi/api': '/api',
        },
        followRedirects: true,
      })
    )
  }

  if (BIDDING_API_PROXY_TARGET) {
    // Separate host for this API set
    app.use(
      ['/bidding/api'],
      createProxyMiddleware({
        target: BIDDING_API_PROXY_TARGET,
        changeOrigin: true,
        pathRewrite: {
          '^/bidding/api': '/api',
        },
        followRedirects: true,
      })
    )
  }

  if (API_PROXY_TARGET) {
    app.use(
      ['/api', '/uploads'],
      createProxyMiddleware({
        target: API_PROXY_TARGET,
        changeOrigin: true,
        followRedirects: true,
      })
    )
  }
}
