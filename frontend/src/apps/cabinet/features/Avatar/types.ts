import { AgreementStatus, AsyncState, IAgreement } from 'src/types'

export interface IAvatarState extends AsyncState {}

export interface IAcceptAgreementPayload {
  agreementId: IAgreement['id']
}

export interface IDeclineAgreementPayload {
  agreementId: IAgreement['id']
  declineReason: string
}

export interface IResolveAgreementResponse {
  agreementId: IAgreement['id']
  status: AgreementStatus
}
