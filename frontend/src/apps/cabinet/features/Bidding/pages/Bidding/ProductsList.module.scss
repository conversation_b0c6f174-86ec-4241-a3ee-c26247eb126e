.product {
  grid-template-rows: 1fr auto;

  border-radius: 20px;
  /* TODO: copied from Figma; why not a simple border instead? */
  box-shadow: 0 0 0 2px #f2f4f7, 0 0 2px 0 rgb(16 24 40 / 6%), 0 0 3px 0 rgb(16 24 40 / 10%);
  padding: 24px;

  width: 292px;
  height: 480px;
}

.product_main {
  grid-auto-rows: max-content;
}

.product_header {
  /* none */
}

.product_title {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.33;
}

.product_desc {
  font-size: 0.875rem;
  color: var(--th-clr-txt-hint);
  line-height: 1.43;
}

.product_details {
  /* none */
}

.product_footer {
  /* none */
}

/* TODO: this should be available out of the box as <Button size="sm" /> */
.btn_view_products {
  padding: 6px 12px;
  border-radius: 9px;
  font-size: 0.875rem;
}

.what_you_get {
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.product_feature {
  font-size: 0.875rem;
  color: var(--th-clr-txt-hint);
  line-height: 1.43;
}

.product_feature_check {
  --icon-clr-bg: var(--th-clr-success-100);
  color: var(--th-clr-success-500);
}
