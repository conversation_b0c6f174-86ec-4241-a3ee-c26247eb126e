.root {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  flex-basis: 290px;
  flex-shrink: 0;
  padding: 24px;
  border-radius: 20px;
  border: 2px solid var(--th-clr-bg-primary);
  background: var(--th-clr-bg-secondary) center center;
  background-size: cover;
  box-shadow: 0 0 0 2px var(--th-clr-gray-100), 0 1px 2px 0 rgb(16 24 40 / 6%), 0 1px 3px 0 rgb(16 24 40 / 10%);

  .wallet_name {
    font-size: 24px;
    font-weight: 500;
    line-height: 32px;
  }

  .logo {
    height: 24px;
  }
}

.main {
  display: flex;
  flex-direction: column;
  gap: 130px;
  justify-content: space-between;
  flex-basis: 490px;
  padding: 0 20px;
  font-size: 14px;
  line-height: 20px;

  .provider_name {
    margin-bottom: 2px;
    font-size: 20px;
    font-weight: 500;
    line-height: 30px;
  }

  .features_header {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
  }

  .description {
    margin-bottom: 16px;
  }
}
