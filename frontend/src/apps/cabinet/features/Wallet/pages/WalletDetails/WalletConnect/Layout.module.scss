.root {
  display: grid;
  gap: 1rem;
  justify-content: stretch;
  align-items: center;

  border: 1px solid var(--th-border-color);
  border-radius: 20px;
  padding: 20px;
  width: 320px;

  background-color: var(--th-clr-bg-primary);
}

.header {
  display: grid;
  justify-items: center;
  position: relative;
}

.info {
  display: grid;
  gap: 0.5em;
  justify-items: center;
  text-align: center;
}

.title {
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 1.55;
  overflow: hidden;
  max-width: 100%;
}

.desc {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.43;
}

.header,
.title,
.desc {
  &:empty {
    display: none;
  }
}
