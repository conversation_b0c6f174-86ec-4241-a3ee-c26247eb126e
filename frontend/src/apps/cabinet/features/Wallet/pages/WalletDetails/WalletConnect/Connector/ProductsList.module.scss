@value list-gap: 0.5rem;

.list {
  display: grid;
  gap: #{list-gap};
  width: 300px;
  padding: 0.5rem;
}

.item {
  display: grid;
  gap: 0.5rem;
  grid-template-columns: max-content auto;

  & + & {
    border-top: 1px solid var(--th-border-color);
    padding-top: #{list-gap};
  }
}

.product_name {
  display: flex;
  justify-content: flex-start;
  font-size: 1.25em;
  width: fit-content;
}

.product_icon {
  --size: 2em;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
}

.tooltip_content {
  z-index: calc(var(--modal-overlay-zindex) + 1);
}

.tooltip_anchor {
  font-weight: bold;
  cursor: pointer;
  color: var(--th-clr-info);

  display: inline-flex;
  gap: 0.25em;
  align-items: center;
}
