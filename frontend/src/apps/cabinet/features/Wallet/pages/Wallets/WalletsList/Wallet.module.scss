.root {
  --pad-inline: 2rem;

  border: 1px solid var(--th-border-color);
  border-radius: 24px;
  overflow: hidden;
  background-color: var(--th-clr-white);

  height: 330px;
  max-width: var(--wallet-width, 476px);

  display: grid;
  grid-template-rows: 1fr 68px;
}

.main {
  padding-inline: var(--pad-inline);
  padding-block: 2rem;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.menu {
  display: grid;
  gap: 0.5rem;
  align-self: flex-start;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  padding-inline: var(--pad-inline);
  padding-block: 1rem;

  border-top: 1px solid var(--th-border-color);
}

.title {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 2rem;
  color: var(--th-clr-txt-primary);

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.balance {
  font-size: 1.125rem;
  line-height: 1.75rem;
  color: var(--th-clr-txt-secondary);
  margin-top: 4px;
}

.balance_error {
  font-size: inherit;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.assets {
  margin-top: auto;
  display: grid;
  gap: 4px;
}

.assets_title {
  color: var(--th-clr-txt-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 20px;
}

.not_available_placeholder {
  color: var(--th-clr-txt-placeholder);
  font-size: 0.875rem;
  font-style: italic;
}

.provider {
  display: flex;
}
