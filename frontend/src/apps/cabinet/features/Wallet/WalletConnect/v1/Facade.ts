import { uniqueId } from 'lodash'

import { extendLogger } from 'src/log'
import { isSupportedBlockchainMethod } from 'src/utils'

import { WCLogger } from '../Logger'
import {
  IConnectorOptions,
  IWCFacade,
  IWCFacadeState,
  IWCSessionRequest,
  WCClientUpdateListener,
} from '../types'

import { WalletConnectEnhanced as WalletConnect_V1 } from './Enhancer'
import { IWCLocalState, WCEventName } from './types'

export class WCFacade_V1 implements IWCFacade {
  readonly id = uniqueId('wc@1_') // just for debugging
  protected readonly logger = extendLogger(this.id, WCLogger.log)

  protected readonly wc: WalletConnect_V1
  protected _onUpdateListeners: WCClientUpdateListener[] = []

  constructor(opts: IConnectorOptions) {
    const wc = new WalletConnect_V1({
      /**
       * Always set bridge to allow create client without uri specified.
       * Because for some reason lib requires to provide either bridge or url or session.
       */
      bridge: 'https://bridge.walletconnect.org',
      ...opts,
    })
    this.setupListeners(wc)
    this.wc = wc
    this.logger.log('Instance V1 created', wc)
  }

  get account() {
    return this.wc.accounts[0]
  }

  get connected() {
    return this.wc.connected
  }

  get chainId() {
    return this.wc.chainId.toString()
  }

  get peerMeta() {
    return this.wc.peerMeta ?? undefined
  }

  /**
   * TODO:
   *  probably should review state cleanup logic
   *  logic of resetting active asset and requests probably is incomplete;
   *  also it emits multiple updates for atomic operations
   */
  get state() {
    const { state } = this.wc
    const { callRequest, sessionRequest, assetSymbol = null } = state
    return {
      assetSymbol,
      callRequest: resolveCallRequestState(callRequest),
      sessionRequest: resolveSessionRequestState(sessionRequest),
    }
  }

  async approveSession(...args: Parameters<IWCFacade['approveSession']>) {
    this.logger.log('approveSession', ...args)
    const [{ accounts, chains, asset }] = args
    // assume that here we deal only with WC@1 chains. Which are numbers.
    const chainId = +chains[0]
    const { wc } = this
    wc.approveSession({ chainId, accounts })
    wc.setConnectedAsset(asset)
    wc.setActiveSessionRequest(undefined)
  }

  async updateSession(...args: Parameters<IWCFacade['updateSession']>) {
    this.logger.log('updateSession', ...args)
    const [{ chainId }] = args
    const { wc } = this
    wc.updateSession({ chainId: +chainId, accounts: wc.accounts })
    // TODO:
    //  This is indirect relation
    //  Assumed that we update session only in response on `switchChain` call request
    //  For now (private MVP stage, 16.06.23) there are no other scenarios.
    wc.setActiveCallRequest(undefined)
  }

  async rejectSession(...args: Parameters<IWCFacade['rejectSession']>) {
    this.logger.log('rejectSession')
    const { wc } = this
    const [{ reason }] = args
    wc.rejectSession({ message: reason })
  }

  async disconnect(...args: Parameters<IWCFacade['disconnect']>) {
    this.logger.log('killSession')
    const { wc } = this
    const [reason] = args
    wc.killSession({ message: reason })
  }

  async approveRequest(...args: Parameters<IWCFacade['approveRequest']>) {
    this.logger.log('approveRequest', ...args)
    const [{ id, result }] = args
    const { wc } = this
    wc.approveRequest({ id, result })
    wc.setActiveCallRequest(undefined)
  }

  async rejectRequest(...args: Parameters<IWCFacade['rejectRequest']>) {
    this.logger.log('rejectRequest', ...args)
    const [{ id, msg }] = args
    const { wc } = this
    wc.rejectRequest({ id, error: { message: msg } })
    wc.setActiveCallRequest(undefined)
  }

  teardown() {
    const { wc } = this
    const events: WCEventName[] = [
      'connect',
      'disconnect',
      'session_request',
      'session_update',
      'call_request',
      'state_change',
    ]
    events.forEach(x => wc?.off(x))
  }

  awaitSessionRequest(...args: Parameters<IWCFacade['awaitSessionRequest']>) {
    const [uri] = args
    const { wc } = this

    if (uri !== undefined) {
      this.setURI(uri)
    }

    return new Promise<IWCSessionRequest>(resolve => {
      wc.sub('session_request', request => {
        this.logger.log('EVENT:session_request', request)
        wc.setActiveSessionRequest(request)
        const {
          id,
          params: [{ chainId, peerMeta }],
        } = request
        resolve({
          id,
          chainId: chainId.toString(),
          peerMeta,
          chains: [chainId.toString()],
          methods: [],
          events: [],
        })
      })
    })
  }

  async setURI(uri: string) {
    this.wc.uri = uri
  }

  onUpdate(cb: WCClientUpdateListener) {
    this._onUpdateListeners.push(cb)
  }

  protected setupListeners(wc: WalletConnect_V1) {
    const onUpdate = () => this._onUpdateListeners.forEach(fn => fn())

    wc.on('connect', (...args) => {
      this.logger.log('EVENT:connect', ...args)
      onUpdate()
    })

    wc.on('disconnect', (...args) => {
      this.logger.log('EVENT:disconnect', ...args)
      onUpdate()
    })

    wc.on('session_update', (...args) => {
      this.logger.log('EVENT:session_update', ...args)
      onUpdate()
    })

    wc.on('state_change', (...args) => {
      this.logger.log('EVENT:state_change', ...args)
      onUpdate()
    })

    wc.sub('call_request', request => {
      // TODO: PLT-403:
      //  maybe, separate handler for 'wallet_switchEthereumChain' method
      //  to auto-accept it

      this.logger.log('EVENT:call_request', request)

      if (!isSupportedBlockchainMethod(request.method)) {
        this.logger.log('Request method is not supported')
        return
      }

      wc.setActiveCallRequest(request)
    })
  }
}

// ---

function resolveCallRequestState(
  req: IWCLocalState['callRequest']
): IWCFacadeState['callRequest'] {
  if (req === undefined || req === null) return null
  const {
    id,
    method,
    params: [params],
  } = req

  return {
    id,
    method,
    params,
    // only for compatibility with WC@2
    topic: '',
  } as IWCFacadeState['callRequest']
}

function resolveSessionRequestState(
  req: IWCLocalState['sessionRequest']
): IWCFacadeState['sessionRequest'] {
  if (req === undefined || req === null) return null
  const {
    id,
    params: [{ chainId, peerMeta }],
  } = req
  return {
    id,
    chainId: chainId.toString(),
    peerMeta,
    // These are only for compatibility with WC@2
    chains: [chainId.toString()],
    methods: [],
    events: [],
  }
}
