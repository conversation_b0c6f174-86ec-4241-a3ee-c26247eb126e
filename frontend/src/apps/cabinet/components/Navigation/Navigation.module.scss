.root {
  /* none */
}

.items {
  list-style: none;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.item {
  font-weight: 600;

  border-radius: 12px;

  background-color: transparent;
  transition: background-color 300ms;

  display: flex;
  align-items: center;

  &:hover {
    background-color: var(--th-clr-gray-100);
  }

  &:active,
  // classname is to be passed to react-router's NavLink
  &:has(.item_active) {
    background-color: var(--th-clr-gray-200);
  }

  > a {
    color: inherit;
    flex-grow: 1;

    display: flex;
    align-items: center;
    text-align: center;
    gap: 0.75rem;
    padding: 0.5rem 0.75rem;
  }
}

.item_icon {
  color: var(--th-clr-primary-100);
  display: flex;

  &:empty {
    display: none;
  }
}
