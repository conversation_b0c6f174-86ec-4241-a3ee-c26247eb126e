.root {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: auto 1fr auto;

  height: 60px;
  border-bottom: 1px solid var(--th-border-color);
  padding-inline: 1.5rem;
  align-items: center;
}

.content {
  /* none */
}

.logo {
  color: var(--th-clr-txt-primary);
  --logo-clr-icon: var(--th-clr-primary);

  height: 44px;
}

.user {
  display: flex;
  gap: 16px;
  margin-left: auto;

  &:empty {
    display: none;
  }
}
