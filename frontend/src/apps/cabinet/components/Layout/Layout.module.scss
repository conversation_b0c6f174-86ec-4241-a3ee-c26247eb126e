@value page, page_header, page_header_sticky from "src/components/layouts/Page/Page.module.scss";

.root {
  --layout-clr-bg: var(--th-clr-bg-body);
  --layout-pad-block: 2rem;

  display: grid;
  grid-template-rows: auto 1fr;
  background-color: var(--layout-clr-bg);

  height: 100vh;
  min-width: 320px;
  margin-inline: auto;
  overflow: hidden;
}

.main {
  padding-block: var(--layout-pad-block);
  padding-inline: 5rem;
  overflow: auto;

  &:has(.page_header_sticky) {
    padding-block-start: 0;

    .page {
      // Gap which won't "collapse" when header sticks to top
      --header-min-pad: calc(var(--layout-pad-block) / 4);
      // The rest of gap required by design add via margin – which will "collapse" when sticking
      --header-margin-block:
        calc(
          var(--layout-pad-block) - var(--header-min-pad)
        );

      --page-sticky-scroll-margin: calc(2 * var(--header-margin-block));

      .page_header {
        padding-block: var(--header-min-pad);
        margin-block: var(--header-margin-block);
        // So that scrolled content is under the header:
        background-color: var(--layout-clr-bg);
      }
    }
  }
}
