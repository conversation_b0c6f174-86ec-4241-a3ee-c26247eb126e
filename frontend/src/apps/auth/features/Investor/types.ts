import { AsyncState } from 'src/types'

export interface IInvestorState extends AsyncState {}

export interface IInvestorAvatarRaw {
  id: number
  username: string
  photo: string | null
  // Whether this avatar has a blockchain node assigned:
  // (i.e. can access his personal cabinet)
  hasAcquiredNode: boolean
  element_id: string
}

export interface IInvestorAvatar {
  id: number
  name: string
  photo: string | null
  is_active: boolean
  element_id: string
}

export interface IInvestorAdvisor {
  id: number
  name: string
  photo: string | null
  element_id: string
}

export interface ICreateAvatarFormFields {
  name: string
  photo: File
  element_id: string
}

export interface IEditAvatarFormFields {
  id: IInvestorAvatar['id']
  name: string
}
