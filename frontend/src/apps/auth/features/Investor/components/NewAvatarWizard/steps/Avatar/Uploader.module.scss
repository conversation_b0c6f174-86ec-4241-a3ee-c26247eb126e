@import "src/styles/mixins";

.uploader {
  border: 1px solid var(--th-border-color);
  border-radius: 16px;
  padding: 24px;
  width: 160px;
  aspect-ratio: 1;

  position: relative;

  label {
    @include m-flex-center($inline: true);

    width: 100%;
    height: 100%;
    text-align: center;
  }
}

.uploader_icon_holder {
  --size: 48px;
  width: var(--size);
  height: var(--size);

  border: 1px solid var(--th-border-color);
  border-radius: 50%;
  padding: 12px;

  @include m-flex-center;
}

.uploader_icon {
  color: var(--th-clr-gray-600);
}

.uploader_placeholder {
  display: grid;
  justify-items: center;
  gap: 12px;
}

.uploader_text_primary {
  color: var(--th-clr-primary);
  font-weight: 600;
  font-size: 14px;
  line-height: 1.43;
}

.uploader_text_secondary {
  color: var(--th-clr-txt-secondary);
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
}

.uploader_file_preview {
  @include m-flex-center;

  cursor: pointer;
  background-color: var(--th-clr-bg-primary);

  position: absolute;
  inset: 0;

  > img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }

  // ---
  // overlays

  &::before,
  &::after {
    position: absolute;
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover {
    &::before,
    &::after {
      opacity: var(--opacity-max, 1);
    }
  }

  &::before {
    content: "";
    z-index: 2;
    background-color: var(--th-clr-gray-900);
    inset: 0;
    --opacity-max: 0.5;
  }

  &::after {
    // if want to customize svg color, unfortunately will have to insert it into html instead of this
    content: url("~src/components/base/Icon/icons/upload.svg");
    z-index: 3;
    --size: 40%;
    width: var(--size);
    height: var(--size);
    --opacity-max: 1;
  }
}

/*
 * @link https://dcspoc.atlassian.net/browse/PLT-582
 * This style _implicitly_ relies on following:
   - control has a `row` layout
   - field label in vertically centered,
   - there is no hint text under label.
  */
.error_msg_extend {
  position: absolute;
  top: 50%;
  transform: translateY(50%);
}
