@import "src/styles/mixins";

.step {
  display: grid;
  gap: 8px;
}

.img {
  border: var(--card-border);
  border-radius: 12px;
  aspect-ratio: 1;

  @include m-flex-center;
}

.main {
  display: grid;
  gap: 16px;
  grid-template-columns: 138px 1fr;

  border: var(--card-border);
  border-radius: 16px;

  padding: 24px;
}

.header {
  /* none */
}

.content {
  display: grid;
  gap: 16px;
  justify-items: start;
}

.title {
  font-weight: 500;
  font-size: 18px;
  line-height: 28px;
  color: var(--th-clr-txt-primary);
}

.subtitle {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--th-clr-txt-secondary);
}
