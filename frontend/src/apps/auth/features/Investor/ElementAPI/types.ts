import { MATRIX_DOMAIN, SYSTEM_USER_ID } from './const'

export type TMatrixServer = typeof MATRIX_DOMAIN
export type TUserID<
  T extends string = string,
  // User account may live on arbitrary server.
  // While rooms are always on corporate one.
  Domain extends string = string
> = `@${T}:${Domain}`
export type TRoomID<T extends string = string> = `!${T}:${TMatrixServer}`
export type TRoomAlias<T extends string = string> = `#${T}:${TMatrixServer}`

export interface IMatrixLoginResponse {
  user_id: TUserID<typeof SYSTEM_USER_ID>
  access_token: string
  home_server: TMatrixServer
  device_id: string
  well_known: {
    'm.homeserver': {
      base_url: string
    }
  }
}

export interface IMatrixRoomResponse {
  room_id: TRoomID
}

export interface IMatrixErrorResponse {
  errcode: string
  error: string
}
