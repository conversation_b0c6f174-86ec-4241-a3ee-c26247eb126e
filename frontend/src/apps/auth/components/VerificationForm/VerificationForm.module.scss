@import "src/styles/mixins";

.wrapper {
  display: flex;
  justify-content: center;
}

.form {
  position: relative;
  padding: 0 36px;
}

.input {
  /* stylelint-disable-next-line declaration-no-important */
  width: 64px !important;
  height: 64px;
  margin: 0 10px;
  font-size: 36px;
  font-weight: 700;
  color: var(--th-clr-txt-primary);
  background-color: transparent;
  border-radius: var(--th-border-radius);
  border: 1px solid var(--th-border-color);
  outline: none;

  &:not(:placeholder-shown) {
    border-color: var(--th-clr-primary);
  }
}

.input_container {
  align-items: center;
}

.icon,
.spinner {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
}

.spinner {
  --spinner-size: 28px;
}

.icon {
  border: 2px solid;
  border-radius: 50%;
}

@include media-md {
  .input {
    /* Override inline styles, provided by react-otp-input */
    /* stylelint-disable-next-line declaration-no-important */
    width: 55px !important;
    height: 70px;
    font-size: 32px;
  }
}

@include media-sm {
  .form {
    padding: 0;
    padding-bottom: 55px;
  }

  .input {
    margin: 0 5px;
    /* Override inline styles, provided by react-otp-input */
    /* stylelint-disable-next-line declaration-no-important */
    width: 38px !important;
    height: 50px;
    font-size: 24px;
  }

  .icon,
  .spinner {
    right: 50%;
    top: auto;
    bottom: 5px;
    transform: translateX(50%);
  }
}
