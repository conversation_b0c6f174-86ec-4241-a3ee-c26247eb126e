.theme_polity {
  /* prefix "th-" stands for "theme".
   * To clearly distinguish from any local vars */

  // base colors
  --th-clr-white: #fff;

  --th-clr-gray-25: #fcfcfd;
  --th-clr-gray-50: #f9fafb;
  --th-clr-gray-100: #f2f4f7;
  --th-clr-gray-200: #eaecf0;
  --th-clr-gray-300: #d0d5dd;
  --th-clr-gray-400: #98a2b3;
  --th-clr-gray-500: #667085;
  --th-clr-gray-600: #475467;
  --th-clr-gray-700: #344054;
  --th-clr-gray-800: #1d2939;
  --th-clr-gray-900: #101828;

  --th-clr-primary-25: #e8f5f4;
  --th-clr-primary-50: #dcf0ee;
  --th-clr-primary-100: #90bbb6;
  --th-clr-primary-200: #74a9a3;
  --th-clr-primary-300: #74a9a3;
  --th-clr-primary-400: #3b867e;
  --th-clr-primary-500: #1f756b;
  --th-clr-primary-600: #1b665e;
  --th-clr-primary-700: #175750;
  --th-clr-primary-800: #134942;
  --th-clr-primary-900: #0f3a35;

  --th-clr-success-25: #f6fef9;
  --th-clr-success-50: #ecfdf3;
  --th-clr-success-100: #d1fadf;
  --th-clr-success-200: #a6f4c5;
  --th-clr-success-300: #6ce9a6;
  --th-clr-success-400: #32d583;
  --th-clr-success-500: #12b76a;
  --th-clr-success-600: #039855;
  --th-clr-success-700: #027a48;

  --th-clr-accent-25: #fff5f6;
  --th-clr-accent-50: #fff1f3;
  --th-clr-accent-100: #ffe4e8;
  --th-clr-accent-200: #fecdd6;
  --th-clr-accent-300: #fea3b4;
  --th-clr-accent-400: #fd6f8e;
  --th-clr-accent-500: #f63d68;
  --th-clr-accent-600: #e31b54;
  --th-clr-accent-700: #c01048;
  --th-clr-accent-800: #a11043;
  --th-clr-accent-900: #89123e;

  --th-clr-error-50: #fef3f2;
  --th-clr-error-100: #fee4e2;
  --th-clr-error-200: #fecdca;
  --th-clr-error-300: #fda29b;
  --th-clr-error-400: #f97066;
  --th-clr-error-500: #f04438;
  --th-clr-error-700: #b42318;

  --th-clr-warn-50: #fffaeb;
  --th-clr-warn-200: #fedf89;
  --th-clr-warn-300: #fec84b;
  --th-clr-warn-400: #fdb022;
  --th-clr-warn-500: #f79009;
  --th-clr-warn-600: #dc6803;
  --th-clr-warn-700: #b54708;

  --th-clr-info-50: #eff8ff;
  --th-clr-info-200: #b2ddff;
  --th-clr-info-300: #84caff;
  --th-clr-info-400: #53b1fd;
  --th-clr-info-500: #2e90fa;
  --th-clr-info-700: #175cd3;

  --th-clr-teal-400: #2ed3b7;

  // logical colors
  --th-clr-bg-body: var(--th-clr-gray-25);
  --th-clr-bg-secondary: var(--th-clr-gray-50);
  --th-clr-bg-primary: var(--th-clr-white);

  --th-clr-bg-selected: var(--th-clr-success-25);
  --th-clr-bg-success: var(--th-clr-success-50);
  --th-clr-bg-warn: var(--th-clr-warn-50);
  --th-clr-bg-error: var(--th-clr-error-50);
  --th-clr-bg-info: var(--th-clr-info-50);
  --th-clr-bg-disabled: var(--th-clr-gray-200);

  --th-clr-primary: var(--th-clr-primary-500);
  --th-clr-success: var(--th-clr-success-500);
  --th-clr-warn: var(--th-clr-warn-500);
  --th-clr-error: var(--th-clr-accent-600);
  --th-clr-info: var(--th-clr-info-500);
  --th-clr-disabled: var(--th-clr-gray-300);

  --th-clr-txt-primary: var(--th-clr-gray-900);
  --th-clr-txt-secondary: var(--th-clr-gray-700);
  --th-clr-txt-hint: var(--th-clr-gray-600);
  --th-clr-txt-placeholder: var(--th-clr-gray-500);
  --th-clr-txt-disabled: var(--th-clr-gray-400);

  --th-clr-btn-outline: var(--th-clr-teal-400);

  // various defaults
  --th-border-radius: 8px;
  --th-border-color: var(--th-clr-gray-300);
  --th-icon-size: 1em;

  // ---

  font-family: "Inter", sans-serif;

  color: var(--th-clr-txt-primary);
  background-color: var(--th-clr-bg-body);
}

// ---

:export {
  th_clr_white: var(--th-clr-white);

  th_clr_gray_25: var(--th-clr-gray-25);
  th_clr_gray_50: var(--th-clr-gray-50);
  th_clr_gray_100: var(--th-clr-gray-100);
  th_clr_gray_200: var(--th-clr-gray-200);
  th_clr_gray_300: var(--th-clr-gray-300);
  th_clr_gray_400: var(--th-clr-gray-400);
  th_clr_gray_500: var(--th-clr-gray-500);
  th_clr_gray_600: var(--th-clr-gray-600);
  th_clr_gray_700: var(--th-clr-gray-700);
  th_clr_gray_800: var(--th-clr-gray-800);
  th_clr_gray_900: var(--th-clr-gray-900);

  th_clr_primary_25: var(--th-clr-primary-25);
  th_clr_primary_50: var(--th-clr-primary-50);
  th_clr_primary_100: var(--th-clr-primary-100);
  th_clr_primary_200: var(--th-clr-primary-200);
  th_clr_primary_300: var(--th-clr-primary-300);
  th_clr_primary_400: var(--th-clr-primary-400);
  th_clr_primary_500: var(--th-clr-primary-500);
  th_clr_primary_600: var(--th-clr-primary-600);
  th_clr_primary_700: var(--th-clr-primary-700);
  th_clr_primary_800: var(--th-clr-primary-800);
  th_clr_primary_900: var(--th-clr-primary-900);

  th_clr_success_25: var(--th-clr-success-25);
  th_clr_success_50: var(--th-clr-success-50);
  th_clr_success_100: var(--th-clr-success-100);
  th_clr_success_200: var(--th-clr-success-200);
  th_clr_success_300: var(--th-clr-success-300);
  th_clr_success_400: var(--th-clr-success-400);
  th_clr_success_500: var(--th-clr-success-500);
  th_clr_success_600: var(--th-clr-success-600);
  th_clr_success_700: var(--th-clr-success-700);

  th_clr_teal_400: var(--th-clr-teal-400);

  th_clr_accent_25: var(--th-clr-accent-25);
  th_clr_accent_50: var(--th-clr-accent-50);
  th_clr_accent_100: var(--th-clr-accent-100);
  th_clr_accent_200: var(--th-clr-accent-200);
  th_clr_accent_300: var(--th-clr-accent-300);
  th_clr_accent_400: var(--th-clr-accent-400);
  th_clr_accent_500: var(--th-clr-accent-500);
  th_clr_accent_600: var(--th-clr-accent-600);
  th_clr_accent_700: var(--th-clr-accent-700);
  th_clr_accent_800: var(--th-clr-accent-800);
  th_clr_accent_900: var(--th-clr-accent-900);

  th_clr_error_50: var(--th-clr-error-50);
  th_clr_error_100: var(--th-clr-error-100);
  th_clr_error_200: var(--th-clr-error-200);
  th_clr_error_300: var(--th-clr-error-300);
  th_clr_error_400: var(--th-clr-error-400);
  th_clr_error_500: var(--th-clr-error-500);
  th_clr_error_700: var(--th-clr-error-700);

  th_clr_warn_50: var(--th-clr-warn-50);
  th_clr_warn_200: var(--th-clr-warn-200);
  th_clr_warn_300: var(--th-clr-warn-300);
  th_clr_warn_400: var(--th-clr-warn-400);
  th_clr_warn_500: var(--th-clr-warn-500);
  th_clr_warn_700: var(--th-clr-warn-700);

  th_clr_info_50: var(--th-clr-info-50);
  th_clr_info_200: var(--th-clr-info-200);
  th_clr_info_300: var(--th-clr-info-300);
  th_clr_info_400: var(--th-clr-info-400);
  th_clr_info_500: var(--th-clr-info-500);
  th_clr_info_700: var(--th-clr-info-700);

  th_clr_bg_primary: var(--th-clr-bg-body);
  th_clr_bg_primary-lower: var(--th-clr-bg-secondary);
  th_clr_bg_primary-higher: var(--th-clr-bg-primary);

  th_clr_bg_selected: var(--th-clr-bg-selected);
  th_clr_bg_success: var(--th-clr-bg-success);
  th_clr_bg_error: var(--th-clr-bg-error);
  th_clr_bg_warn: var(--th-clr-bg-warn);
  th_clr_bg_info: var(--th-clr-bg-info);
  th_clr_bg_disabled: var(--th-clr-bg-disabled);

  th_clr_primary: var(--th-clr-primary);
  th_clr_success: var(--th-clr-success);
  th_clr_error: var(--th-clr-error);
  th_clr_warn: var(--th-clr-warn);
  th_clr_info: var(--th-clr-info);
  th_clr_disabled: var(--th-clr-disabled);

  th_clr_txt_primary: var(--th-clr-gray-900);
  th_clr_txt_secondary: var(--th-clr-gray-600);
  th_clr_txt_hint: var(--th-clr-txt-hint);
  th_clr_txt_placeholder: var(--th-clr-txt-placeholder);
  th_clr_txt_disabled: var(--th-clr-txt-disabled);

  th_border_radius: var(--th-border-radius);
  th_border_color: var(--th-border-color);
}
