import React from 'react'

import createTitle from '@parachutehome/create-title.macro'
import { ComponentMeta, ComponentStory } from '@storybook/react'

import { AlertVariant, Alert as Component } from './Alert'

export default {
  title: createTitle(),
  component: Component,
} as ComponentMeta<typeof Component>

const Template: ComponentStory<typeof Component> = args => (
  <Component {...args} />
)

export const Default = Template.bind({})

const variants: AlertVariant[] = ['success', 'info', 'warning', 'error']

Default.args = {
  children: 'Alert content',
  variant: 'success',
  title: 'Title',
  description: 'Description',
}

Default.argTypes = {
  className: {
    table: {
      disable: true,
    },
  },
  variant: {
    type: { name: 'enum', value: variants },
  },
}

export const Showcase: ComponentStory<typeof Component> = args => (
  <div style={{ display: 'grid', gap: '1rem', maxWidth: 'max-content' }}>
    {variants.map(variant => (
      <div style={{ display: 'grid', gap: 8 }} key={variant}>
        <pre style={{ fontSize: 20 }}>variant={variant}</pre>
        <div
          key={variant}
          style={{
            display: 'grid',
            gap: '3rem',
            gridTemplateColumns: 'repeat(2, max-content)',
            alignItems: 'start',
          }}
        >
          <Component {...args} variant={variant} />
          <Component {...args} variant={variant} title="" description="" />
        </div>
      </div>
    ))}
  </div>
)

Showcase.args = {
  title: 'Title',
  description: 'Description',
  children: 'Children',
}

Showcase.argTypes = {
  className: {
    table: {
      disable: true,
    },
  },
  variant: {
    table: {
      disable: true,
    },
  },
}
