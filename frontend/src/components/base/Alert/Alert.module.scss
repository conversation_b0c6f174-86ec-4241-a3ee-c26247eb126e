.root {
  --alert-clr-bg: var(--th-clr-gray-25);
  --alert-clr-txt: var(--th-clr-gray-700);
  --alert-clr-icon: var(--th-clr-gray-600);
  --alert-clr-border: var(--th-clr-gray-300);

  background-color: var(--alert-clr-bg);
  color: var(--alert-clr-txt);
  border: 1px solid var(--alert-clr-border);
  border-radius: 12px;
  padding: 16px;

  display: flex;
  gap: 12px;
}

.icon {
  font-size: 20px;
  color: var(--alert-clr-icon);
}

.main {
  flex-grow: 1;
}

.title {
  font-weight: 600;
}

.description {
  margin-bottom: 12px;
}

.not_empty:empty {
  display: none;
}

.variant_success {
  --alert-clr-bg: var(--th-clr-success-25);
  --alert-clr-txt: var(--th-clr-success-700);
  --alert-clr-icon: var(--th-clr-success-600);
  --alert-clr-border: var(--th-clr-success-300);
}

.variant_info {
  --alert-clr-bg: var(--th-clr-info-25);
  --alert-clr-txt: var(--th-clr-info-700);
  --alert-clr-icon: var(--th-clr-info-600);
  --alert-clr-border: var(--th-clr-info-300);
}

.variant_error {
  --alert-clr-bg: var(--th-clr-error-25);
  --alert-clr-txt: var(--th-clr-error-700);
  --alert-clr-icon: var(--th-clr-error-600);
  --alert-clr-border: var(--th-clr-error-300);
}

.variant_warning {
  --alert-clr-bg: var(--th-clr-warn-25);
  --alert-clr-txt: var(--th-clr-warn-700);
  --alert-clr-icon: var(--th-clr-warn-600);
  --alert-clr-border: var(--th-clr-warn-300);
}
