@mixin hover-active {
  &:not(.disabled) {
    &:hover {
      @content;
    }
  }
}

@mixin link {
  text-decoration: none;
  cursor: pointer;

  &.disabled {
    cursor: not-allowed;
    pointer-events: none;
  }
}

/* Make it a separate class, to allow for "unstyled" links too. */
.link_variant {
  display: inline-flex;
  align-items: center;
  /* No justify-center by default. Trying to behave like a normal text element. */
  column-gap: 0.5em;

  font-weight: 600;

  transition-duration: 0.3s;
  transition-property: color, background-color;

  &.disabled {
    color: var(--th-clr-gray-300);
  }

  &.accent {
    color: var(--th-clr-accent-700);

    @include hover-active {
      color: var(--th-clr-accent-800);
    }

    &.disabled {
      color: var(--th-clr-accent-300);
    }
  }

  /* TODO: shouldn't we have some styles for :visited state?  */
}

.link {
  @include link;
}

/* Variants */

.primary {
  color: var(--th-clr-primary-500);

  @include hover-active {
    color: var(--th-clr-primary-800);
  }
}

.secondary {
  color: var(--th-clr-gray-600);

  @include hover-active {
    color: var(--th-clr-gray-800);
  }
}

/* Sizes */

.size_sm {
  font-size: 0.875rem;
  line-height: 1.43;
}

.size_md {
  font-size: 0.875rem;
  line-height: 1.43;
}

.size_lg {
  font-size: 1rem;
  line-height: 1.5;
}

.size_xl {
  font-size: 1rem;
  line-height: 1.5;
}

/* --- */

/* TODO:
    this is identical to styles in Button module.
    maybe should dedupe this */
.icon_wrapper {
  --size: 1.25em;
  max-width: var(--size);
  max-height: var(--size);

  > * {
    max-width: 100%;
    max-height: 100%;
  }

  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  flex-grow: 0;
}
