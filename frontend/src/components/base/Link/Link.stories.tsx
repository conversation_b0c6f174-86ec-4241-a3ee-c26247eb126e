import React from 'react'
import { Router } from 'react-router-dom'

import createTitle from '@parachutehome/create-title.macro'
import { ComponentMeta, ComponentStory } from '@storybook/react'
import { createMemoryHistory } from 'history'

import { Link as Component } from './Link'
import { LinkVariant } from './Link.types'

export default {
  title: createTitle(),
  component: Component,
  decorators: [
    Story => (
      <Router history={createMemoryHistory()}>
        <Story />
      </Router>
    ),
  ],
} as ComponentMeta<typeof Component>

const Template: ComponentStory<typeof Component> = args => (
  <Component {...args} />
)

export const Default = Template.bind({})

Default.args = {
  children: 'Link',
  variant: 'secondary',
  size: 'lg',
  to: '/',
}

Default.argTypes = {
  disabled: {
    type: 'boolean',
  },
  startIcon: {
    type: 'string',
  },
  endIcon: {
    type: 'string',
  },
}

const VARIANTS: LinkVariant[] = [
  'primary',
  'secondary',
  'none',
  'btn-primary',
  'btn-secondary',
  'btn-tertiary',
]

export const Showcase: ComponentStory<typeof Component> = args => (
  <div
    style={{
      display: 'grid',
      gap: '1rem',
      gridTemplateColumns: 'repeat(3, max-content)',
    }}
  >
    {VARIANTS.map(variant => (
      <div
        key={variant}
        style={{ border: '1px solid lightgrey', borderRadius: 12, padding: 16 }}
      >
        <b>{variant}</b>
        <hr />
        <div
          key={variant}
          style={{
            display: 'grid',
            gap: '1rem',
            gridTemplateColumns: 'repeat(2, max-content)',
          }}
        >
          <Component {...args} variant={variant}>
            default
          </Component>

          <Component {...args} variant={variant} disabled>
            disabled
          </Component>

          <Component {...args} variant={variant} accent>
            accent
          </Component>

          <Component {...args} variant={variant} accent disabled>
            disabled
          </Component>
        </div>
      </div>
    ))}
  </div>
)

Showcase.args = {
  to: '/',
  size: 'lg',
}

Showcase.argTypes = {
  variant: {
    table: {
      disable: true,
    },
  },
  accent: {
    table: {
      disable: true,
    },
  },
  disabled: {
    table: {
      disable: true,
    },
  },
  startIcon: {
    type: 'string',
  },
  endIcon: {
    type: 'string',
  },
}
