@import "src/styles/mixins";
@import "src/components/base/Button/Button.module";

.root {
  overflow: hidden;
}

.placeholder {
  display: inline-block;
  width: 100%;
  cursor: pointer;
}

.default_placeholder {
  @include button;
  @include btn-colors(
    $color-primary-disabled: var(--th-clr-primary-100)
  );

  composes: size_lg from "src/components/base/Button/Button.module.scss";
}

.filename {
  color: var(--th-clr-txt-secondary);

  @include m-text-overflow;
}
