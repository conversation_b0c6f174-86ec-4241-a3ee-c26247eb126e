import { CSSProperties, ReactElement, ReactHTML, ReactNode } from 'react'

export interface IControlProps extends IStyled {
  tag?: keyof ReactHTML
  label?: ReactNode
  labelPosition?: 'before' | 'after'
  hint?: ReactNode
  hintPosition?: 'label' | 'footer'
  layout?: 'row' | 'col' | CSSProperties['gridTemplateColumns']
  alignLabel?: CSSProperties['alignSelf']
  alignInput?: CSSProperties['alignSelf']
  gap?: number | string
  error?: string
  inline?: boolean
  ignoreContext?: boolean
  showError?: boolean
  styleContent?: CSSProperties
  classNameContent?: string
  classNameError?: string
  classNameHint?: string
  loading?: boolean
  collapseEmptyFooter?: boolean
}

export interface IControlComponentProps extends IControlProps {
  children: ReactElement
}
