.badge {
  --badge-clr-bg: var(--th-clr-gray-100);
  --badge-clr-txt: var(--th-clr-txt-primary);
  --badge-clr-icon: var(--th-clr-gray-400);
  --badge-clr-border: var(--badge-clr-icon);

  background-color: var(--badge-clr-bg);
  color: var(--badge-clr-txt);

  display: flex;
  align-items: center;
  gap: 6px;

  border-radius: 16px;
  padding-block: calc(4px + 2px * var(--badge-size-lvl));
  padding-inline: calc(10px + 2px * var(--badge-size-lvl));

  font-weight: normal;
  font-size: calc(1rem * var(--badge-size-factor));
  line-height: 1.5;
  word-break: keep-all;

  // prevent any grow in flex/grid containers
  width: fit-content;
  height: fit-content;

  &.border {
    border: 1px solid var(--badge-clr-border);
  }

  &.icon {
    &::before {
      content: "";
      display: block;
      border-radius: 50%;
      width: calc(6px * var(--badge-size-factor));
      aspect-ratio: 1;
      flex-shrink: 0;
      background-color: var(--badge-clr-icon);
    }
  }
}

.size_sm {
  --badge-size-factor: 0.75;
  --badge-size-lvl: -1;
}

.size_md {
  --badge-size-factor: 1;
  --badge-size-lvl: 0;
}

.size_lg {
  --badge-size-factor: 1.25;
  --badge-size-lvl: 1;
}

.variant_success {
  --badge-clr-bg: var(--th-clr-bg-success);
  --badge-clr-icon: var(--th-clr-success);
  --badge-clr-txt: var(--th-clr-success-700);
}

.variant_error {
  --badge-clr-bg: var(--th-clr-bg-error);
  --badge-clr-icon: var(--th-clr-error);
  --badge-clr-txt: var(--th-clr-error-700);
}

.variant_warn {
  --badge-clr-bg: var(--th-clr-bg-warn);
  --badge-clr-icon: var(--th-clr-warn);
  --badge-clr-txt: var(--th-clr-warn-700);
}

.variant_info {
  --badge-clr-bg: var(--th-clr-bg-info);
  --badge-clr-icon: var(--th-clr-info);
  --badge-clr-txt: var(--th-clr-info-700);
}
