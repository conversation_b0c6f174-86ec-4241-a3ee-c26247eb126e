import React, { ReactNode } from 'react'

import createTitle from '@parachutehome/create-title.macro'
import { ComponentMeta, ComponentStory } from '@storybook/react'

import { Badge as Component, IBadgeProps } from './Badge'

export default {
  title: createTitle(),
  component: Component,
} as ComponentMeta<typeof Component>

const Template: ComponentStory<typeof Component> = args => (
  <Component {...args} />
)

export const Default = Template.bind({})

Default.args = {
  children: 'Content',
}

const SIZES: IBadgeProps['size'][] = ['sm', 'md', 'lg']
const VARIANTS: IBadgeProps['variant'][] = [
  'success',
  'warn',
  'error',
  'info',
  undefined,
]

export const Showcase: ComponentStory<typeof Component> = args => (
  <div style={{ display: 'grid', gap: 12 }}>
    {VARIANTS.map(variant => (
      <Section title={variant ?? 'default'} key={variant ?? 'default'}>
        <div style={{ display: 'flex', gap: 12 }}>
          {SIZES.map(size => (
            <Component {...args} size={size} variant={variant} key={size} />
          ))}
        </div>
      </Section>
    ))}
  </div>
)

Showcase.args = {
  children: 'Content',
}

function Section({
  children,
  title,
}: {
  children: ReactNode
  title: ReactNode
}) {
  return (
    <div style={{ display: 'grid', gap: 12 }}>
      <div>{title}</div>
      {children}
    </div>
  )
}
