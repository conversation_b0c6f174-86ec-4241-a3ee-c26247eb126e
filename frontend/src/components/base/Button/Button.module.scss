@mixin hover-active {
  &:not(:disabled, [disabled], .disabled) {
    &:hover {
      @content;
    }
  }
}

@mixin focus-active {
  &:focus {
    @content;
  }
}

@mixin disabled {
  &:disabled,
  &[disabled],
  &.disabled {
    @content;
  }
}

@mixin button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  column-gap: 0.5em;

  font-weight: 600;

  border: 1px solid transparent;

  cursor: pointer;

  transition-duration: 0.3s;
  transition-property: color, background-color, border-color, box-shadow, filter;

  @include disabled {
    cursor: not-allowed;
  }
}

@mixin btn-colors(
  $color-primary: var(--th-clr-primary),
  $color-primary-hover: var(--th-clr-primary-600),
  $color-primary-disabled: $color-primary,

  $color-border: var(--btn-clr-primary), // by default border is same as _current_ btn bg
  $color-border-hover: $color-border,
  $color-border-disabled: $color-border,

  $color-txt: var(--th-clr-white),
  $color-txt-hover: $color-txt,
  $color-txt-disabled: $color-txt,

  $outline-color: var(--th-clr-btn-outline),
  $outline-width: 2px
) {
  /* Define all this as custom props, so any button variant can be easily
   * restyled if necessary – without copying all other props that shouldn't change. */
  --btn-clr-primary: #{$color-primary};
  --btn-clr-primary-hover: #{$color-primary-hover};
  --btn-clr-primary-disabled: #{$color-primary-disabled};

  --btn-clr-txt: #{$color-txt};
  --btn-clr-txt-hover: #{$color-txt-hover};
  --btn-clr-txt-disabled: #{$color-txt-disabled};

  --btn-clr-border: #{$color-border};
  --btn-clr-border-disabled: #{$color-border-disabled};

  --btn-clr-outline: #{$outline-color};

  /* --- */

  color: var(--btn-clr-txt);
  background-color: var(--btn-clr-primary);

  border-color: var(--btn-clr-border);
  border-width: 1px;
  border-style: solid;

  @include hover-active {
    --btn-clr-primary: #{$color-primary-hover};
    --btn-clr-txt: #{$color-txt-hover};
    --btn-clr-border: #{$color-border-hover};
  }

  @include focus-active {
    outline: #{$outline-width} solid var(--btn-clr-outline);
  }

  @include disabled {
    --btn-clr-primary: #{$color-primary-disabled};
    --btn-clr-txt: #{$color-txt-disabled};
    --btn-clr-border: #{$color-border-disabled};
  }
}

@mixin btn-accent($args...) {
  &.accent {
    @include btn-colors($args...);
  }
}

.btn {
  @include button;
}

/* Variants */

.primary {
  @include btn-colors(
    $color-primary: var(--th-clr-primary),
    $color-primary-hover: var(--th-clr-primary-600),
    $color-primary-disabled: var(--th-clr-primary-100),

    $color-border-disabled: var(--th-clr-primary-200),
  );

  @include btn-accent(
    $color-primary: var(--th-clr-accent-600),
    $color-primary-hover: var(--th-clr-accent-700),
    $color-primary-disabled: var(--th-clr-accent-200),

    $outline-width: 4px,
    $outline-color: var(--th-clr-error-100)
  );
}

.secondary {
  $color-primary: var(--th-clr-white);
  $outline-width: 4px;

  @include btn-colors(
    $color-primary: $color-primary,
    $color-primary-hover: var(--th-clr-gray-100),

    $color-txt: var(--th-clr-txt-secondary),
    $color-txt-hover: var(--th-clr-gray-800),
    $color-txt-disabled: var(--th-clr-gray-300),

    $color-border: var(--th-clr-gray-300),
    $color-border-disabled: var(--th-clr-gray-200),

    $outline-width: $outline-width,
    $outline-color: var(--th-clr-gray-100)
  );

  @include btn-accent(
    $color-primary: $color-primary,
    $color-primary-hover: var(--th-clr-accent-50),

    $color-txt: var(--th-clr-accent-700),
    $color-txt-hover: var(--th-clr-accent-800),
    $color-txt-disabled: var(--th-clr-accent-300),

    $color-border: var(--th-clr-accent-300),
    $color-border-disabled: var(--th-clr-accent-200),

    $outline-width: $outline-width,
    $outline-color: var(--th-clr-error-100)
  );
}

.tertiary {
  $color-primary: transparent;

  @include btn-colors(
    $color-primary: $color-primary,
    $color-primary-hover: var(--th-clr-gray-100),

    $color-txt: var(--th-clr-txt-secondary),
    $color-txt-hover: var(--th-clr-gray-800),
    $color-txt-disabled: var(--th-clr-gray-300),

    $outline-color: var(--th-clr-btn-outline)
  );

  @include btn-accent(
    $color-primary: $color-primary,
    $color-primary-hover: var(--th-clr-accent-50),

    $color-txt: var(--th-clr-accent-700),
    $color-txt-hover: var(--th-clr-accent-800),
    $color-txt-disabled: var(--th-clr-accent-300),

    $outline-color: var(--th-clr-error-100)
  );
}

/* Sizes */

.size_sm {
  padding: 6px 12px;
  border-radius: 9px;
  font-size: 0.875rem;
}

.size_md {
  padding: 8px 16px;
  border-radius: 10px;
  font-size: 0.875rem;
}

.size_lg {
  padding: 8px 16px;
  border-radius: 11px;
  font-size: 1rem;
}

.size_xl {
  padding: 10px 20px;
  border-radius: 12px;
  font-size: 1rem;
}

/* --- */

.icon_wrapper {
  --size: 1.25em;
  max-width: var(--size);
  max-height: var(--size);

  > * {
    max-width: 100%;
    max-height: 100%;
  }

  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  flex-grow: 0;
}
