import { ButtonHTMLAttributes, ReactNode } from 'react'

export type ButtonVariant = 'primary' | 'secondary' | 'tertiary'

export type ButtonSize =
  | 'sm'
  | 'md'
  | 'lg'
  | 'xl'
  /* This one is to explicitly not apply any of default size styles
   * TODO: should get rid of this. Need to refactor overcomplicated  <Box> layout and decouple <IconButton> from it. */
  | 'unset'

export interface IButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant
  size?: ButtonSize
  accent?: boolean
  startIcon?: ReactNode
  endIcon?: ReactNode
  /* Add disabled look and prevent handling onClick event, but don't actually add `disabled` attribute –
   * thus, button can still react to other effects like hover.
   * Which allows to have a title pop-up, tooltips and stuff like this on disabled button. */
  pseudoDisabled?: boolean
}
