import React from 'react'

import createTitle from '@parachutehome/create-title.macro'
import { ComponentMeta, ComponentStory } from '@storybook/react'

import { Button as Component } from './Button'
import { ButtonVariant } from './Button.types'

export default {
  title: createTitle(),
  component: Component,
} as ComponentMeta<typeof Component>

const Template: ComponentStory<typeof Component> = args => (
  <Component {...args} />
)

export const Default = Template.bind({})

Default.args = {
  children: 'Button',
  variant: 'primary',
  size: 'lg',
}

Default.argTypes = {
  pseudoDisabled: {
    table: {
      disable: true,
    },
  },
  disabled: {
    type: 'boolean',
  },
  startIcon: {
    type: 'string',
  },
  endIcon: {
    type: 'string',
  },
}

const VARIANTS: ButtonVariant[] = ['primary', 'secondary', 'tertiary']

export const Showcase: ComponentStory<typeof Component> = args => (
  <div style={{ display: 'grid', gap: '1rem', maxWidth: 'max-content' }}>
    {VARIANTS.map(variant => (
      <div
        key={variant}
        style={{ border: '1px solid lightgrey', borderRadius: 12, padding: 16 }}
      >
        <b>{variant}</b>
        <hr />
        <div
          key={variant}
          style={{
            display: 'grid',
            gap: '1rem',
            gridTemplateColumns: 'repeat(2, max-content)',
          }}
        >
          <Component {...args} variant={variant}>
            default
          </Component>

          <Component {...args} variant={variant} disabled>
            disabled
          </Component>

          <Component {...args} variant={variant} accent>
            accent
          </Component>

          <Component {...args} variant={variant} accent disabled>
            disabled
          </Component>
        </div>
      </div>
    ))}
  </div>
)

Showcase.argTypes = {
  pseudoDisabled: {
    table: {
      disable: true,
    },
  },
  variant: {
    table: {
      disable: true,
    },
  },
  accent: {
    table: {
      disable: true,
    },
  },
  startIcon: {
    type: 'string',
  },
  endIcon: {
    type: 'string',
  },
}
