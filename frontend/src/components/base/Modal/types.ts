import { CSSProperties, ReactNode } from 'react'
import { Props as IBaseModalProps } from 'react-modal'

import { Optional } from 'utility-types'

import { IButtonProps } from '../Button'

export interface IModalProps
  extends Omit<
    Optional<IBaseModalProps, 'isOpen'>,
    // we'll use a custom one
    | 'overlayElement'
    // passing this class disables default inline styles for overlay. Don't need this.
    | 'overlayClassName'
  > {
  showCloseBtn?: boolean
  /* Position close-btn absolutely.
   * Note that as by design btn is on same level as other header content, it may cause overlap.
   * Intended for rare cases when header content must be perfectly centered. */
  floatCloseBtn?: boolean
  /* Change default values for showCloseBtn / shouldCloseOnEsc / shouldCloseOnOverlayClick props  */
  defaultRequired?: boolean

  /* Override this callback, to don't accept args (click event) */
  onRequestClose?(): void

  title?: ReactNode
  subtitle?: ReactNode
  icon?: ReactNode
  footer?: ReactNode

  modalBodyClassName?: string
  modalBodyStyle?: CSSProperties

  idSlotIcon?: string
  idSlotTitle?: string
  idSlotSubtitle?: string
  idSlotFooter?: string
}

type ModalConfirmButtonProps = Omit<
  IButtonProps,
  'onClick' | 'disabled' | 'type' | 'form'
>

export interface IModalConfirmProps
  extends Omit<IModalProps, 'onRequestClose'> {
  form?: string // form id for a button's `form` attribute
  btnCancel?: ModalConfirmButtonProps
  btnConfirm?: ModalConfirmButtonProps
  onCancel?: () => void
  onConfirm?: () => void
  allowConfirm?: boolean
  isLoading?: boolean
  loader?: ReactNode | (() => ReactNode)
}
