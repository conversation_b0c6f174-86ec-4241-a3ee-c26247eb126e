:root {
  --modal-overlay-zindex: 999;
}

/* stylelint-disable declaration-no-important, selector-class-pattern */
:global {
  /* defaults for ALL pop-ups */
  .ReactModal__Overlay {
    backdrop-filter: blur(8px);
    z-index: var(--modal-overlay-zindex);
  }

  .ReactModal__Content {
    border: none !important;
    inset: unset !important;
    padding: 0 !important;
  }
}

.portal {
  /* defaults for the default screen-centered small pop-up */
  :global {
    .ReactModal__Overlay {
      display: flex;
      justify-content: center;
      align-items: center;

      // TODO:
      //  This color is --th-clr-gray-700
      //  But how in the world do you convert hex to rgb in vanilla CSS?
      background-color: rgb(52 64 84 / 70%) !important;
    }

    .ReactModal__Content {
      position: relative !important;

      background-color: var(--th-clr-bg-primary) !important;
      border-radius: calc(1.5 * var(--th-border-radius)) !important;
      box-shadow: 0 20px 24px -4px rgb(16 24 40 / 8%), 0 8px 8px -4px rgb(16 24 40 / 3%);

      max-width: 80%;
      max-height: 80%;

      // We need scrollbar appear only on `.body` block.
      // Somehow flex helps with this.
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
  }
}
/* stylelint-enable */

.content {
  --modal-section-padding: 24px;
  --modal-section-divider: 1px solid var(--th-clr-gray-200);

  // This is also required to put scrollbar down to `.body`
  display: grid;
  overflow: auto;
}

/* Don't apply this to .header itself,
 * because there can be absolute-positioned elements inside .header,
 * which should respect the padding.
 * See desc for `floatCloseBtn` prop in `types.ts`. */
.header_container {
  padding: var(--modal-section-padding);
  border-bottom: var(--modal-section-divider);
}

.header {
  position: relative;

  display: flex;
  gap: 16px;

  &_icon {
    align-self: center;
  }

  &_content {
    overflow: hidden;
    flex-grow: 1;
  }
}

.body {
  padding: var(--modal-section-padding);
  overflow: auto;
}

.footer {
  padding: var(--modal-section-padding);
  border-top: var(--modal-section-divider);
}

.title {
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.56;
  color: var(--th-clr-txt-primary);

  overflow: hidden;
  text-overflow: ellipsis;
}

.subtitle {
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  color: var(--th-clr-txt-secondary);

  overflow: hidden;
  text-overflow: ellipsis;
}

.btn_close {
  --size: 24px;
  width: var(--size);
  height: var(--size);

  cursor: pointer;
  color: var(--th-clr-gray-500);
  align-self: start;
  flex-shrink: 0;

  &_float {
    position: absolute;
    right: 0;
  }
}
