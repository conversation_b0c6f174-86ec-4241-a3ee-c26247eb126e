import * as React from 'react'

import { Overwrite } from 'utility-types'

import { MaybeBoxSize } from '../../layouts/Box'

export type IIconType =
  /* TODO: do we need both "check" and "success"? */
  | 'check'
  | 'success'
  | 'error'
  | 'confirm'
  | 'close'
  | 'cancel'
  | 'external'
  | 'refresh'
  | 'delete'
  | 'info'
  | 'edit'
  | 'logout'
  | 'copy'
  | 'upload'
  | 'download'
  | 'wallet'
  | 'chevron-left'
  | 'chevron-right'
  | 'notification'
  | 'book'
  | 'eye'
  | 'empty-list'
  | 'walletconnect'
  | 'arrow-narrow-up'
  | 'arrow-narrow-down'
  | 'arrow-narrow-left'
  | 'arrow-narrow-right'
  // TODO: maybe these aren't that needed; rethink when design is completed
  | 'add-card'
  | 'location'
  | 'coin'

type SVGProps = Omit<React.SVGProps<SVGSVGElement>, 'ref'>

type BaseIconProps = Overwrite<
  SVGProps,
  {
    // Overwrite this: https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/type
    type:
      | IIconType
      /* typescript is unable to validate signature of a passed component.
       * You may pass in a whatever component, and get totally broken result.
       *
       * But, the ability to render a custom icon, which is not registered in icon types enum,
       * is very convenient, so this non-reliable interface is left intentionally.
       *
       * It is assumed that you pass in either react-icons components, or smth that supports Icon interface. */
      | React.FunctionComponent
    // Add a custom shorthand for colored icons
    color?: boolean | string
  }
>

export interface IIconProps extends BaseIconProps {
  size?: MaybeBoxSize
  disabled?: boolean
  box?: boolean
}

export type IIconSettingsContext = Omit<IIconProps, 'type'>
