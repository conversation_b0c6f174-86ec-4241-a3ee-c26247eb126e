import React, { ReactNode } from 'react'
import { MdAppSettingsAlt } from 'react-icons/md'

import createTitle from '@parachutehome/create-title.macro'
import { ComponentMeta, ComponentStory, Story } from '@storybook/react'

import { Icon as Component } from './Icon'
import { IIconProps } from './types'

export default {
  title: createTitle(),
  component: Component,
} as ComponentMeta<typeof Component>

const Template: ComponentStory<typeof Component> = args => (
  <Component {...args} />
)

export const Default = Template.bind({})

Default.args = {
  type: 'logout',
}

const ShowcaseTemplate: ComponentStory<typeof Component> = ({
  size,
  ...props
}) => (
  <div style={{ display: 'flex', gap: 12 }}>
    <Component {...props} size="xs" />
    <Component {...props} size="sm" />
    <Component {...props} size="md" />
    <Component {...props} size="lg" />
    <Component {...props} size="xl" />
    <Component {...props} size="2xl" />
  </div>
)

export const Showcase: Story<IIconProps> = props => (
  <div style={{ display: 'grid', gap: 12 }}>
    <Section title="Fixed sizes">
      <ShowcaseTemplate {...props} box={false} />
      <ShowcaseTemplate {...props} box />
    </Section>

    <Section title="Custom size (set through control panel)">
      <Component {...props} />
    </Section>

    <Section title="Custom icon element">
      <Component {...props} type={MdAppSettingsAlt} />
    </Section>
  </div>
)

Showcase.args = {
  type: 'logout',
  size: '5rem',
}

function Section({
  children,
  title,
}: {
  children: ReactNode
  title: ReactNode
}) {
  return (
    <div style={{ display: 'grid', gap: 12 }}>
      <div>{title}</div>
      {children}
    </div>
  )
}
