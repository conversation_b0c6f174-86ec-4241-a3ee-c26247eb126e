@import "src/styles/mixins";

@value icon-gap: 12px;
@value icon-size: 24px;
@value default-body-max-height: 300px;

:export {
  icon_size: icon-size;
  icon_gap: icon-gap;
  default_body_max_height: default-body-max-height;
}

.header {
  position: relative;
  cursor: pointer;
  padding: 15px;
  padding-right: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  overflow: hidden;
}

.icon {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: scale(1, -1) translateY(50%);
  fill: var(--th-clr-gray-300);
  transition: 0.3s;
}

.body {
  position: relative;
  padding: 0 15px;
  box-sizing: content-box;

  overflow: hidden;
  max-height: 0;
  transition: 0.3s;
  /**
   * This will reserve constant space at right equal to scrollbar width.
   * Even if there is no scrollbar needed in particular case.
   * A little bit odd, but better than content shifting left-right when scrollbar appears.
   */
  scrollbar-gutter: stable;

  &::before {
    content: "";
    position: absolute;
    display: block;
    height: 1px;
    background-color: var(--th-clr-gray-400);
    width: 60%;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
  }
}

.accordion {
  // explicit value is required for transition. Values like `max-content` can't be animated.
  --accordion-body-max-height: default-body-max-height;

  @include m-bordered;

  position: relative;
  background-color: var(--th-clr-bg-primary);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -1px;
    width: 2px;
    height: 0;
    background-color: var(--th-clr-primary);
    transition: 0.3s;
  }

  &:hover::before {
    height: 100%;
  }

  &.open {
    .body {
      padding: 15px;
      max-height: var(--accordion-body-max-height);
    }

    .icon {
      transform: translateY(-50%);
    }
  }

  /**
   * It must NOT be put in just ".open" state, because it will cause scrollbar flickering on toggle:
   * scroll thumb size will grow and shrink when accordion toggles, which looks really weird.
   */
  &.opened {
    .body {
      overflow: auto;
    }
  }
}
