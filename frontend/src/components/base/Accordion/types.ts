import { ReactHTML, ReactNode } from 'react'

export interface IAccordionProps extends IStyled {
  tag?: keyof ReactHTML
  children: ReactNode
  header?: ReactNode
  lazy?: boolean
  isOpen?: boolean
  defaultOpen?: boolean
  onToggle?: (isOpen: boolean) => void
  bodyMaxHeight?: number | string
}

export interface IAccordionContext {
  bodyMaxHeight: number | string
  iconSize: number | string
  iconGap: number | string
  isOpen: boolean
}
