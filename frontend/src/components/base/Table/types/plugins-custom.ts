import { CellPropGetter, HeaderPropGetter, TableCellProps } from 'react-table'

import { PatchPropGetter } from './util'

type CustomCellProps = Omit<TableCellProps, 'key'>

export interface ICustomColumnOptions<D extends object> {
  // props to be passed directly BOTH to `td` and `th` cells
  tx?: CustomCellProps | (() => CustomCellProps)
  // props to be passed directly to `th` cells
  th?: CustomCellProps | PatchPropGetter<HeaderPropGetter<D>>
  // props to be passed directly to `td` cells
  td?: CustomCellProps | PatchPropGetter<CellPropGetter<D>>
  // props to be passed directly to wrapper of `td` cell content
  cell_content?: CustomCellProps | PatchPropGetter<CellPropGetter<D>>
  // Style text in `td` cell
  accent?: boolean
}

// ---

/*
 * Extension point for custom plugins
 * Extend these interfaces from plugin's options interfaces
 *
 * TODO: Remove eslint-disable when these are not empty
 */
/* eslint-disable @typescript-eslint/no-empty-interface, @typescript-eslint/no-unused-vars */
export interface ICustomPluginsOptions<T extends object> {}

export interface ICustomPluginsState<T extends object> {}
/* eslint-enable */

// ---
// Apply our customs to react-table internals

declare module 'react-table' {
  interface ColumnInterface<D extends object> extends ICustomColumnOptions<D> {}

  interface TableOptions<D extends object> extends ICustomPluginsOptions<D> {}

  interface TableState<D extends object> extends ICustomPluginsState<D> {}

  interface TableRowProps {
    id?: string | number
  }
}
