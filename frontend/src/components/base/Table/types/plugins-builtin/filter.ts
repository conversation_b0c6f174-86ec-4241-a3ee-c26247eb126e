import {
  UseFiltersColumnOptions,
  UseFiltersColumnProps,
  UseFiltersInstanceProps,
  UseFiltersOptions,
  UseFiltersState,
  UseGlobalFiltersColumnOptions,
  UseGlobalFiltersInstanceProps,
  UseGlobalFiltersOptions,
  UseGlobalFiltersState,
} from 'react-table'

declare module 'react-table' {
  interface ColumnInterface<D extends object>
    extends UseFiltersColumnOptions<D>,
      UseGlobalFiltersColumnOptions<D> {}

  interface TableOptions<D extends object>
    extends UseFiltersOptions<D>,
      UseGlobalFiltersOptions<D> {}

  interface ColumnInstance<D extends object> extends UseFiltersColumnProps<D> {}

  interface TableInstance<D extends object>
    extends UseFiltersInstanceProps<D>,
      UseGlobalFiltersInstanceProps<D> {}

  interface TableState<D extends object>
    extends UseFiltersState<D>,
      UseGlobalFiltersState<D> {}
}
