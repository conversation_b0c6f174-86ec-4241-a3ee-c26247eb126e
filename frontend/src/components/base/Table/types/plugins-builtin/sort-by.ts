import {
  UseSortByColumnOptions,
  UseSortByColumnProps,
  UseSortByInstanceProps,
  UseSortByOptions,
  UseSortByState,
} from 'react-table'

declare module 'react-table' {
  interface ColumnInterface<D extends object>
    extends UseSortByColumnOptions<D> {}

  interface TableOptions<D extends object> extends UseSortByOptions<D> {}

  interface ColumnInstance<D extends object> extends UseSortByColumnProps<D> {}

  interface TableInstance<D extends object> extends UseSortByInstanceProps<D> {}

  interface TableState<D extends object> extends UseSortByState<D> {}

  interface Hooks<D extends object> extends UseSortByHooks<D> {}
}
