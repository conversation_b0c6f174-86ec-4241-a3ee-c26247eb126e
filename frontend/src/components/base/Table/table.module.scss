.container {
  --table-col-gap: 48px;
  --table-cell-pad: 16px;

  // ---

  display: grid;
  grid-auto-rows: max-content;
  gap: 8px;

  overflow-x: auto;
  scrollbar-gutter: auto;
}

.table {
  border-collapse: collapse;

  width: 100%;
  background-color: inherit;

  thead,
  tbody,
  tr {
    background-color: inherit;
  }

  tbody tr:hover {
    background-color: var(--th-clr-bg-secondary);
  }

  th,
  td {
    padding-inline: calc(var(--table-col-gap) / 2);
  }

  td {
    padding-block: var(--table-cell-pad);
  }

  th {
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1.5;
    color: var(--th-clr-txt-hint);
    text-align: start;
    padding-block: 12px;
  }

  thead {
    position: sticky;
    top: 0;
    border-bottom: 1px solid var(--th-clr-gray-200);
    background-color: var(--th-clr-bg-secondary);
    // make it be over elements of "position: relative" by default
    // (unless they have own z-index)
    z-index: 1;
  }
}

.cell {
  vertical-align: middle;
}

.cell_content {
  display: flex;
  gap: 0.5em;
}

.data_row {
  --table-row-border-color: var(--th-border-color);
}

.cell_txt_accent {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.43;
  font-style: normal;
  color: var(--th-clr-txt-primary);
}

.placeholder_container {
  overflow: auto;
  background-color: inherit;
}
