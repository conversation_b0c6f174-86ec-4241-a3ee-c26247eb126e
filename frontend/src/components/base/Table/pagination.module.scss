.pagination {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-inline: 16px;
}

.btns_holder {
  display: flex;
  gap: 4px;
}

.btn_navigation {
  width: 32px;
  padding: 2px 4px;
}

.page_size {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
}

.page_size_selector {
  border-radius: 8px;
  padding: 1px 4px;
  background-color: var(--th-clr-primary);
  color: var(--th-clr-white);
  font-weight: bold;
}

.page_counter {
  display: inline-flex;
  gap: inherit;
  font-size: 0.875rem;
}

.badge {
  font-size: inherit;
  gap: 4px;
}
