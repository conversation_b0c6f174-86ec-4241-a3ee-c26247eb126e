@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-align {
  &_start {
    align-items: flex-start;
  }

  &_center {
    align-items: center;
  }

  &_stretch {
    align-items: stretch;
  }
}

.page {
  --page-sticky-scroll-margin: 0;
  --page-sticky-header-top: 0;

  &.pushdown {
    margin-top: 48px;
  }
}

.page_sticky_scroll_watcher {
  position: relative;
  // This should possible to do with `rootMargin` option on IntersectionObserver,
  // but I didn't manage to get it working.
  // Also, here we can use any CSS units, while with that opt – only px or %.
  top: var(--page-sticky-scroll-margin, 0);
  // This is required to properly position relative element.
  // Without height, it's `top` position will be SOMEHOW affected by top margin of first static element in block.
  height: 0.1px;
}

.page_header {
  @include flex-column;
  @include flex-align;
  gap: 8px;
  margin-block-end: 40px;
}

.page_header_sticky {
  position: sticky;
  top: var(--page-sticky-header-top, 0);
  // it is required to deal with potential "position: relative" elements
  // they will beat "position: sticky" and still appear on top of it
  z-index: 10;

  &::after {
    content: "";
    display: inline-block;
    width: 100%;
    height: 2px;
    --gradient:
      linear-gradient(
        to right,
        var(--th-clr-success-100),
        var(--th-clr-success-500),
        var(--th-clr-success-700),
        var(--th-clr-success-500),
        var(--th-clr-success-100)
      );
    background: var(--gradient);
    position: absolute;
    bottom: 0;

    opacity: 0;
    transition: opacity 500ms;
  }

  &.page_header_sticking {
    &::after {
      opacity: 1;
    }
  }
}

@mixin hide-empty {
  &:empty {
    display: none;
  }
}

.hero {
  @include hide-empty;
}

.page_title {
  font-weight: 600;
  font-size: 2.25rem;
  line-height: 1.3;
  color: var(--th-clr-txt-primary);

  @include hide-empty;
}

.page_description {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--th-clr-txt-secondary);

  @include hide-empty;
}

.page_content {
  @include flex-column;
  @include flex-align;
}

.go_back {
  color: var(--th-clr-txt-hint);
  padding: 8px;
  border-radius: 9px;
  font-size: 0.875rem;
  line-height: 1.43;
  width: fit-content;
  margin-bottom: 2rem;
}
