@import "src/styles/mixins";

.card {
  background-color: var(--th-clr-bg-primary);
  overflow: auto;

  --card-spacing: 2.5em;
  --card-aside-width: auto;
  --card-border: 1px solid var(--th-clr-gray-200);
  --card-border-radius: 1.5rem;
  --card-title-size: 1.5em;
  --card-header-gap: 1.5em;

  padding: var(--card-spacing);
  border: var(--card-border);
  border-radius: var(--card-border-radius);

  &.spacing_sm {
    --card-border-radius: 1rem;
    --card-spacing: 1.5em;
    --card-title-size: 1.35em;
    --card-header-gap: 1.35em;
  }

  &.spacing_xs {
    --card-border-radius: 1rem;
    --card-spacing: 0.75em;
    --card-title-size: 1.125em;
    --card-header-gap: 1.125em;
  }
}

.card_aside {
  display: grid;
  grid-template-columns: var(--card-aside-width) 1fr;
  gap: var(--card-spacing);
}

.elevated {
  @include m-elevated;
}

.main { /* none */ }

.aside { /* none */ }

.header {
  margin-bottom: var(--card-header-gap);
}

.title {
  font-weight: 500;
  font-size: var(--card-title-size);
  line-height: 1.5;
  color: var(--th-clr-txt-primary);
}

.subtitle {
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.43;
  color: var(--th-clr-txt-secondary);
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 1em;

  margin-top: 2.5em;
}
