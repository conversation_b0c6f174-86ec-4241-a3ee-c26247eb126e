.box_params {
  // ---
  // Basics

  --box-size-def: auto;
  --box-pad-def: auto;

  --box-size-min: 32px;
  --box-size-step: 4px;

  --box-pad-min: 4px;
  --box-pad-step: 2px;

  // ---
  // Internal. To be modifier by specific size class

  --box-size-factor: 0;
  --box-pad-factor: var(--box-size-factor); // equal by default

  // ---
  // The main. To be overridden when any specific box size is applied.

  --box-size: var(--box-size-def);
  --box-pad: var(--box-pad-def);

  // ---

  box-sizing: border-box;
  aspect-ratio: 1;
}

.box {
  composes: box_params;

  height: var(--box-size);
  padding: var(--box-pad);

  overflow: clip;
  overflow-clip-margin: content-box;

  > * {
    max-width: 100%;
    max-height: 100%;
    flex-grow: 1;
  }

  display: flex;
  justify-content: center;
  align-items: center;
}

.box_active {
  --box-size: calc(var(--box-size-min) + var(--box-size-step) * var(--box-size-factor));
  --box-pad: calc(var(--box-pad-min) + var(--box-pad-step) * var(--box-pad-factor));
}

.inline {
  display: inline-flex;
}

.bordered {
  border: 1px solid var(--th-border-color);
  border-radius: var(--th-border-radius);
}

// ---
// Size modifiers

.size_xs {
  composes: box_active;
  --box-size-factor: 0;
}

.size_sm {
  composes: box_active;
  --box-size-factor: 1;
}

.size_md {
  composes: box_active;
  --box-size-factor: 2;
}

.size_lg {
  composes: box_active;
  --box-size-factor: 3;
}

.size_xl {
  composes: box_active;
  --box-size-factor: 4;
}

.size_2xl {
  composes: box_active;
  --box-size-factor: 6;
  --box-pad-factor: 5;
}
