{"name": "bastion", "version": "0.1.0", "private": true, "dependencies": {"@hookform/error-message": "^2.0.1", "@react-hook/async": "^3.1.1", "@react-hook/event": "^1.2.6", "@react-hook/switch": "^1.3.3", "@react-hook/toggle": "^2.0.1", "@reduxjs/toolkit": "^2.2.4", "@types/debug": "^4.1.7", "@types/jest": "^27.4.1", "@types/lodash": "^4.14.181", "@types/node": "^17.0.23", "@types/qs": "^6.9.7", "@types/react": "^17.0.43", "@types/react-dom": "^17.0.14", "@types/react-modal": "^3.13.1", "@types/react-redux": "^7.1.23", "@types/react-router-dom": "^5.3.3", "@types/react-table": "^7.7.10", "@walletconnect/client": "^1.8.0", "@walletconnect/sign-client": "^2.7.3", "axios": "^0.26.1", "axios-jwt": "^1.7.6", "clsx": "^1.1.1", "connected-react-router": "^6.9.2", "date-fns": "^2.28.0", "debug": "^4.3.4", "filesize": "^8.0.7", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "object-to-formdata": "^4.4.2", "qs": "^6.10.3", "rc-tooltip": "^6.0.1", "react": "^17.0.0", "react-app-error-boundary": "^1.0.2", "react-custom-scrollbars-2": "^4.5.0", "react-dom": "^17.0.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^1.3.0", "react-hook-consent": "^3.2.0", "react-hook-form": "^7.29.0", "react-icons": "^4.10.1", "react-modal": "^3.15.1", "react-otp-input": "^2.4.0", "react-redux": "^7.2.6", "react-router": "5.2.1", "react-router-dom": "5.3.0", "react-select": "^5.3.2", "react-table": "^7.7.0", "react-tabs": "^4", "react-toastify": "^9.0.4", "tiny-cookie": "^2.3.2", "typescript": "4.7.4", "use-immer": "^0.9.0", "utility-types": "^3.10.0", "uuid": "^9.0.0", "world-countries": "^4.0.0"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "app": "scripts/run-app.sh", "app:start": "yarn app start", "app:start:auth": "yarn app start auth", "app:start:cabinet": "yarn app start cabinet", "app:start:operator": "yarn app start operator", "app:build": "yarn app build", "server": "source scripts/read-env.sh && cd server && yarn watch", "server:ws:bidding": "source scripts/read-env.sh && cd server && yarn ws-bidding", "test": "react-app-rewired test", "lint:js": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:ts": "tsc --noEmit", "lint:css": "stylelint 'src/**/*.{s,}css'", "lint": "yarn lint:js && yarn lint:ts && yarn lint:css", "fix": "yarn lint:js --fix && yarn lint:css --fix", "clean:lint": "rimraf .eslintcache && rimraf node_modules/.cache/eslint-loader", "clean": "yarn clean:lint && rimraf node_modules/.cache", "report": "open build/report.html", "prepare": "scripts/prepare.sh", "storybook": "start-storybook -p 6006 -s public", "build-storybook": "build-storybook -s public"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@parachutehome/create-title.macro": "^2.0.0", "@storybook/addon-actions": "^6.5.3", "@storybook/addon-essentials": "^6.5.3", "@storybook/addon-interactions": "^6.5.3", "@storybook/addon-links": "^6.5.3", "@storybook/builder-webpack4": "^6.5.3", "@storybook/manager-webpack4": "^6.5.3", "@storybook/node-logger": "^6.5.3", "@storybook/preset-create-react-app": "^3.2.0", "@storybook/react": "^6.5.3", "@storybook/testing-library": "^0.0.11", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^14.0.0", "@types/rc-tooltip": "^3.7.7", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.17.0", "babel-plugin-jsx-control-statements": "^4.1.2", "babel-plugin-macros": "^3.1.0", "babel-plugin-transform-imports": "^2.0.0", "concurrently": "^7.1.0", "customize-cra": "^1.0.0", "detect-port": "^1.3.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^7.0.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^26.1.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-jsx-control-statements": "^2.2.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.4.0", "eslint-plugin-storybook": "^0.5.12", "eslint-plugin-testing-library": "^5.1.0", "http-proxy-middleware": "^2.0.4", "husky": ">=7", "lint-staged": ">=12", "lodash-webpack-plugin": "^0.11.6", "node-sass": "^7.0.1", "postcss": "^8.4.12", "postcss-normalize": "^10.0.1", "prettier": "^2.6.1", "react-app-rewired": "^2.2.1", "react-scripts": "4.0.3", "rimraf": "^3.0.2", "stylelint": "^14.6.1", "stylelint-config-css-modules": "^4.1.0", "stylelint-config-standard-scss": "^3.0.0", "stylelint-webpack-plugin": "^3.2.0", "typescript-plugin-css-modules": "^3.4.0", "webpack-bundle-analyzer": "^4.5.0"}, "resolutions": {"react-error-overlay": "6.0.9"}}