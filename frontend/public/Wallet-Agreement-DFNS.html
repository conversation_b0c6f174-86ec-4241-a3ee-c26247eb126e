<html><head><meta content="text/html; charset=UTF-8" http-equiv="content-type"><style type="text/css">@import url(https://themes.googleusercontent.com/fonts/css?kit=fpjTOVmNbO4Lz34iLyptLUXza5VhXqVC6o75Eld_V98);.lst-kix_list_33-5>li{counter-increment:lst-ctn-kix_list_33-5}.lst-kix_list_2-1>li{counter-increment:lst-ctn-kix_list_2-1}ol.lst-kix_list_33-5.start{counter-reset:lst-ctn-kix_list_33-5 0}ol.lst-kix_list_30-0.start{counter-reset:lst-ctn-kix_list_30-0 0}.lst-kix_list_30-6>li{counter-increment:lst-ctn-kix_list_30-6}ul.lst-kix_list_9-3{list-style-type:none}ul.lst-kix_list_9-4{list-style-type:none}ul.lst-kix_list_9-1{list-style-type:none}ul.lst-kix_list_9-2{list-style-type:none}ul.lst-kix_list_9-7{list-style-type:none}ul.lst-kix_list_9-8{list-style-type:none}ol.lst-kix_list_20-2.start{counter-reset:lst-ctn-kix_list_20-2 0}ul.lst-kix_list_9-5{list-style-type:none}ul.lst-kix_list_9-6{list-style-type:none}ol.lst-kix_list_2-3.start{counter-reset:lst-ctn-kix_list_2-3 0}ul.lst-kix_list_9-0{list-style-type:none}.lst-kix_list_29-8>li{counter-increment:lst-ctn-kix_list_29-8}ol.lst-kix_list_30-5.start{counter-reset:lst-ctn-kix_list_30-5 0}.lst-kix_list_18-8>li{counter-increment:lst-ctn-kix_list_18-8}.lst-kix_list_32-8>li{counter-increment:lst-ctn-kix_list_32-8}.lst-kix_list_24-7>li:before{content:"o  "}ul.lst-kix_list_27-0{list-style-type:none}ul.lst-kix_list_27-1{list-style-type:none}.lst-kix_list_24-8>li:before{content:"\0025aa   "}ul.lst-kix_list_27-2{list-style-type:none}.lst-kix_list_24-2>li:before{content:"\0025aa   "}ol.lst-kix_list_20-7.start{counter-reset:lst-ctn-kix_list_20-7 0}.lst-kix_list_24-3>li:before{content:"\0025cf   "}li.li-bullet-13:before{margin-left:-17.9pt;white-space:nowrap;display:inline-block;min-width:17.9pt}.lst-kix_list_24-4>li:before{content:"o  "}ol.lst-kix_list_34-2.start{counter-reset:lst-ctn-kix_list_34-2 0}ul.lst-kix_list_27-3{list-style-type:none}.lst-kix_list_24-5>li:before{content:"\0025aa   "}ul.lst-kix_list_27-4{list-style-type:none}ul.lst-kix_list_27-5{list-style-type:none}ul.lst-kix_list_27-6{list-style-type:none}ul.lst-kix_list_27-7{list-style-type:none}ul.lst-kix_list_27-8{list-style-type:none}.lst-kix_list_24-6>li:before{content:"\0025cf   "}.lst-kix_list_23-6>li:before{content:"\0025cf   "}.lst-kix_list_23-3>li:before{content:"\0025cf   "}.lst-kix_list_23-7>li:before{content:"o  "}.lst-kix_list_23-2>li:before{content:"\0025aa   "}ul.lst-kix_list_16-2{list-style-type:none}ul.lst-kix_list_16-1{list-style-type:none}ul.lst-kix_list_16-0{list-style-type:none}.lst-kix_list_23-0>li:before{content:"\0025cf   "}.lst-kix_list_23-8>li:before{content:"\0025aa   "}.lst-kix_list_3-6>li{counter-increment:lst-ctn-kix_list_3-6}.lst-kix_list_23-1>li:before{content:"o  "}.lst-kix_list_24-1>li:before{content:"o  "}ul.lst-kix_list_16-8{list-style-type:none}ul.lst-kix_list_16-7{list-style-type:none}ul.lst-kix_list_16-6{list-style-type:none}.lst-kix_list_2-8>li{counter-increment:lst-ctn-kix_list_2-8}ul.lst-kix_list_16-5{list-style-type:none}.lst-kix_list_24-0>li:before{content:"\0025cf   "}ul.lst-kix_list_16-4{list-style-type:none}ul.lst-kix_list_16-3{list-style-type:none}.lst-kix_list_23-4>li:before{content:"o  "}.lst-kix_list_23-5>li:before{content:"\0025aa   "}.lst-kix_list_22-2>li:before{content:"\0025aa   "}.lst-kix_list_22-6>li:before{content:"\0025cf   "}.lst-kix_list_22-0>li:before{content:"\0025cf   "}.lst-kix_list_22-8>li:before{content:"\0025aa   "}ol.lst-kix_list_3-0.start{counter-reset:lst-ctn-kix_list_3-0 0}ol.lst-kix_list_34-7.start{counter-reset:lst-ctn-kix_list_34-7 0}.lst-kix_list_22-4>li:before{content:"o  "}ol.lst-kix_list_32-8.start{counter-reset:lst-ctn-kix_list_32-8 0}ol.lst-kix_list_29-2.start{counter-reset:lst-ctn-kix_list_29-2 0}.lst-kix_list_25-5>li:before{content:"\0025aa   "}.lst-kix_list_25-7>li:before{content:"o  "}.lst-kix_list_3-5>li{counter-increment:lst-ctn-kix_list_3-5}ol.lst-kix_list_18-3.start{counter-reset:lst-ctn-kix_list_18-3 0}ol.lst-kix_list_18-7{list-style-type:none}ol.lst-kix_list_18-8{list-style-type:none}ol.lst-kix_list_33-0.start{counter-reset:lst-ctn-kix_list_33-0 0}ol.lst-kix_list_18-3{list-style-type:none}ol.lst-kix_list_18-4{list-style-type:none}ol.lst-kix_list_18-5{list-style-type:none}ol.lst-kix_list_18-6{list-style-type:none}ol.lst-kix_list_18-0{list-style-type:none}ol.lst-kix_list_18-1{list-style-type:none}ol.lst-kix_list_18-2{list-style-type:none}ol.lst-kix_list_2-8.start{counter-reset:lst-ctn-kix_list_2-8 0}ol.lst-kix_list_29-8{list-style-type:none}ol.lst-kix_list_29-7{list-style-type:none}ol.lst-kix_list_29-4{list-style-type:none}ol.lst-kix_list_29-3{list-style-type:none}ol.lst-kix_list_29-6{list-style-type:none}ol.lst-kix_list_29-5{list-style-type:none}ol.lst-kix_list_29-0{list-style-type:none}ol.lst-kix_list_29-2{list-style-type:none}ol.lst-kix_list_29-1{list-style-type:none}ol.lst-kix_list_3-1{list-style-type:none}ol.lst-kix_list_3-2{list-style-type:none}ol.lst-kix_list_3-3{list-style-type:none}ol.lst-kix_list_3-4.start{counter-reset:lst-ctn-kix_list_3-4 0}ol.lst-kix_list_3-4{list-style-type:none}ol.lst-kix_list_19-0.start{counter-reset:lst-ctn-kix_list_19-0 0}ol.lst-kix_list_3-0{list-style-type:none}ol.lst-kix_list_34-6{list-style-type:none}ol.lst-kix_list_34-5{list-style-type:none}ol.lst-kix_list_34-8{list-style-type:none}ol.lst-kix_list_34-7{list-style-type:none}ol.lst-kix_list_34-2{list-style-type:none}ol.lst-kix_list_34-1{list-style-type:none}ol.lst-kix_list_34-4{list-style-type:none}ol.lst-kix_list_34-3{list-style-type:none}ul.lst-kix_list_5-7{list-style-type:none}ul.lst-kix_list_5-8{list-style-type:none}ul.lst-kix_list_5-5{list-style-type:none}ol.lst-kix_list_34-0{list-style-type:none}ul.lst-kix_list_5-6{list-style-type:none}ol.lst-kix_list_32-4.start{counter-reset:lst-ctn-kix_list_32-4 0}.lst-kix_list_21-8>li:before{content:"\0025aa   "}.lst-kix_list_26-5>li:before{content:"\0025aa   "}ul.lst-kix_list_5-0{list-style-type:none}ol.lst-kix_list_18-2.start{counter-reset:lst-ctn-kix_list_18-2 0}ul.lst-kix_list_5-3{list-style-type:none}ol.lst-kix_list_3-5{list-style-type:none}ul.lst-kix_list_5-4{list-style-type:none}ol.lst-kix_list_3-6{list-style-type:none}.lst-kix_list_26-8>li:before{content:"\0025aa   "}ul.lst-kix_list_5-1{list-style-type:none}ol.lst-kix_list_3-7{list-style-type:none}ul.lst-kix_list_5-2{list-style-type:none}ol.lst-kix_list_3-8{list-style-type:none}.lst-kix_list_21-0>li:before{content:"\0025cf   "}.lst-kix_list_26-1>li:before{content:"o  "}.lst-kix_list_21-1>li:before{content:"o  "}.lst-kix_list_34-3>li{counter-increment:lst-ctn-kix_list_34-3}.lst-kix_list_26-4>li:before{content:"o  "}.lst-kix_list_21-5>li:before{content:"\0025aa   "}.lst-kix_list_21-4>li:before{content:"o  "}.lst-kix_list_26-0>li:before{content:"\0025cf   "}ul.lst-kix_list_23-0{list-style-type:none}ul.lst-kix_list_23-1{list-style-type:none}ul.lst-kix_list_23-2{list-style-type:none}ul.lst-kix_list_23-3{list-style-type:none}ul.lst-kix_list_23-4{list-style-type:none}ul.lst-kix_list_23-5{list-style-type:none}ul.lst-kix_list_23-6{list-style-type:none}ol.lst-kix_list_19-5.start{counter-reset:lst-ctn-kix_list_19-5 0}.lst-kix_list_25-1>li:before{content:"o  "}ul.lst-kix_list_23-7{list-style-type:none}.lst-kix_list_25-0>li:before{content:"\0025cf   "}ul.lst-kix_list_23-8{list-style-type:none}ul.lst-kix_list_12-6{list-style-type:none}ul.lst-kix_list_12-5{list-style-type:none}ul.lst-kix_list_12-4{list-style-type:none}ul.lst-kix_list_12-3{list-style-type:none}ul.lst-kix_list_12-2{list-style-type:none}ul.lst-kix_list_12-1{list-style-type:none}ul.lst-kix_list_12-0{list-style-type:none}ul.lst-kix_list_12-8{list-style-type:none}ol.lst-kix_list_20-6.start{counter-reset:lst-ctn-kix_list_20-6 0}ul.lst-kix_list_12-7{list-style-type:none}.lst-kix_list_34-1>li{counter-increment:lst-ctn-kix_list_34-1}.lst-kix_list_2-2>li{counter-increment:lst-ctn-kix_list_2-2}.lst-kix_list_3-7>li{counter-increment:lst-ctn-kix_list_3-7}.lst-kix_list_22-5>li:before{content:"\0025aa   "}.lst-kix_list_22-1>li:before{content:"o  "}.lst-kix_list_27-4>li:before{content:"o  "}.lst-kix_list_20-2>li{counter-increment:lst-ctn-kix_list_20-2}ul.lst-kix_list_7-5{list-style-type:none}ul.lst-kix_list_7-6{list-style-type:none}ul.lst-kix_list_7-3{list-style-type:none}ul.lst-kix_list_7-4{list-style-type:none}ul.lst-kix_list_7-7{list-style-type:none}ul.lst-kix_list_7-8{list-style-type:none}ul.lst-kix_list_7-1{list-style-type:none}.lst-kix_list_19-6>li{counter-increment:lst-ctn-kix_list_19-6}ul.lst-kix_list_7-2{list-style-type:none}.lst-kix_list_27-0>li:before{content:"\0025cf   "}ul.lst-kix_list_7-0{list-style-type:none}.lst-kix_list_20-4>li{counter-increment:lst-ctn-kix_list_20-4}li.li-bullet-3:before{margin-left:-14.2pt;white-space:nowrap;display:inline-block;min-width:14.2pt}ol.lst-kix_list_19-4.start{counter-reset:lst-ctn-kix_list_19-4 0}.lst-kix_list_32-0>li{counter-increment:lst-ctn-kix_list_32-0}ol.lst-kix_list_33-6.start{counter-reset:lst-ctn-kix_list_33-6 0}ol.lst-kix_list_2-2.start{counter-reset:lst-ctn-kix_list_2-2 0}ol.lst-kix_list_20-1.start{counter-reset:lst-ctn-kix_list_20-1 0}.lst-kix_list_25-4>li:before{content:"o  "}.lst-kix_list_19-4>li{counter-increment:lst-ctn-kix_list_19-4}.lst-kix_list_25-8>li:before{content:"\0025aa   "}ul.lst-kix_list_25-0{list-style-type:none}ul.lst-kix_list_25-1{list-style-type:none}ul.lst-kix_list_25-2{list-style-type:none}ul.lst-kix_list_25-3{list-style-type:none}ul.lst-kix_list_25-4{list-style-type:none}li.li-bullet-8:before{margin-left:-18pt;white-space:nowrap;display:inline-block;min-width:18pt}.lst-kix_list_34-8>li{counter-increment:lst-ctn-kix_list_34-8}.lst-kix_list_20-5>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) "." counter(lst-ctn-kix_list_20-1,decimal) "." counter(lst-ctn-kix_list_20-2,decimal) "." counter(lst-ctn-kix_list_20-3,decimal) "." counter(lst-ctn-kix_list_20-4,decimal) "." counter(lst-ctn-kix_list_20-5,decimal) ". "}.lst-kix_list_28-8>li:before{content:"\0025aa   "}ul.lst-kix_list_25-5{list-style-type:none}ul.lst-kix_list_25-6{list-style-type:none}.lst-kix_list_20-1>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) "." counter(lst-ctn-kix_list_20-1,decimal) ". "}ul.lst-kix_list_25-7{list-style-type:none}ul.lst-kix_list_25-8{list-style-type:none}.lst-kix_list_28-4>li:before{content:"o  "}ol.lst-kix_list_3-5.start{counter-reset:lst-ctn-kix_list_3-5 0}ul.lst-kix_list_14-4{list-style-type:none}ul.lst-kix_list_14-3{list-style-type:none}ul.lst-kix_list_14-2{list-style-type:none}ol.lst-kix_list_32-3.start{counter-reset:lst-ctn-kix_list_32-3 0}ul.lst-kix_list_14-1{list-style-type:none}ul.lst-kix_list_14-0{list-style-type:none}ul.lst-kix_list_14-8{list-style-type:none}ul.lst-kix_list_14-7{list-style-type:none}.lst-kix_list_18-1>li{counter-increment:lst-ctn-kix_list_18-1}ul.lst-kix_list_14-6{list-style-type:none}ul.lst-kix_list_14-5{list-style-type:none}.lst-kix_list_28-0>li:before{content:"\0025cf   "}.lst-kix_list_32-7>li{counter-increment:lst-ctn-kix_list_32-7}.lst-kix_list_27-8>li:before{content:"\0025aa   "}ol.lst-kix_list_20-3.start{counter-reset:lst-ctn-kix_list_20-3 0}.lst-kix_list_29-1>li{counter-increment:lst-ctn-kix_list_29-1}ol.lst-kix_list_32-7.start{counter-reset:lst-ctn-kix_list_32-7 0}.lst-kix_list_19-1>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) "." counter(lst-ctn-kix_list_19-1,decimal) ". "}ul.lst-kix_list_1-0{list-style-type:none}.lst-kix_list_19-4>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) "." counter(lst-ctn-kix_list_19-1,decimal) "." counter(lst-ctn-kix_list_19-2,decimal) "." counter(lst-ctn-kix_list_19-3,decimal) "." counter(lst-ctn-kix_list_19-4,decimal) ". "}.lst-kix_list_19-3>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) "." counter(lst-ctn-kix_list_19-1,decimal) "." counter(lst-ctn-kix_list_19-2,decimal) "." counter(lst-ctn-kix_list_19-3,decimal) ". "}.lst-kix_list_32-6>li{counter-increment:lst-ctn-kix_list_32-6}ol.lst-kix_list_29-6.start{counter-reset:lst-ctn-kix_list_29-6 0}ul.lst-kix_list_1-3{list-style-type:none}ul.lst-kix_list_1-4{list-style-type:none}ul.lst-kix_list_1-1{list-style-type:none}ul.lst-kix_list_1-2{list-style-type:none}ul.lst-kix_list_1-7{list-style-type:none}ul.lst-kix_list_1-8{list-style-type:none}ul.lst-kix_list_1-5{list-style-type:none}ul.lst-kix_list_1-6{list-style-type:none}.lst-kix_list_33-7>li{counter-increment:lst-ctn-kix_list_33-7}.lst-kix_list_19-6>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) "." counter(lst-ctn-kix_list_19-1,decimal) "." counter(lst-ctn-kix_list_19-2,decimal) "." counter(lst-ctn-kix_list_19-3,decimal) "." counter(lst-ctn-kix_list_19-4,decimal) "." counter(lst-ctn-kix_list_19-5,decimal) "." counter(lst-ctn-kix_list_19-6,decimal) ". "}.lst-kix_list_32-2>li{counter-increment:lst-ctn-kix_list_32-2}.lst-kix_list_20-5>li{counter-increment:lst-ctn-kix_list_20-5}.lst-kix_list_18-3>li{counter-increment:lst-ctn-kix_list_18-3}ol.lst-kix_list_33-4.start{counter-reset:lst-ctn-kix_list_33-4 0}.lst-kix_list_18-0>li:before{content:"" counter(lst-ctn-kix_list_18-0,decimal) ".1 "}.lst-kix_list_3-0>li{counter-increment:lst-ctn-kix_list_3-0}.lst-kix_list_18-2>li:before{content:"" counter(lst-ctn-kix_list_18-2,lower-roman) ". "}li.li-bullet-2:before{margin-left:-18pt;white-space:nowrap;display:inline-block;min-width:18pt}.lst-kix_list_30-4>li{counter-increment:lst-ctn-kix_list_30-4}.lst-kix_list_27-1>li:before{content:"o  "}.lst-kix_list_27-3>li:before{content:"\0025cf   "}.lst-kix_list_18-8>li:before{content:"" counter(lst-ctn-kix_list_18-8,lower-roman) ". "}ol.lst-kix_list_30-1.start{counter-reset:lst-ctn-kix_list_30-1 0}.lst-kix_list_10-7>li:before{content:"\0025aa   "}.lst-kix_list_20-1>li{counter-increment:lst-ctn-kix_list_20-1}.lst-kix_list_10-5>li:before{content:"\0025aa   "}ol.lst-kix_list_18-6.start{counter-reset:lst-ctn-kix_list_18-6 0}.lst-kix_list_29-3>li{counter-increment:lst-ctn-kix_list_29-3}ol.lst-kix_list_20-8{list-style-type:none}ol.lst-kix_list_20-5{list-style-type:none}ol.lst-kix_list_20-4{list-style-type:none}ol.lst-kix_list_20-7{list-style-type:none}ol.lst-kix_list_20-6{list-style-type:none}.lst-kix_list_9-2>li:before{content:"\0025aa   "}ol.lst-kix_list_20-1{list-style-type:none}ol.lst-kix_list_20-0{list-style-type:none}ol.lst-kix_list_20-3{list-style-type:none}ol.lst-kix_list_20-2{list-style-type:none}.lst-kix_list_9-0>li:before{content:"\0025cf   "}ul.lst-kix_list_21-1{list-style-type:none}ul.lst-kix_list_21-2{list-style-type:none}ul.lst-kix_list_21-3{list-style-type:none}ul.lst-kix_list_21-4{list-style-type:none}ul.lst-kix_list_21-5{list-style-type:none}ul.lst-kix_list_21-6{list-style-type:none}ul.lst-kix_list_21-7{list-style-type:none}ul.lst-kix_list_21-8{list-style-type:none}ol.lst-kix_list_33-7{list-style-type:none}ol.lst-kix_list_33-6{list-style-type:none}ol.lst-kix_list_33-8{list-style-type:none}ol.lst-kix_list_33-3{list-style-type:none}ol.lst-kix_list_33-2{list-style-type:none}.lst-kix_list_11-3>li:before{content:"\0025aa   "}ol.lst-kix_list_33-5{list-style-type:none}ol.lst-kix_list_33-4{list-style-type:none}ul.lst-kix_list_21-0{list-style-type:none}ol.lst-kix_list_18-4.start{counter-reset:lst-ctn-kix_list_18-4 0}ol.lst-kix_list_33-1{list-style-type:none}ol.lst-kix_list_33-0{list-style-type:none}ol.lst-kix_list_29-1.start{counter-reset:lst-ctn-kix_list_29-1 0}.lst-kix_list_29-1>li:before{content:"" counter(lst-ctn-kix_list_29-1,lower-latin) ". "}.lst-kix_list_20-4>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) "." counter(lst-ctn-kix_list_20-1,decimal) "." counter(lst-ctn-kix_list_20-2,decimal) "." counter(lst-ctn-kix_list_20-3,decimal) "." counter(lst-ctn-kix_list_20-4,decimal) ". "}.lst-kix_list_29-3>li:before{content:"" counter(lst-ctn-kix_list_29-3,decimal) ". "}.lst-kix_list_20-2>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) "." counter(lst-ctn-kix_list_20-1,decimal) "." counter(lst-ctn-kix_list_20-2,decimal) ". "}.lst-kix_list_9-8>li:before{content:"\0025aa   "}ul.lst-kix_list_10-0{list-style-type:none}ol.lst-kix_list_33-1.start{counter-reset:lst-ctn-kix_list_33-1 0}.lst-kix_list_28-7>li:before{content:"o  "}ul.lst-kix_list_10-8{list-style-type:none}ul.lst-kix_list_10-7{list-style-type:none}.lst-kix_list_1-7>li:before{content:"\0025aa   "}ul.lst-kix_list_10-6{list-style-type:none}ul.lst-kix_list_10-5{list-style-type:none}ul.lst-kix_list_10-4{list-style-type:none}ul.lst-kix_list_10-3{list-style-type:none}.lst-kix_list_1-5>li:before{content:"\0025aa   "}ul.lst-kix_list_10-2{list-style-type:none}.lst-kix_list_28-5>li:before{content:"\0025aa   "}ul.lst-kix_list_10-1{list-style-type:none}.lst-kix_list_2-1>li:before{content:"" counter(lst-ctn-kix_list_2-1,decimal) ". "}.lst-kix_list_19-8>li{counter-increment:lst-ctn-kix_list_19-8}.lst-kix_list_2-3>li:before{content:"" counter(lst-ctn-kix_list_2-3,decimal) ". "}.lst-kix_list_30-4>li:before{content:"" counter(lst-ctn-kix_list_30-4,lower-latin) ". "}.lst-kix_list_20-8>li{counter-increment:lst-ctn-kix_list_20-8}.lst-kix_list_3-2>li:before{content:"" counter(lst-ctn-kix_list_3-2,decimal) ". "}.lst-kix_list_26-7>li:before{content:"o  "}.lst-kix_list_8-1>li:before{content:"o  "}.lst-kix_list_3-5>li:before{content:"" counter(lst-ctn-kix_list_3-5,decimal) ". "}.lst-kix_list_18-0>li{counter-increment:lst-ctn-kix_list_18-0}.lst-kix_list_30-7>li:before{content:"" counter(lst-ctn-kix_list_30-7,lower-latin) ". "}ol.lst-kix_list_30-8.start{counter-reset:lst-ctn-kix_list_30-8 0}.lst-kix_list_8-6>li:before{content:"\0025aa   "}.lst-kix_list_26-2>li:before{content:"\0025aa   "}.lst-kix_list_21-6>li:before{content:"\0025cf   "}.lst-kix_list_33-4>li{counter-increment:lst-ctn-kix_list_33-4}.lst-kix_list_21-3>li:before{content:"\0025cf   "}ol.lst-kix_list_18-7.start{counter-reset:lst-ctn-kix_list_18-7 0}ol.lst-kix_list_30-2.start{counter-reset:lst-ctn-kix_list_30-2 0}ol.lst-kix_list_29-4.start{counter-reset:lst-ctn-kix_list_29-4 0}.lst-kix_list_17-1>li:before{content:"o  "}.lst-kix_list_25-3>li:before{content:"\0025cf   "}.lst-kix_list_32-3>li{counter-increment:lst-ctn-kix_list_32-3}li.li-bullet-10:before{margin-left:-17.9pt;white-space:nowrap;display:inline-block;min-width:17.9pt}.lst-kix_list_16-2>li:before{content:"\0025aa   "}.lst-kix_list_16-5>li:before{content:"\0025aa   "}ol.lst-kix_list_30-3.start{counter-reset:lst-ctn-kix_list_30-3 0}.lst-kix_list_30-7>li{counter-increment:lst-ctn-kix_list_30-7}ol.lst-kix_list_18-8.start{counter-reset:lst-ctn-kix_list_18-8 0}ol.lst-kix_list_29-3.start{counter-reset:lst-ctn-kix_list_29-3 0}.lst-kix_list_3-3>li{counter-increment:lst-ctn-kix_list_3-3}.lst-kix_list_17-6>li:before{content:"\0025aa   "}.lst-kix_list_2-6>li:before{content:"" counter(lst-ctn-kix_list_2-6,decimal) ". "}.lst-kix_list_7-5>li:before{content:"\0025aa   "}.lst-kix_list_27-6>li:before{content:"\0025cf   "}.lst-kix_list_19-5>li{counter-increment:lst-ctn-kix_list_19-5}.lst-kix_list_22-7>li:before{content:"o  "}ol.lst-kix_list_30-4.start{counter-reset:lst-ctn-kix_list_30-4 0}.lst-kix_list_30-0>li{counter-increment:lst-ctn-kix_list_30-0}.lst-kix_list_34-7>li:before{content:"" counter(lst-ctn-kix_list_34-7,lower-latin) ". "}.lst-kix_list_18-5>li:before{content:"" counter(lst-ctn-kix_list_18-5,lower-roman) ". "}.lst-kix_list_13-6>li:before{content:"\0025aa   "}.lst-kix_list_15-6>li:before{content:"\0025aa   "}.lst-kix_list_29-0>li{counter-increment:lst-ctn-kix_list_29-0}.lst-kix_list_31-3>li:before{content:"\0025cf   "}.lst-kix_list_10-2>li:before{content:"\0025aa   "}.lst-kix_list_20-7>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) "." counter(lst-ctn-kix_list_20-1,decimal) "." counter(lst-ctn-kix_list_20-2,decimal) "." counter(lst-ctn-kix_list_20-3,decimal) "." counter(lst-ctn-kix_list_20-4,decimal) "." counter(lst-ctn-kix_list_20-5,decimal) "." counter(lst-ctn-kix_list_20-6,decimal) "." counter(lst-ctn-kix_list_20-7,decimal) ". "}.lst-kix_list_4-6>li:before{content:"\0025aa   "}ol.lst-kix_list_29-8.start{counter-reset:lst-ctn-kix_list_29-8 0}.lst-kix_list_25-6>li:before{content:"\0025cf   "}ol.lst-kix_list_29-5.start{counter-reset:lst-ctn-kix_list_29-5 0}.lst-kix_list_34-2>li{counter-increment:lst-ctn-kix_list_34-2}.lst-kix_list_9-5>li:before{content:"\0025aa   "}.lst-kix_list_29-6>li:before{content:"" counter(lst-ctn-kix_list_29-6,decimal) ". "}li.li-bullet-5:before{margin-left:-36pt;white-space:nowrap;display:inline-block;min-width:36pt}.lst-kix_list_33-3>li:before{content:"" counter(lst-ctn-kix_list_33-3,decimal) ". "}.lst-kix_list_12-2>li:before{content:"\0025aa   "}.lst-kix_list_11-6>li:before{content:"\0025aa   "}.lst-kix_list_32-7>li:before{content:"" counter(lst-ctn-kix_list_32-7,lower-latin) ". "}ol.lst-kix_list_30-6.start{counter-reset:lst-ctn-kix_list_30-6 0}.lst-kix_list_1-2>li:before{content:"\0025aa   "}li.li-bullet-0:before{margin-left:-28.4pt;white-space:nowrap;display:inline-block;min-width:28.4pt}ol.lst-kix_list_29-7.start{counter-reset:lst-ctn-kix_list_29-7 0}.lst-kix_list_18-7>li{counter-increment:lst-ctn-kix_list_18-7}.lst-kix_list_29-7>li{counter-increment:lst-ctn-kix_list_29-7}ol.lst-kix_list_30-7.start{counter-reset:lst-ctn-kix_list_30-7 0}.lst-kix_list_28-2>li:before{content:"\0025aa   "}.lst-kix_list_14-1>li:before{content:"o  "}.lst-kix_list_14-3>li:before{content:"\0025aa   "}ul.lst-kix_list_28-0{list-style-type:none}.lst-kix_list_14-0>li:before{content:"\0025cf   "}.lst-kix_list_14-4>li:before{content:"\0025aa   "}ul.lst-kix_list_28-1{list-style-type:none}ol.lst-kix_list_18-5.start{counter-reset:lst-ctn-kix_list_18-5 0}.lst-kix_list_14-5>li:before{content:"\0025aa   "}.lst-kix_list_14-7>li:before{content:"\0025aa   "}.lst-kix_list_14-6>li:before{content:"\0025aa   "}.lst-kix_list_34-6>li{counter-increment:lst-ctn-kix_list_34-6}ul.lst-kix_list_28-2{list-style-type:none}ul.lst-kix_list_28-3{list-style-type:none}ul.lst-kix_list_28-4{list-style-type:none}ul.lst-kix_list_28-5{list-style-type:none}ul.lst-kix_list_28-6{list-style-type:none}ul.lst-kix_list_28-7{list-style-type:none}ul.lst-kix_list_28-8{list-style-type:none}.lst-kix_list_14-2>li:before{content:"\0025aa   "}ol.lst-kix_list_32-1.start{counter-reset:lst-ctn-kix_list_32-1 0}.lst-kix_list_20-7>li{counter-increment:lst-ctn-kix_list_20-7}ul.lst-kix_list_17-1{list-style-type:none}ul.lst-kix_list_17-0{list-style-type:none}.lst-kix_list_32-2>li:before{content:"" counter(lst-ctn-kix_list_32-2,lower-roman) ". "}ul.lst-kix_list_17-8{list-style-type:none}.lst-kix_list_32-1>li:before{content:"" counter(lst-ctn-kix_list_32-1,lower-latin) ". "}.lst-kix_list_32-3>li:before{content:"" counter(lst-ctn-kix_list_32-3,decimal) ". "}ul.lst-kix_list_17-7{list-style-type:none}ul.lst-kix_list_17-6{list-style-type:none}ul.lst-kix_list_17-5{list-style-type:none}ol.lst-kix_list_3-7.start{counter-reset:lst-ctn-kix_list_3-7 0}ul.lst-kix_list_17-4{list-style-type:none}ul.lst-kix_list_17-3{list-style-type:none}.lst-kix_list_14-8>li:before{content:"\0025aa   "}ul.lst-kix_list_17-2{list-style-type:none}.lst-kix_list_32-0>li:before{content:"" counter(lst-ctn-kix_list_32-0,decimal) ". "}.lst-kix_list_3-2>li{counter-increment:lst-ctn-kix_list_3-2}.lst-kix_list_5-0>li:before{content:"\0025cf   "}.lst-kix_list_5-3>li:before{content:"\0025aa   "}.lst-kix_list_5-2>li:before{content:"\0025aa   "}.lst-kix_list_5-1>li:before{content:"o  "}ol.lst-kix_list_18-0.start{counter-reset:lst-ctn-kix_list_18-0 0}.lst-kix_list_5-7>li:before{content:"\0025aa   "}ul.lst-kix_list_8-4{list-style-type:none}ul.lst-kix_list_8-5{list-style-type:none}.lst-kix_list_5-6>li:before{content:"\0025aa   "}.lst-kix_list_5-8>li:before{content:"\0025aa   "}ul.lst-kix_list_8-2{list-style-type:none}ul.lst-kix_list_8-3{list-style-type:none}ul.lst-kix_list_8-8{list-style-type:none}ul.lst-kix_list_8-6{list-style-type:none}ul.lst-kix_list_8-7{list-style-type:none}.lst-kix_list_5-4>li:before{content:"\0025aa   "}.lst-kix_list_5-5>li:before{content:"\0025aa   "}ul.lst-kix_list_8-0{list-style-type:none}ul.lst-kix_list_8-1{list-style-type:none}.lst-kix_list_6-1>li:before{content:"o  "}.lst-kix_list_6-3>li:before{content:"\0025aa   "}ol.lst-kix_list_32-6.start{counter-reset:lst-ctn-kix_list_32-6 0}.lst-kix_list_6-0>li:before{content:"\0025cf   "}.lst-kix_list_6-4>li:before{content:"\0025aa   "}.lst-kix_list_6-2>li:before{content:"\0025aa   "}.lst-kix_list_2-5>li{counter-increment:lst-ctn-kix_list_2-5}ol.lst-kix_list_3-2.start{counter-reset:lst-ctn-kix_list_3-2 0}.lst-kix_list_6-8>li:before{content:"\0025aa   "}.lst-kix_list_6-5>li:before{content:"\0025aa   "}.lst-kix_list_6-7>li:before{content:"\0025aa   "}.lst-kix_list_6-6>li:before{content:"\0025aa   "}.lst-kix_list_7-4>li:before{content:"\0025aa   "}.lst-kix_list_7-6>li:before{content:"\0025aa   "}.lst-kix_list_18-5>li{counter-increment:lst-ctn-kix_list_18-5}ol.lst-kix_list_19-7.start{counter-reset:lst-ctn-kix_list_19-7 0}.lst-kix_list_7-2>li:before{content:"\0025aa   "}.lst-kix_list_34-8>li:before{content:"" counter(lst-ctn-kix_list_34-8,lower-roman) ". "}.lst-kix_list_31-0>li:before{content:"\0025cf   "}.lst-kix_list_13-7>li:before{content:"\0025aa   "}ol.lst-kix_list_34-4.start{counter-reset:lst-ctn-kix_list_34-4 0}.lst-kix_list_7-8>li:before{content:"\0025aa   "}ol.lst-kix_list_2-5.start{counter-reset:lst-ctn-kix_list_2-5 0}.lst-kix_list_15-5>li:before{content:"\0025aa   "}.lst-kix_list_31-6>li:before{content:"\0025cf   "}.lst-kix_list_31-8>li:before{content:"\0025aa   "}.lst-kix_list_4-1>li:before{content:"o  "}.lst-kix_list_31-2>li:before{content:"\0025aa   "}.lst-kix_list_31-4>li:before{content:"o  "}.lst-kix_list_15-7>li:before{content:"\0025aa   "}ol.lst-kix_list_33-3.start{counter-reset:lst-ctn-kix_list_33-3 0}.lst-kix_list_4-3>li:before{content:"\0025aa   "}.lst-kix_list_4-5>li:before{content:"\0025aa   "}.lst-kix_list_15-1>li:before{content:"o  "}.lst-kix_list_33-1>li{counter-increment:lst-ctn-kix_list_33-1}.lst-kix_list_15-3>li:before{content:"\0025aa   "}.lst-kix_list_33-2>li{counter-increment:lst-ctn-kix_list_33-2}.lst-kix_list_30-2>li{counter-increment:lst-ctn-kix_list_30-2}.lst-kix_list_20-0>li{counter-increment:lst-ctn-kix_list_20-0}.lst-kix_list_32-4>li:before{content:"" counter(lst-ctn-kix_list_32-4,lower-latin) ". "}.lst-kix_list_19-2>li{counter-increment:lst-ctn-kix_list_19-2}.lst-kix_list_33-8>li{counter-increment:lst-ctn-kix_list_33-8}.lst-kix_list_33-4>li:before{content:"" counter(lst-ctn-kix_list_33-4,lower-latin) ". "}.lst-kix_list_30-3>li{counter-increment:lst-ctn-kix_list_30-3}.lst-kix_list_12-3>li:before{content:"\0025aa   "}.lst-kix_list_32-6>li:before{content:"" counter(lst-ctn-kix_list_32-6,decimal) ". "}.lst-kix_list_12-1>li:before{content:"o  "}.lst-kix_list_33-0>li:before{content:"(" counter(lst-ctn-kix_list_33-0,lower-roman) ") "}.lst-kix_list_33-2>li:before{content:"" counter(lst-ctn-kix_list_33-2,lower-roman) ". "}.lst-kix_list_32-8>li:before{content:"" counter(lst-ctn-kix_list_32-8,lower-roman) ". "}.lst-kix_list_34-0>li:before{content:"(" counter(lst-ctn-kix_list_34-0,lower-roman) ") "}.lst-kix_list_13-3>li:before{content:"\0025aa   "}.lst-kix_list_34-4>li:before{content:"" counter(lst-ctn-kix_list_34-4,lower-latin) ". "}.lst-kix_list_34-6>li:before{content:"" counter(lst-ctn-kix_list_34-6,decimal) ". "}li.li-bullet-4:before{margin-left:-14.2pt;white-space:nowrap;display:inline-block;min-width:14.2pt}.lst-kix_list_13-5>li:before{content:"\0025aa   "}.lst-kix_list_12-5>li:before{content:"\0025aa   "}.lst-kix_list_18-4>li{counter-increment:lst-ctn-kix_list_18-4}.lst-kix_list_12-7>li:before{content:"\0025aa   "}.lst-kix_list_29-4>li{counter-increment:lst-ctn-kix_list_29-4}.lst-kix_list_33-6>li:before{content:"" counter(lst-ctn-kix_list_33-6,decimal) ". "}.lst-kix_list_32-4>li{counter-increment:lst-ctn-kix_list_32-4}.lst-kix_list_33-8>li:before{content:"" counter(lst-ctn-kix_list_33-8,lower-roman) ". "}ol.lst-kix_list_29-0.start{counter-reset:lst-ctn-kix_list_29-0 0}.lst-kix_list_34-2>li:before{content:"" counter(lst-ctn-kix_list_34-2,lower-roman) ". "}.lst-kix_list_13-1>li:before{content:"o  "}.lst-kix_list_32-5>li{counter-increment:lst-ctn-kix_list_32-5}.lst-kix_list_34-5>li{counter-increment:lst-ctn-kix_list_34-5}ul.lst-kix_list_24-0{list-style-type:none}.lst-kix_list_30-5>li:before{content:"" counter(lst-ctn-kix_list_30-5,lower-roman) ". "}ul.lst-kix_list_24-1{list-style-type:none}ul.lst-kix_list_24-2{list-style-type:none}ul.lst-kix_list_24-3{list-style-type:none}ul.lst-kix_list_24-4{list-style-type:none}ul.lst-kix_list_24-5{list-style-type:none}ol.lst-kix_list_33-2.start{counter-reset:lst-ctn-kix_list_33-2 0}.lst-kix_list_30-1>li:before{content:"" counter(lst-ctn-kix_list_30-1,lower-latin) ". "}ol.lst-kix_list_2-6.start{counter-reset:lst-ctn-kix_list_2-6 0}.lst-kix_list_3-0>li:before{content:"" counter(lst-ctn-kix_list_3-0,decimal) ". "}.lst-kix_list_30-2>li:before{content:"" counter(lst-ctn-kix_list_30-2,lower-roman) ". "}ol.lst-kix_list_20-5.start{counter-reset:lst-ctn-kix_list_20-5 0}ul.lst-kix_list_24-6{list-style-type:none}ul.lst-kix_list_24-7{list-style-type:none}.lst-kix_list_3-4>li:before{content:"" counter(lst-ctn-kix_list_3-4,decimal) ". "}ul.lst-kix_list_24-8{list-style-type:none}.lst-kix_list_3-3>li:before{content:"" counter(lst-ctn-kix_list_3-3,decimal) ". "}.lst-kix_list_8-0>li:before{content:"\0025cf   "}.lst-kix_list_30-6>li:before{content:"" counter(lst-ctn-kix_list_30-6,decimal) ". "}.lst-kix_list_8-7>li:before{content:"\0025aa   "}.lst-kix_list_3-8>li:before{content:"" counter(lst-ctn-kix_list_3-8,decimal) ". "}.lst-kix_list_8-3>li:before{content:"\0025aa   "}ul.lst-kix_list_13-5{list-style-type:none}ul.lst-kix_list_13-4{list-style-type:none}ul.lst-kix_list_13-3{list-style-type:none}ul.lst-kix_list_13-2{list-style-type:none}ul.lst-kix_list_13-1{list-style-type:none}.lst-kix_list_3-7>li:before{content:"" counter(lst-ctn-kix_list_3-7,decimal) ". "}ul.lst-kix_list_13-0{list-style-type:none}.lst-kix_list_8-4>li:before{content:"\0025aa   "}.lst-kix_list_19-1>li{counter-increment:lst-ctn-kix_list_19-1}ul.lst-kix_list_13-8{list-style-type:none}.lst-kix_list_11-1>li:before{content:"o  "}ul.lst-kix_list_13-7{list-style-type:none}ul.lst-kix_list_13-6{list-style-type:none}.lst-kix_list_11-0>li:before{content:"\0025cf   "}.lst-kix_list_8-8>li:before{content:"\0025aa   "}ol.lst-kix_list_2-2{list-style-type:none}.lst-kix_list_16-8>li:before{content:"\0025aa   "}ol.lst-kix_list_2-3{list-style-type:none}ol.lst-kix_list_2-4{list-style-type:none}.lst-kix_list_16-7>li:before{content:"\0025aa   "}ol.lst-kix_list_2-5{list-style-type:none}ol.lst-kix_list_2-0{list-style-type:none}ol.lst-kix_list_2-1{list-style-type:none}.lst-kix_list_4-8>li:before{content:"\0025aa   "}.lst-kix_list_4-7>li:before{content:"\0025aa   "}ol.lst-kix_list_20-0.start{counter-reset:lst-ctn-kix_list_20-0 0}.lst-kix_list_17-0>li:before{content:"\0025cf   "}li.li-bullet-7:before{margin-left:-36pt;white-space:nowrap;display:inline-block;min-width:36pt}ul.lst-kix_list_4-8{list-style-type:none}.lst-kix_list_16-0>li:before{content:"\0025cf   "}ul.lst-kix_list_4-6{list-style-type:none}ul.lst-kix_list_4-7{list-style-type:none}ul.lst-kix_list_4-0{list-style-type:none}.lst-kix_list_16-4>li:before{content:"\0025aa   "}ul.lst-kix_list_4-1{list-style-type:none}ol.lst-kix_list_3-3.start{counter-reset:lst-ctn-kix_list_3-3 0}.lst-kix_list_16-3>li:before{content:"\0025aa   "}ul.lst-kix_list_4-4{list-style-type:none}ol.lst-kix_list_2-6{list-style-type:none}ul.lst-kix_list_4-5{list-style-type:none}ol.lst-kix_list_2-7{list-style-type:none}ul.lst-kix_list_4-2{list-style-type:none}ol.lst-kix_list_2-8{list-style-type:none}ul.lst-kix_list_4-3{list-style-type:none}ol.lst-kix_list_18-1.start{counter-reset:lst-ctn-kix_list_18-1 0}.lst-kix_list_30-1>li{counter-increment:lst-ctn-kix_list_30-1}.lst-kix_list_17-7>li:before{content:"\0025aa   "}.lst-kix_list_17-8>li:before{content:"\0025aa   "}.lst-kix_list_33-0>li{counter-increment:lst-ctn-kix_list_33-0}.lst-kix_list_17-3>li:before{content:"\0025aa   "}.lst-kix_list_17-4>li:before{content:"\0025aa   "}.lst-kix_list_7-0>li:before{content:"\0025cf   "}ol.lst-kix_list_19-6.start{counter-reset:lst-ctn-kix_list_19-6 0}ul.lst-kix_list_26-0{list-style-type:none}.lst-kix_list_2-4>li:before{content:"" counter(lst-ctn-kix_list_2-4,decimal) ". "}.lst-kix_list_2-8>li:before{content:"" counter(lst-ctn-kix_list_2-8,decimal) ". "}ul.lst-kix_list_26-1{list-style-type:none}ul.lst-kix_list_26-2{list-style-type:none}ul.lst-kix_list_26-3{list-style-type:none}.lst-kix_list_7-3>li:before{content:"\0025aa   "}.lst-kix_list_10-0>li:before{content:"\0025cf   "}.lst-kix_list_13-8>li:before{content:"\0025aa   "}.lst-kix_list_31-1>li:before{content:"o  "}.lst-kix_list_18-3>li:before{content:"" counter(lst-ctn-kix_list_18-3,decimal) ". "}.lst-kix_list_18-7>li:before{content:"" counter(lst-ctn-kix_list_18-7,lower-latin) ". "}ul.lst-kix_list_26-4{list-style-type:none}ul.lst-kix_list_26-5{list-style-type:none}ul.lst-kix_list_26-6{list-style-type:none}.lst-kix_list_18-6>li{counter-increment:lst-ctn-kix_list_18-6}ul.lst-kix_list_26-7{list-style-type:none}ul.lst-kix_list_26-8{list-style-type:none}ol.lst-kix_list_3-8.start{counter-reset:lst-ctn-kix_list_3-8 0}.lst-kix_list_7-7>li:before{content:"\0025aa   "}.lst-kix_list_15-4>li:before{content:"\0025aa   "}.lst-kix_list_31-5>li:before{content:"\0025aa   "}ol.lst-kix_list_19-1.start{counter-reset:lst-ctn-kix_list_19-1 0}.lst-kix_list_10-4>li:before{content:"\0025aa   "}.lst-kix_list_10-8>li:before{content:"\0025aa   "}ol.lst-kix_list_20-4.start{counter-reset:lst-ctn-kix_list_20-4 0}.lst-kix_list_4-0>li:before{content:"\0025cf   "}ul.lst-kix_list_15-3{list-style-type:none}ul.lst-kix_list_15-2{list-style-type:none}.lst-kix_list_15-0>li:before{content:"\0025cf   "}ul.lst-kix_list_15-1{list-style-type:none}.lst-kix_list_15-8>li:before{content:"\0025aa   "}ul.lst-kix_list_15-0{list-style-type:none}.lst-kix_list_4-4>li:before{content:"\0025aa   "}ul.lst-kix_list_15-8{list-style-type:none}ul.lst-kix_list_15-7{list-style-type:none}ul.lst-kix_list_15-6{list-style-type:none}.lst-kix_list_9-3>li:before{content:"\0025aa   "}ul.lst-kix_list_15-5{list-style-type:none}ul.lst-kix_list_15-4{list-style-type:none}ol.lst-kix_list_32-5.start{counter-reset:lst-ctn-kix_list_32-5 0}ol.lst-kix_list_33-7.start{counter-reset:lst-ctn-kix_list_33-7 0}.lst-kix_list_9-7>li:before{content:"\0025aa   "}.lst-kix_list_2-4>li{counter-increment:lst-ctn-kix_list_2-4}.lst-kix_list_29-4>li:before{content:"" counter(lst-ctn-kix_list_29-4,lower-latin) ". "}.lst-kix_list_29-8>li:before{content:"" counter(lst-ctn-kix_list_29-8,lower-roman) ". "}ol.lst-kix_list_3-6.start{counter-reset:lst-ctn-kix_list_3-6 0}ol.lst-kix_list_32-2.start{counter-reset:lst-ctn-kix_list_32-2 0}.lst-kix_list_32-5>li:before{content:"" counter(lst-ctn-kix_list_32-5,lower-roman) ". "}.lst-kix_list_11-4>li:before{content:"\0025aa   "}.lst-kix_list_12-4>li:before{content:"\0025aa   "}ul.lst-kix_list_6-6{list-style-type:none}li.li-bullet-12:before{margin-left:-17.9pt;white-space:nowrap;display:inline-block;min-width:17.9pt}ul.lst-kix_list_6-7{list-style-type:none}ul.lst-kix_list_6-4{list-style-type:none}ul.lst-kix_list_6-5{list-style-type:none}ul.lst-kix_list_6-8{list-style-type:none}.lst-kix_list_29-0>li:before{content:"" counter(lst-ctn-kix_list_29-0,lower-latin) ") "}.lst-kix_list_33-1>li:before{content:"" counter(lst-ctn-kix_list_33-1,lower-latin) ". "}.lst-kix_list_1-0>li:before{content:"\0025cf   "}ol.lst-kix_list_19-2.start{counter-reset:lst-ctn-kix_list_19-2 0}ul.lst-kix_list_6-2{list-style-type:none}.lst-kix_list_11-8>li:before{content:"\0025aa   "}ul.lst-kix_list_6-3{list-style-type:none}ol.lst-kix_list_2-0.start{counter-reset:lst-ctn-kix_list_2-0 0}ul.lst-kix_list_6-0{list-style-type:none}.lst-kix_list_12-0>li:before{content:"\0025cf   "}ul.lst-kix_list_6-1{list-style-type:none}.lst-kix_list_1-4>li:before{content:"\0025aa   "}.lst-kix_list_13-0>li:before{content:"\0025cf   "}.lst-kix_list_13-4>li:before{content:"\0025aa   "}.lst-kix_list_34-5>li:before{content:"" counter(lst-ctn-kix_list_34-5,lower-roman) ". "}.lst-kix_list_33-5>li:before{content:"" counter(lst-ctn-kix_list_33-5,lower-roman) ". "}ol.lst-kix_list_19-3.start{counter-reset:lst-ctn-kix_list_19-3 0}.lst-kix_list_2-0>li:before{content:"" counter(lst-ctn-kix_list_2-0,decimal) ". "}ol.lst-kix_list_2-1.start{counter-reset:lst-ctn-kix_list_2-1 0}ol.lst-kix_list_33-8.start{counter-reset:lst-ctn-kix_list_33-8 0}.lst-kix_list_1-8>li:before{content:"\0025aa   "}.lst-kix_list_34-1>li:before{content:"" counter(lst-ctn-kix_list_34-1,lower-latin) ". "}.lst-kix_list_12-8>li:before{content:"\0025aa   "}.lst-kix_list_19-0>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) ". "}ol.lst-kix_list_30-6{list-style-type:none}ol.lst-kix_list_30-5{list-style-type:none}.lst-kix_list_19-2>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) "." counter(lst-ctn-kix_list_19-1,decimal) "." counter(lst-ctn-kix_list_19-2,decimal) ". "}ol.lst-kix_list_30-8{list-style-type:none}ol.lst-kix_list_3-1.start{counter-reset:lst-ctn-kix_list_3-1 0}ol.lst-kix_list_30-7{list-style-type:none}ol.lst-kix_list_30-2{list-style-type:none}ol.lst-kix_list_30-1{list-style-type:none}ol.lst-kix_list_30-4{list-style-type:none}ol.lst-kix_list_30-3{list-style-type:none}ol.lst-kix_list_30-0{list-style-type:none}.lst-kix_list_19-0>li{counter-increment:lst-ctn-kix_list_19-0}.lst-kix_list_29-6>li{counter-increment:lst-ctn-kix_list_29-6}.lst-kix_list_2-3>li{counter-increment:lst-ctn-kix_list_2-3}ol.lst-kix_list_19-8.start{counter-reset:lst-ctn-kix_list_19-8 0}.lst-kix_list_19-8>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) "." counter(lst-ctn-kix_list_19-1,decimal) "." counter(lst-ctn-kix_list_19-2,decimal) "." counter(lst-ctn-kix_list_19-3,decimal) "." counter(lst-ctn-kix_list_19-4,decimal) "." counter(lst-ctn-kix_list_19-5,decimal) "." counter(lst-ctn-kix_list_19-6,decimal) "." counter(lst-ctn-kix_list_19-7,decimal) "." counter(lst-ctn-kix_list_19-8,decimal) ". "}ol.lst-kix_list_20-8.start{counter-reset:lst-ctn-kix_list_20-8 0}.lst-kix_list_19-5>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) "." counter(lst-ctn-kix_list_19-1,decimal) "." counter(lst-ctn-kix_list_19-2,decimal) "." counter(lst-ctn-kix_list_19-3,decimal) "." counter(lst-ctn-kix_list_19-4,decimal) "." counter(lst-ctn-kix_list_19-5,decimal) ". "}ol.lst-kix_list_34-8.start{counter-reset:lst-ctn-kix_list_34-8 0}.lst-kix_list_19-7>li:before{content:"" counter(lst-ctn-kix_list_19-0,decimal) "." counter(lst-ctn-kix_list_19-1,decimal) "." counter(lst-ctn-kix_list_19-2,decimal) "." counter(lst-ctn-kix_list_19-3,decimal) "." counter(lst-ctn-kix_list_19-4,decimal) "." counter(lst-ctn-kix_list_19-5,decimal) "." counter(lst-ctn-kix_list_19-6,decimal) "." counter(lst-ctn-kix_list_19-7,decimal) ". "}li.li-bullet-6:before{margin-left:-36pt;white-space:nowrap;display:inline-block;min-width:36pt}.lst-kix_list_19-7>li{counter-increment:lst-ctn-kix_list_19-7}ul.lst-kix_list_31-2{list-style-type:none}ul.lst-kix_list_31-3{list-style-type:none}ul.lst-kix_list_31-4{list-style-type:none}ul.lst-kix_list_31-5{list-style-type:none}ul.lst-kix_list_31-6{list-style-type:none}ul.lst-kix_list_31-7{list-style-type:none}ul.lst-kix_list_31-8{list-style-type:none}.lst-kix_list_18-1>li:before{content:"" counter(lst-ctn-kix_list_18-1,lower-latin) ". "}.lst-kix_list_33-3>li{counter-increment:lst-ctn-kix_list_33-3}ul.lst-kix_list_31-0{list-style-type:none}ul.lst-kix_list_31-1{list-style-type:none}ol.lst-kix_list_32-0.start{counter-reset:lst-ctn-kix_list_32-0 0}ol.lst-kix_list_34-3.start{counter-reset:lst-ctn-kix_list_34-3 0}ol.lst-kix_list_2-4.start{counter-reset:lst-ctn-kix_list_2-4 0}.lst-kix_list_34-4>li{counter-increment:lst-ctn-kix_list_34-4}ul.lst-kix_list_22-0{list-style-type:none}.lst-kix_list_2-7>li:before{content:"" counter(lst-ctn-kix_list_2-7,decimal) ". "}.lst-kix_list_2-7>li{counter-increment:lst-ctn-kix_list_2-7}ul.lst-kix_list_22-1{list-style-type:none}ul.lst-kix_list_22-2{list-style-type:none}ul.lst-kix_list_22-3{list-style-type:none}ul.lst-kix_list_22-4{list-style-type:none}.lst-kix_list_2-5>li:before{content:"" counter(lst-ctn-kix_list_2-5,decimal) ". "}ul.lst-kix_list_22-5{list-style-type:none}ul.lst-kix_list_22-6{list-style-type:none}ul.lst-kix_list_22-7{list-style-type:none}ol.lst-kix_list_32-8{list-style-type:none}ol.lst-kix_list_32-7{list-style-type:none}.lst-kix_list_27-5>li:before{content:"\0025aa   "}ol.lst-kix_list_32-4{list-style-type:none}ol.lst-kix_list_32-3{list-style-type:none}ol.lst-kix_list_32-6{list-style-type:none}ol.lst-kix_list_32-5{list-style-type:none}ol.lst-kix_list_32-0{list-style-type:none}.lst-kix_list_18-6>li:before{content:"" counter(lst-ctn-kix_list_18-6,decimal) ". "}ol.lst-kix_list_32-2{list-style-type:none}ol.lst-kix_list_32-1{list-style-type:none}.lst-kix_list_10-1>li:before{content:"o  "}.lst-kix_list_18-4>li:before{content:"" counter(lst-ctn-kix_list_18-4,lower-latin) ". "}ul.lst-kix_list_22-8{list-style-type:none}li.li-bullet-1:before{margin-left:-18pt;white-space:nowrap;display:inline-block;min-width:18pt}.lst-kix_list_10-3>li:before{content:"\0025aa   "}ul.lst-kix_list_11-7{list-style-type:none}ul.lst-kix_list_11-6{list-style-type:none}.lst-kix_list_2-6>li{counter-increment:lst-ctn-kix_list_2-6}ul.lst-kix_list_11-5{list-style-type:none}ul.lst-kix_list_11-4{list-style-type:none}ul.lst-kix_list_11-3{list-style-type:none}ul.lst-kix_list_11-2{list-style-type:none}ul.lst-kix_list_11-1{list-style-type:none}ul.lst-kix_list_11-0{list-style-type:none}.lst-kix_list_34-0>li{counter-increment:lst-ctn-kix_list_34-0}ul.lst-kix_list_11-8{list-style-type:none}.lst-kix_list_20-8>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) "." counter(lst-ctn-kix_list_20-1,decimal) "." counter(lst-ctn-kix_list_20-2,decimal) "." counter(lst-ctn-kix_list_20-3,decimal) "." counter(lst-ctn-kix_list_20-4,decimal) "." counter(lst-ctn-kix_list_20-5,decimal) "." counter(lst-ctn-kix_list_20-6,decimal) "." counter(lst-ctn-kix_list_20-7,decimal) "." counter(lst-ctn-kix_list_20-8,decimal) ". "}.lst-kix_list_3-4>li{counter-increment:lst-ctn-kix_list_3-4}.lst-kix_list_29-7>li:before{content:"" counter(lst-ctn-kix_list_29-7,lower-latin) ". "}.lst-kix_list_29-5>li:before{content:"" counter(lst-ctn-kix_list_29-5,lower-roman) ". "}.lst-kix_list_20-0>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) ". "}.lst-kix_list_9-6>li:before{content:"\0025aa   "}li.li-bullet-9:before{margin-left:-18pt;white-space:nowrap;display:inline-block;min-width:18pt}.lst-kix_list_9-4>li:before{content:"\0025aa   "}.lst-kix_list_30-8>li{counter-increment:lst-ctn-kix_list_30-8}ol.lst-kix_list_34-5.start{counter-reset:lst-ctn-kix_list_34-5 0}.lst-kix_list_20-6>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) "." counter(lst-ctn-kix_list_20-1,decimal) "." counter(lst-ctn-kix_list_20-2,decimal) "." counter(lst-ctn-kix_list_20-3,decimal) "." counter(lst-ctn-kix_list_20-4,decimal) "." counter(lst-ctn-kix_list_20-5,decimal) "." counter(lst-ctn-kix_list_20-6,decimal) ". "}li.li-bullet-11:before{margin-left:-17.9pt;white-space:nowrap;display:inline-block;min-width:17.9pt}.lst-kix_list_11-5>li:before{content:"\0025aa   "}.lst-kix_list_20-6>li{counter-increment:lst-ctn-kix_list_20-6}.lst-kix_list_1-1>li:before{content:"o  "}.lst-kix_list_11-7>li:before{content:"\0025aa   "}.lst-kix_list_1-3>li:before{content:"\0025aa   "}ol.lst-kix_list_34-6.start{counter-reset:lst-ctn-kix_list_34-6 0}.lst-kix_list_28-3>li:before{content:"\0025cf   "}ol.lst-kix_list_2-7.start{counter-reset:lst-ctn-kix_list_2-7 0}.lst-kix_list_27-7>li:before{content:"o  "}.lst-kix_list_28-1>li:before{content:"o  "}ol.lst-kix_list_19-6{list-style-type:none}ol.lst-kix_list_19-7{list-style-type:none}.lst-kix_list_3-1>li{counter-increment:lst-ctn-kix_list_3-1}ol.lst-kix_list_19-8{list-style-type:none}.lst-kix_list_30-3>li:before{content:"" counter(lst-ctn-kix_list_30-3,decimal) ". "}ol.lst-kix_list_19-2{list-style-type:none}ol.lst-kix_list_34-0.start{counter-reset:lst-ctn-kix_list_34-0 0}ol.lst-kix_list_19-3{list-style-type:none}ol.lst-kix_list_19-4{list-style-type:none}.lst-kix_list_30-8>li:before{content:"" counter(lst-ctn-kix_list_30-8,lower-roman) ". "}ol.lst-kix_list_19-5{list-style-type:none}.lst-kix_list_30-5>li{counter-increment:lst-ctn-kix_list_30-5}ol.lst-kix_list_19-0{list-style-type:none}ol.lst-kix_list_19-1{list-style-type:none}.lst-kix_list_3-1>li:before{content:"" counter(lst-ctn-kix_list_3-1,decimal) ". "}.lst-kix_list_26-6>li:before{content:"\0025cf   "}.lst-kix_list_33-6>li{counter-increment:lst-ctn-kix_list_33-6}.lst-kix_list_8-2>li:before{content:"\0025aa   "}.lst-kix_list_21-2>li:before{content:"\0025aa   "}.lst-kix_list_8-5>li:before{content:"\0025aa   "}.lst-kix_list_2-0>li{counter-increment:lst-ctn-kix_list_2-0}.lst-kix_list_26-3>li:before{content:"\0025cf   "}.lst-kix_list_3-6>li:before{content:"" counter(lst-ctn-kix_list_3-6,decimal) ". "}.lst-kix_list_21-7>li:before{content:"o  "}.lst-kix_list_11-2>li:before{content:"\0025aa   "}.lst-kix_list_16-6>li:before{content:"\0025aa   "}.lst-kix_list_25-2>li:before{content:"\0025aa   "}.lst-kix_list_18-2>li{counter-increment:lst-ctn-kix_list_18-2}.lst-kix_list_29-5>li{counter-increment:lst-ctn-kix_list_29-5}.lst-kix_list_16-1>li:before{content:"o  "}.lst-kix_list_19-3>li{counter-increment:lst-ctn-kix_list_19-3}.lst-kix_list_34-7>li{counter-increment:lst-ctn-kix_list_34-7}.lst-kix_list_17-2>li:before{content:"\0025aa   "}.lst-kix_list_30-0>li:before{content:"" counter(lst-ctn-kix_list_30-0,decimal) ". "}.lst-kix_list_17-5>li:before{content:"\0025aa   "}.lst-kix_list_27-2>li:before{content:"\0025aa   "}.lst-kix_list_22-3>li:before{content:"\0025cf   "}.lst-kix_list_7-1>li:before{content:"o  "}.lst-kix_list_29-2>li{counter-increment:lst-ctn-kix_list_29-2}.lst-kix_list_20-3>li{counter-increment:lst-ctn-kix_list_20-3}.lst-kix_list_31-7>li:before{content:"o  "}ol.lst-kix_list_34-1.start{counter-reset:lst-ctn-kix_list_34-1 0}.lst-kix_list_3-8>li{counter-increment:lst-ctn-kix_list_3-8}.lst-kix_list_4-2>li:before{content:"\0025aa   "}.lst-kix_list_15-2>li:before{content:"\0025aa   "}.lst-kix_list_10-6>li:before{content:"\0025aa   "}.lst-kix_list_9-1>li:before{content:"o  "}.lst-kix_list_20-3>li:before{content:"" counter(lst-ctn-kix_list_20-0,decimal) "." counter(lst-ctn-kix_list_20-1,decimal) "." counter(lst-ctn-kix_list_20-2,decimal) "." counter(lst-ctn-kix_list_20-3,decimal) ". "}.lst-kix_list_29-2>li:before{content:"" counter(lst-ctn-kix_list_29-2,lower-roman) ". "}.lst-kix_list_28-6>li:before{content:"\0025cf   "}.lst-kix_list_1-6>li:before{content:"\0025aa   "}.lst-kix_list_33-7>li:before{content:"" counter(lst-ctn-kix_list_33-7,lower-latin) ". "}.lst-kix_list_12-6>li:before{content:"\0025aa   "}.lst-kix_list_34-3>li:before{content:"" counter(lst-ctn-kix_list_34-3,decimal) ". "}.lst-kix_list_2-2>li:before{content:"" counter(lst-ctn-kix_list_2-2,decimal) ". "}.lst-kix_list_32-1>li{counter-increment:lst-ctn-kix_list_32-1}.lst-kix_list_13-2>li:before{content:"\0025aa   "}ol{margin:0;padding:0}table td,table th{padding:0}.c22{background-color:#ffffff;color:#2f5496;font-weight:400;text-decoration:none;vertical-align:baseline;font-size:11pt;font-family:"Calibri";font-style:normal}.c0{margin-left:35.9pt;padding-top:6pt;padding-left:-0.1pt;padding-bottom:0pt;line-height:1.0;orphans:2;widows:2;text-align:justify}.c14{background-color:#ffffff;color:#000000;text-decoration:none;vertical-align:baseline;font-size:11pt;font-family:"Calibri";font-style:normal}.c4{color:#000000;font-weight:400;text-decoration:none;vertical-align:baseline;font-size:11pt;font-family:"Calibri";font-style:normal}.c38{padding-top:6pt;padding-bottom:0pt;line-height:1.0;orphans:2;widows:2;text-align:center}.c23{color:#000000;font-weight:400;vertical-align:baseline;font-size:11pt;font-family:"Calibri";font-style:normal}.c37{padding-top:0pt;padding-bottom:0pt;line-height:1.0;orphans:2;widows:2;text-align:right}.c18{color:#000000;text-decoration:none;vertical-align:baseline;font-size:11pt;font-family:"Calibri";font-style:normal}.c1{padding-top:6pt;padding-bottom:0pt;line-height:1.0;orphans:2;widows:2;text-align:justify}.c24{color:#2f5496;text-decoration:none;vertical-align:baseline;font-size:11pt;font-family:"Calibri";font-style:normal}.c36{padding-top:0pt;padding-bottom:8pt;line-height:1.0791666666666666;orphans:2;widows:2;text-align:justify}.c27{font-weight:400;vertical-align:baseline;font-size:11pt;font-family:"Calibri";font-style:normal}.c25{color:#000000;vertical-align:baseline;font-size:11pt;font-family:"Calibri";font-style:normal}.c6{text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;font-weight:700;text-decoration:underline}.c30{text-decoration:none;vertical-align:baseline;font-size:11pt;font-family:"Calibri"}.c31{background-color:#ffffff;max-width:489pt;padding:35.5pt 42.5pt 42.5pt 63.8pt}.c21{text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;text-decoration:underline}.c34{background-color:#ffffff;color:#3c3c3c;text-decoration:none}.c15{margin-left:36pt;padding-left:18pt}.c20{color:inherit;text-decoration:inherit}.c9{margin-left:46.3pt;padding-left:-3.8pt}.c3{padding:0;margin:0}.c12{margin-left:36pt;padding-left:0pt}.c29{page-break-after:avoid}.c19{margin-left:17.9pt}.c7{font-weight:700}.c26{color:#0563c1}.c17{color:#0070c0}.c10{margin-left:18pt}.c2{margin-left:28.4pt}.c5{height:11pt}.c35{height:13pt}.c16{padding-left:10.4pt}.c32{color:#000000}.c33{margin-left:36pt}.c8{font-style:italic}.c11{background-color:#ffff00}.c28{font-weight:400}.c13{background-color:#00ffff}.title{padding-top:24pt;color:#000000;font-weight:700;font-size:36pt;padding-bottom:6pt;font-family:"Calibri";line-height:1.0791666666666666;page-break-after:avoid;orphans:2;widows:2;text-align:left}.subtitle{padding-top:18pt;color:#666666;font-size:24pt;padding-bottom:4pt;font-family:"Georgia";line-height:1.0791666666666666;page-break-after:avoid;font-style:italic;orphans:2;widows:2;text-align:left}li{color:#000000;font-size:11pt;font-family:"Calibri"}p{margin:0;color:#000000;font-size:11pt;font-family:"Calibri"}h1{padding-top:0pt;color:#000000;font-weight:700;font-size:24pt;padding-bottom:8pt;font-family:"Times New Roman";line-height:1.0;orphans:2;widows:2;text-align:left}h2{padding-top:2pt;color:#2f5496;font-size:13pt;padding-bottom:0pt;font-family:"Calibri";line-height:1.0791666666666666;page-break-after:avoid;orphans:2;widows:2;text-align:left}h3{padding-top:14pt;color:#000000;font-weight:700;font-size:14pt;padding-bottom:4pt;font-family:"Calibri";line-height:1.0791666666666666;page-break-after:avoid;orphans:2;widows:2;text-align:left}h4{padding-top:12pt;color:#000000;font-weight:700;font-size:12pt;padding-bottom:2pt;font-family:"Calibri";line-height:1.0791666666666666;page-break-after:avoid;orphans:2;widows:2;text-align:left}h5{padding-top:11pt;color:#000000;font-weight:700;font-size:11pt;padding-bottom:2pt;font-family:"Calibri";line-height:1.0791666666666666;page-break-after:avoid;orphans:2;widows:2;text-align:left}h6{padding-top:10pt;color:#000000;font-weight:700;font-size:10pt;padding-bottom:2pt;font-family:"Calibri";line-height:1.0791666666666666;page-break-after:avoid;orphans:2;widows:2;text-align:left}</style></head><body class="c31 doc-content"><p class="c38"><span class="c18 c7">VIRTUAL CURRENCY WALLET AGREEMENT - DFNS</span></p><p class="c1 c5"><span class="c18 c7"></span></p><p class="c1"><span class="c4">THIS </span><span class="c18 c7">VIRTUAL CURRENCY WALLET AGREEMENT</span><span class="c4">&nbsp;(&ldquo;</span><span class="c25 c6">WALLET AGREEMENT</span><span class="c4">&rdquo;) IS A LEGAL AND BINDING AGREEMENT BETWEEN YOU (&ldquo;</span><span class="c25 c6">USER</span><span class="c4">&rdquo;) AND BASTION GENOSSENSCHAFT (IN ENGLISH &ndash; BASTION COOPERATIVE), INCORPORATED AND EXISTING UNDER THE LAWS OF THE PRINCIPALITY OF LIECHTENSTEIN, AND HAVING ITS REGISTERED OFFICE AT SCHAANERSTRASSE 27, VADUZ 9490, PRINCIPALITY OF LIECHTENSTEIN (&ldquo;</span><span class="c25 c6">BASTION</span><span class="c4">&rdquo;, &ldquo;</span><span class="c25 c6">DISTRIBUTOR</span><span class="c4">&rdquo;). </span></p><p class="c1"><span class="c4">USE OF SOFTWARE AND PRODUCTS, INCLUDING WITHOUT LIMITATION ALL ASSOCIATED DOCUMENTATION, IS SUBJECT TO THE TERMS AND CONDITIONS. BY CLICKING &ldquo;I ACCEPT,&rdquo; DOWNLOADING OR INSTALLING THE SOFTWARE, OR OTHERWISE SIGNIFYING ACCEPTANCE OF THIS WALLET AGREEMENT, THE USER AGREES TO ALL TERMS AND CONDITIONS HEREIN.</span></p><h2 class="c1 c29 c35"><span class="c4"></span></h2><h2 class="c1 c29"><span class="c24 c7">1. General</span></h2><ol class="c3 lst-kix_list_20-1 start" start="1"><li class="c1 c10 c16 li-bullet-0"><span class="c4">In this Wallet Agreement Bastion and the User are collectively referred to as the &ldquo;</span><span class="c25 c6">Parties</span><span class="c4">&rdquo; and each individually as a &ldquo;</span><span class="c25 c6">Party</span><span class="c4">&rdquo;.</span></li><li class="c1 c10 c16 li-bullet-0"><span class="c4">This Wallet Agreement regulates the provision by the Distributor to the User of the third-party software, specifically the creation, provision to the User and maintenance of a virtual currency wallet</span><span class="c18 c7">&nbsp;</span><span class="c4">(</span><span class="c18 c7">&ldquo;</span><span class="c6 c25">VCW</span><span class="c18 c7">&rdquo;</span><span class="c4">)</span><span class="c18 c7">&nbsp;</span><span class="c4">enabled</span><span class="c18 c7">&nbsp;</span><span class="c4">by</span><span class="c18 c7">&nbsp;Dfns</span><span class="c4">, a soci&eacute;t&eacute; par actions simplifi&eacute;e incorporated under the laws of France, whose registered office is at 198, avenue de France, 75013 Paris (France), registered with the Trade and Companies Register of Paris under number 888 176&nbsp;575 (&ldquo;</span><span class="c25 c6">Merchant</span><span class="c4">&rdquo;) &ndash; &ldquo;</span><span class="c25 c6">Service&rdquo;</span><span class="c4">.</span></li><li class="c1 c10 c16 li-bullet-0"><span class="c4">In providing the Service to the User under this Wallet Agreement Bastion acts on the basis of a Software as a Service Agreement of 12 October 2022 between Bastion and the Merchant.</span></li><li class="c1 c10 c16 li-bullet-0"><span class="c4">This Wallet Agreement is subject to acceptance by the User of the </span><span class="c21 c17 c27">Terms of Use</span><span class="c17 c8 c28 c30">&nbsp;</span><span class="c4">(&ldquo;</span><span class="c25 c6">Terms</span><span class="c4">&rdquo;) and shall be read, understood and interpreted in conjunction with the Terms incorporated herein by reference.</span></li><li class="c1 c10 c16 li-bullet-0"><span class="c4">Bastion reserves the right to revise this Wallet Agreement from time to time at its sole discretion, with any changes made available on Bastion website and/or mobile application. Bastion may will also notify the User of such changes via push notifications through the App. The changes shall take effect from the date of publication of the new terms or such other date stated in the notice, and your continued access or use of the Services from such date shall be deemed to constitute acceptance of the new terms in their entirety. It shall be the User&rsquo;s sole responsibility to check Bastion website for such changes from time to time.</span></li></ol><p class="c1 c5"><span class="c4"></span></p><h2 class="c1 c29"><span class="c24 c7">2. Definitions</span></h2><p class="c1"><span class="c27 c34">For the purposes of this Wallet Agreement, the following terms shall have the following meaning:</span></p><p class="c1"><span class="c14 c7">&ldquo;App&rdquo; </span><span class="c14 c28">means Polity Private MVP application.</span></p><p class="c1"><span class="c7 c14">&ldquo;User Account&rdquo;</span><span class="c14 c28">&nbsp;means an account used to record the User&rsquo;s email, username, password, 2-factor verification code, unique identity information of the mobile application,&nbsp;joined groups, the accessible networks, and the User&rsquo;s personal files and settings. User Account will record any on-chain information about the User&#39;s transactions, and be the only account to login and use the VCW.</span></p><p class="c1"><span class="c14 c28">&nbsp;&ldquo;</span><span class="c14 c7">Virtual Currency Wallet&rdquo; or &ldquo;VCW&rdquo; </span><span class="c14 c28">means a software program to securely store, [</span><span class="c4 c11">manage, receive, and send</span><span class="c14 c28">] digital assets, including web console and mobile applications, in a distributed and &ldquo;keyless&rdquo; manner.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Indemnified Parties</span><span class="c4">&rdquo; has the meaning given in Clause 6.1.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Initial Duration</span><span class="c4">&rdquo; has the meaning given in Clause 9.1. </span></p><p class="c1"><span>&ldquo;</span><span class="c7">Privacy Policy</span><span>&rdquo; means Bastion&rsquo;s </span><span class="c21 c26">Privacy Policy</span><span class="c17">&nbsp;</span><span class="c4">which we may amend from time to time by Bastion.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Quarter</span><span class="c4">&rdquo; means a period of three (3) calendar months</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Recipient</span><span class="c4">&rdquo; has the meaning given in Clause 8.1.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Renewal Duration</span><span class="c4">&rdquo; has the meaning given in Clause 9.1.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Service Credit</span><span class="c4">&rdquo; is a dollar credit, calculated in the manner set forth in Clause 6.2 that we may credit to your Account.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Services</span><span class="c4">&rdquo; means the service(s) provided by Bastion and/or the Merchant to the User via the App or any other website notified to you from time to time.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Signer&rdquo;</span><span class="c4">&nbsp;means an individual Docker Container responsible for operating a given key-share, and communicating with other Signers to create signatures.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Software</span><span class="c4">&rdquo; means any software or application developed and released by the Merchant as part of the Services. </span></p><p class="c1"><span>&ldquo;</span><span class="c7">Subscription Fee</span><span>&rdquo; means the </span><span class="c13">[</span><span class="c21 c17 c13">monthly</span><span class="c13">], [</span><span class="c21 c17 c13">annual</span><span class="c13">], [</span><span class="c21 c17 c13">lifetime</span><span class="c13">]</span><span>&nbsp;subscription fee of </span><span class="c13">[nil] [ &nbsp; &nbsp;] [ &nbsp; &nbsp;] &nbsp;[ &nbsp; &nbsp;]</span><span class="c4">&nbsp;payable by you to Bastion for the use of VCW.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Supported Digital Assets</span><span>&rdquo; means any digital asset which is a digital representation of value based on (or built on top of) a cryptographic protocol of a computer network which has been approved by and/or supported by both Bastion and the Merchant for use in connection with the Services from time to time. Please refer to for the list at </span><span class="c21 c26"><a class="c20" href="https://www.google.com/url?q=https://www.dfns.co/assets&amp;sa=D&amp;source=editors&amp;ust=1719482618216549&amp;usg=AOvVaw1PoAlYB6-MxvR5MuFK7kjF">https://www.dfns.co/assets</a></span><span class="c4">&nbsp;. </span></p><p class="c1"><span>&ldquo;</span><span class="c7">User Data</span><span class="c4">&rdquo; means the data inputted by you, the Authorised Users, or the Merchant on your behalf for the purpose of using the Services, or facilitating your use of the Services, other than personal data.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Virus</span><span class="c4">&rdquo; means a thing or device (including any software, code, file or programme) which may: prevent, impair or otherwise adversely affect the operation of any computer software, hardware or network, any telecommunications service, equipment or network or any other service or device; prevent, impair or otherwise adversely affect access to or the operation of any programme or data, including the reliability of any programme or data (whether by re-arranging, altering or erasing the programme or data in whole or part or otherwise); or adversely affect the user experience, including worms, trojan horses, viruses and other similar things or devices.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Vulnerability</span><span class="c4">&rdquo; means a weakness in the computational logic (for example, code) found in software and hardware components that when exploited, results in a negative impact to the confidentiality, integrity, or availability of the Services, and the term &ldquo;Vulnerabilities&rdquo; shall be interpreted accordingly.</span></p><p class="c1"><span>&ldquo;</span><span class="c7">Wallet</span><span class="c4">&rdquo; means a combination of private and public keys.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">2. The Services </span></p><p class="c1"><span class="c4">2.1. Bastion shall, during the Term provide the Services described in Clauses 2.2 and 2.3 to the User. The provision of the Services is subject to prior:</span></p><ul class="c3 lst-kix_list_21-0 start"><li class="c1 c12 li-bullet-1"><span class="c4">acceptance by the User of the Terms, Disclaimer and Privacy Policy;</span></li><li class="c1 c12 li-bullet-2"><span class="c4">onboarding of the User, i.e., whitelisting, creation by the User of at least one Avatar and creation of a virtual node for that Avatar;</span></li><li class="c1 c12 li-bullet-2"><span class="c4">Selection by the User of one a Subscription Plan;</span></li><li class="c1 c12 li-bullet-2"><span class="c4">acceptance by the User of this Wallet Agreement.</span></li></ul><p class="c1 c5 c33"><span class="c4"></span></p><p class="c1"><span class="c21">2.2. Generation of the key-shares </span></p><p class="c1 c19"><span>The Merchant&rsquo;s algorithm enables the User to generate a combination of public and private keys (the &ldquo;</span><span class="c6">Wallet</span><span class="c4">&rdquo;) through the API. </span></p><p class="c1 c19"><span>2.2.1. The private key is generated by the User, via the API, as a collection of key-shares </span><span class="c21">(</span><span class="c6">&ldquo;Distributed Secret Key Shares</span><span>&rdquo;). Each part of the Distributed Secret Key Shares is assigned to a host (&ldquo;</span><span class="c6">Signer</span><span class="c7">&rdquo;</span><span class="c4">). For security purposes, Signers are distributed among different Data Centers located across several geographic regions. </span></p><p class="c1 c10"><span>Each User has access to a unique collection of Signers (&ldquo;</span><span class="c6">Signing Group</span><span class="c4">&rdquo;), composed of Signers and assigned with a threshold of key-shares, which represent the minimum number of key-shares needed to sign a transaction via Multi-Party Computation (MPC). For additional security purposes, the number of Signers equals twice the number of key-shares for the threshold, plus one. The distributed key generation process is initiated by the User via API call. </span></p><p class="c1 c10"><span>2.2.2. The User may via API through the Merchants&rsquo; policy engine define the </span><span class="c6">Authorized Person(s)</span><span class="c4">&nbsp;who will have the authority to configure the API and under which conditions. </span></p><p class="c1 c10"><span class="c4">2.2.3. When an Authorized Person, wants to perform an activity on a blockchain, such Authorized Person shall use his/her user confidential credentials to access the API key and process the chosen activity. </span></p><p class="c1 c10"><span class="c4">The User hereby agrees and accepts full responsibility for all use that occurs under the User account and any credentials, passwords, keys, tokens, including any activities conducted by the User or any third parties that have access to the User&rsquo;s account information whether authorized or not.</span></p><p class="c1 c10"><span>&nbsp;If the User believes an unauthorized person has gained access to his/her account, or any credentials, passwords, keys, tokens, the User must immediately notify the Merchant at: </span><span class="c21 c26"><a class="c20" href="mailto:<EMAIL>"><EMAIL></a></span><span class="c4">.</span></p><p class="c1 c10"><span class="c4">The User hereby accepts and acknowledges that both the private keys and the private key-shares will never be revealed to anyone, including Bastion and/or the Merchant &nbsp;or any of the Users Authorized Persons, the private key-shares will remain &ldquo;embedded&rdquo; within the Solution, i.e. stored and encrypted within each of the different Signers. However, the public key and addresses derived from the private keys will be communicated to the User by the API. </span></p><p class="c1 c10"><span class="c18 c7">The User hereby acknowledges and accepts being solely responsible for any consequences, losses, expenses, costs, and claims that may result from any incorrect, neglected and unauthorized use of the private Key Shares. </span></p><p class="c1 c10"><span class="c7 c18">Neither Bastion nor the Merchant shall assume responsibility for any loss or damage that may be incurred by the User due to the failed recovery of the wallet or User not recording the Key Share, loss of data, erroneous transmission of cryptocurrency, and hacking by third parties. </span></p><p class="c1 c10"><span class="c18 c7">The User hereby accepts to implement appropriate technical, organizational and security measures to make sure that the User credentials are stored safely. </span></p><p class="c1 c10 c5"><span class="c18 c7"></span></p><p class="c1"><span class="c21">2.3. Wallet Management Services:</span></p><p class="c1"><span class="c4">2.3.1 Bastion will provide the User with a user interface accessible through the User&rsquo;s Account to enable the User to manage the digital wallet. The User may only carry out transactions from the Account once the Distributed Secret Key Shares have been generated. Through the user interface, the User may interact with the relevant supported blockchain and enjoy the following functions:</span></p><p class="c1"><span>*******. </span><span class="c8">Receiving assets</span><span class="c4">: Use the App to generate a QR code as well as a blockchain address to receive Supported Digital Assets. </span></p><p class="c1"><span class="c4">Given the risk that the address may be tampered with or replaced during transmission, Bastion recommends to confirm the receiving blockchain address with the sender after the sender has received it.</span></p><p class="c1"><span>*******. </span><span class="c8">Address whitelist</span><span class="c4">: Save recipient blockchain addresses on the App through the &ldquo;whitelist&rdquo; function. </span></p><p class="c1"><span class="c4">If there is more than one person operating the Account, Bastion recommends that at least two Authorised Persons with the &ldquo;administrator&rdquo; role check and verify that the whitelist address is accurate.</span></p><p class="c1"><span>*******. </span><span class="c8">Sending assets</span><span class="c4">: Transfer Supported Digital Assets out of the linked digital asset wallet through the User interface to external recipients. </span></p><p class="c1"><span class="c4">The User may transfer its digital assets, from the Wallets generated using the Solution, to an external legitimate address by using the following process: </span></p><ol class="c3 lst-kix_list_29-0 start" start="1"><li class="c1 c9 li-bullet-3"><span class="c4">The User authenticates through Multiple Factor Authorization (&ldquo;</span><span class="c25 c6">MFA</span><span class="c4">&rdquo;) to the Merchant&rsquo;s dashboard to obtain an API authorization token.</span></li><li class="c1 c9 li-bullet-4"><span class="c4">The User initiates a transfer request on behalf of the user interface, via the API, using its authorization token or confidential API key. </span></li><li class="c1 c9 li-bullet-4"><span class="c4">A message is sent automatically to the Signers. </span></li><li class="c1 c9 li-bullet-4"><span class="c4">The Signing Group creates a signature for the message and the Service automatically completes the activity corresponding to the User&rsquo;s initial request. </span></li></ol><p class="c1"><span class="c4">Please ensure that there are sufficient funds for the transfer, including any transaction or network fees which may be incurred. These transaction fees are set by the third party blockchains and networks and neither Bastion nor the Merchant have control or influence over such fees. Bastion does not charge any fees for such transfers.</span></p><p class="c1"><span>2.3.1.4 </span><span class="c8">Transaction record: </span><span class="c4">Record the status of the transactions or the recent transaction history.</span></p><p class="c1"><span class="c4">This information may be downloaded or exported as a MS Excel sheet.</span></p><p class="c1"><span>2.3.1.5 </span><span class="c8">Policy engine:</span><span class="c4">&nbsp;Customize and set the approval policies for transactions which may be carried out through the Account. </span></p><p class="c1"><span class="c4">The User may configure triggering conditions based on up to five (5) dimensions (initiator, asset source, destination, asset type and amount). Specific member roles may be assigned and set. Once set, Authorised Users with the requisite permissions may modify the policies with the approval of Authorised Users with the &ldquo;administrator&rdquo; role. </span></p><p class="c1"><span class="c4">For the avoidance of doubt, neither Bastion nor the Merchant will be able to make any changes to the User&rsquo;s approval policies. </span></p><p class="c1"><span class="c4">The User can access and retrieve these records at any time.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c21 c23">2.4. Unsupported Digital Assets</span></p><p class="c1"><span>Under no circumstances should the User attempt to use the Services stated herein to store, send, request, or receive any assets other than Supported Digital Assets (for more information, please visit the Merchants&rsquo; supported tokens list: </span><span class="c21 c26"><a class="c20" href="https://www.google.com/url?q=https://www.dfns.co/assets&amp;sa=D&amp;source=editors&amp;ust=1719482618220073&amp;usg=AOvVaw37vAVrJHUBBVUZC4oQpsGh">https://www.dfns.co/assets</a></span><span class="c4">&nbsp;). </span></p><p class="c1"><span class="c4">Neither Bastion nor the Merchant assume responsibility in connection with any attempt by the User to use Digital Assets that the Merchant does not support. </span></p><p class="c1"><span class="c4">The User hereby acknowledges and agrees that Bastion and/or the Merchant are not liable for any unsupported Digital Asset that is sent to the User Wallet. </span></p><p class="c1"><span class="c4">The User agrees that the Merchant may in its sole discretion terminate support or add support for any particular Digital Asset, it is the User&rsquo;s responsibility to check the supported assets before taking any action on the wallet. Neither bastion nor the Merchant incure obligation whatsoever with regard to unsupported Digital Assets sent to the User Account or with regard to Supported Digital Asset sent to an incompatible wallet address. </span></p><p class="c1"><span class="c4">The User hereby acknowledges and agrees that all erroneously transmitted Digital Assets will be lost and that he/she may be required to pay network or miner&rsquo;s fees in order for an Inbound Transfer transaction to be successful. Insufficient network fees may cause an Inbound Transfer to remain in a &ldquo;pending state&rdquo; outside of the Merchant&rsquo;s control. The User agrees to release Bastion and or the Merchant of responsibility and liability for delays or any loss incurred as a result of an error in the initiation of the transaction. In such cases, the parties agree that neither Bastion nor the Merchant have an obligation to assist in the remediation of such transactions.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c23 c21">2.5. Wallet Services&rsquo; Limitations</span></p><p class="c1"><span>2.5.1. The Service may be subject to limitations on the number of API calls per minute. If the User wishes to exceed such limitations, the User may request an increase via email at </span><span class="c21 c26"><a class="c20" href="mailto:<EMAIL>"><EMAIL></a></span><span class="c4">&nbsp;. The Merchant may approve (subject to additional fees) or reject any such request at its sole discretion. If the User attempts to exceed the limitation without prior approval from the Merchant, the later may suspend or throttle the User&rsquo;s excess use and the User shall be subject to applicable charges for any excess usage. </span></p><p class="c1"><span>2.5.2. The Merchant only supports, and the User may only use the Service in connection with, the protocols/tokens/transactions that are listed at: </span><span class="c21 c26"><a class="c20" href="https://www.google.com/url?q=http://www.dfns.co/assets&amp;sa=D&amp;source=editors&amp;ust=1719482618220917&amp;usg=AOvVaw0L2ksyUmSsHBnX1EWVGXWa">www.dfns.co/assets</a></span><span>&nbsp;(&ldquo;</span><span class="c6">Supported Tokens</span><span class="c4">&rdquo;). The list of Supported Tokens may change at the sole discretion of the Merchant. Accordingly, the User shall not attempt to receive, request, send, store, or engage in any other type of transaction involving any protocol, token or transaction other than a Supported Token. Neither Bastion nor the Merchant will have responsibility or liability if the User loses, burns, or otherwise cannot access or control any token that the Merchant does not support. </span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span>2.6. </span><span class="c23 c21">Software and App Updates</span></p><p class="c1"><span class="c4">2.5.1 Bastion and/or the Merchant may from time to time provide updates and upgrades to Software. Such updates and upgrades (if any), and their frequency, will be determined and implemented at Bastion&rsquo;s and/or the Merchant&rsquo;s sole discretion. The responsibility for ensuring that the latest version of the Software is installed on the User&rsquo;s device(s) rests with the User. Any use of outdated versions of the Software or incompatible devices will be at the User&rsquo;s sole risk and without prejudice to the generality of Clause 8.</span></p><p class="c1"><span class="c4">2.5.2. If the blockchain of a Supported Digital Asset forks, Bastion and/or the Merchant may temporarily suspend the use of the Services in relation to such affected digital asset. Bastion and the Merchant reserve the absolute discretion to determine whether or not to support the forked protocol. In any event, the User may opt to transfer the affected digital asset out of the digital wallet.</span></p><p class="c1 c5"><span class="c18 c7"></span></p><p class="c1"><span class="c23 c21">2.6. Disaster recovery </span></p><p class="c1"><span class="c4">As long as each Signing Group gathers a number of Signers equal to or greater than the threshold, the User can perform an activity without having to trigger the disaster recovery plan. </span></p><p class="c1"><span class="c4">However, if either: </span></p><ul class="c3 lst-kix_list_31-0 start"><li class="c1 c12 li-bullet-1"><span class="c4">the Service is unable to use one or several Key Shares to sign a transaction; or </span></li><li class="c1 c12 li-bullet-1"><span class="c4">one or several Authorized Persons forget the password, or any information required to approve a transaction for any other reason; and the number of remaining usable Key Shares is lower than the threshold of key-shares needed to create a signature (i.e. performing an activity has become materially impossible), then the Merchant&rsquo;s Disaster Recovery Plan shall be executed.</span></li></ul><p class="c1"><span>The Merchant&rsquo;s Disaster Recovery Plan is available for reference at: </span><span class="c21 c26"><a class="c20" href="https://www.google.com/url?q=https://www.dfns.co/faq-chapters/security&amp;sa=D&amp;source=editors&amp;ust=1719482618221859&amp;usg=AOvVaw0sXgUbY5wTSNiJ8u0Vfih9">https://www.dfns.co/faq-chapters/security</a></span><span class="c4">. </span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">3. Warranties</span></p><p class="c1"><span>3.1</span><span class="c30 c32 c8 c28">. Bastion&rsquo;s Warranties:</span></p><p class="c1"><span class="c4">3.1.1 Bastion warrants that the Services will be performed substantially in accordance with the Documentation and with reasonable care. This undertaking shall not apply in case of any non-conformance caused by use of the Services contrary to the instructions, or modification or alteration of the APIs or Software by any party other than Bastion or the Merchant or the duly authorised contractors or agents. If the Services do not conform with the foregoing undertaking, Bastion will, at its own expense, use commercially reasonable endeavours to correct any such non-conformance promptly, or provide the User with an alternative means of accomplishing the desired performance. </span></p><p class="c1"><span class="c4">3.1.2 Bastion represents and warrants that it, or its Merchant, contractors and agents shall use commercially reasonable efforts to provide the Services without introducing errors.</span></p><p class="c1"><span class="c4">3.1.3 Save as expressly set out in this Clause 3, Bastion makes no other warranties in respect of the Services, Software and/or the Documentation.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span>3.2 </span><span class="c8">Bastion&rsquo;s Warranty Disclaimer:</span></p><p class="c1"><span class="c4">3.2.1 To the maximum extent permitted under applicable laws, and except as expressly provided in this Wallet Agreement, Bastion makes no warranties of any kind, express or implied, regarding the Services and the Software and disclaims to the full extent permitted by law all warranties of merchantability, fitness for a particular purpose, and any warranties arising out of the course of dealing between the Parties.</span></p><p class="c1"><span class="c4">3.2.2 The User hereby agrees that the Services and the Software are provided on an &ldquo;as is&rdquo; and &ldquo;as available&rdquo; basis without warranty of any kind.</span></p><p class="c1"><span class="c4">3.2.3 Bastion makes no warranties or representations regarding the use of the Software and the App with third party products or services, including, for the avoidance of doubt, Web3 applications.</span></p><p class="c1"><span class="c4">3.2.4 Bastion does not warrant that:</span></p><ul class="c3 lst-kix_list_24-0 start"><li class="c1 c12 li-bullet-1"><span class="c4">the use of the Services will be uninterrupted or error-free;</span></li><li class="c1 c12 li-bullet-2"><span class="c4">the Services, Documentation and/or the information obtained by the User through the Services will meet the User&rsquo;s requirements; or</span></li><li class="c1 c12 li-bullet-2"><span class="c4">the Software or the Services will be free from Vulnerabilities or Viruses.</span></li></ul><p class="c1 c5 c33"><span class="c4"></span></p><p class="c1"><span class="c23 c21">3.3. Trade Sanctions, AML, CFT Compliance </span></p><p class="c1"><span>3.3.1. The User hereby agrees to use the Services listed in this Wallet Agreement in compliance with all applicable trade sanctions laws, export control laws, AML and CFT rules and regulations, including without limitation the regulations administered by the U.S. Department of the Treasury&rsquo;s Office of Foreign Assets Control (&ldquo;</span><span class="c6">OFAC</span><span>&rdquo;), and the U.S. Department of Commerce&rsquo;s Bureau of Industry and Security (&ldquo;</span><span class="c6">BIS</span><span class="c4">&rdquo;). </span></p><p class="c1"><span class="c4">The User represents and warrant that:</span></p><ol class="c3 lst-kix_list_33-0 start" start="1"><li class="c1 c15 li-bullet-5"><span class="c4">the User is not a citizen of, or located within, a country or territory that is subject to comprehensive trade restrictions (including without limitation U.S trade sanctions, Russia, Crimea, Cuba, Iran, North Korea, and Syria); </span></li><li class="c1 c15 li-bullet-6"><span class="c4">the User is not a Politically exposed person (PEP), </span></li><li class="c1 c15 li-bullet-5"><span class="c4">the User is not identified on any government restricted party lists (including without limitation the Specially Designated Nationals and Blocked Persons List, Foreign Sanctions Evaders List, and Sectoral Sanctions Identifications List, administered by OFAC, and the Denied Party List, Entity List and Unverified List, administered by BIS); and</span></li><li class="c1 c15 li-bullet-7"><span class="c4">that no content created or submitted by the User, or the User&rsquo;s Authorised Persons, is subject to any restriction on disclosure, transfer, download, export or re-export under the Export Control Laws. </span></li></ol><p class="c1 c10"><span class="c4">The User &nbsp;hereby agrees not to use the Service to disclose, transfer, download, export or re-export, directly or indirectly, any content to any country, entity or other party which is ineligible to receive such items under the Export Control Laws or under other laws or regulations to which the User may be subject. </span></p><p class="c1"><span class="c4">The User acknowledges that the Service may not be available in all jurisdictions and that the User is solely responsible for </span></p><ol class="c3 lst-kix_list_34-0 start" start="1"><li class="c1 c15 li-bullet-5"><span class="c4">complying with Anti money laundering and Counter Finance Terrorism Laws, the Export Control Laws and </span></li><li class="c1 c15 li-bullet-7"><span class="c4">monitoring them for any modifications. </span></li></ol><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span>3.4. </span><span class="c8">The User&rsquo;s Warranties and Representations:</span></p><p class="c1"><span class="c4">3.4.1 The User has the full and sufficient legal capacity and authority to enter into and be bound by this Wallet Agreement and the person executing or otherwise accepting this Wallet Agreement on the User&rsquo;s behalf has full legal capacity and authorization to do so.</span></p><p class="c1"><span class="c4">3.4.2 If the User is a corporate entity, the User duly organized, validly existing and in good standing under the laws of the jurisdiction in which it is organized.</span></p><p class="c1"><span class="c4">3.4.3 No petition has been presented, no proceeding has been commenced, no order has been made, and no resolution has been passed for the User&rsquo;s bankruptcy (if the User is an individual), or for the appointment of an, administrator, trustee or similar officer of it or of all or a substantial part of its business or assets (if the User is an entity).</span></p><p class="c1"><span class="c4">3.4.4. If the User is a corporate entity, the User will ensure that its directors, employees, representatives, agents, affiliates and/or contractors (including for the avoidance of doubt, Authorised Users) do comply with this Wallet Agreement.</span></p><p class="c1"><span class="c4">3.4.5. All information provided by the User to Bastion in the course of onboarding and the use of the Services is complete, true, and accurate in all material respects, including, where the User is a corporate entity, no material information about the ownership and control over the User and its operations has been excluded; and no other person or entity has an ownership interest in the User except for those disclosed in connection with such onboarding.</span></p><p class="c1"><span class="c4">3.4.6. The User represents and warrants to Bastion that activities that the User carries out through its Account comply with all applicable laws and regulations, including but not limited to anti-bribery, anti-corruption, anti-fraud, anti-money laundering or anti-terrorism laws and regulations.</span></p><p class="c1"><span class="c4">3.4.7. The User further represents and warrants to Bastion that we will be immediately notified of any Vulnerabilities found in the Services, Accounts, Apps or Software. The User shall, and shall procure that its representatives, agents and subcontractors cooperate with Bastion in good faith during Bastion&rsquo;s investigations of any illegal operations and any Vulnerabilities.</span></p><p class="c1"><span class="c4">3.4.8. The User shall maintain adequate security and control over any and all its private keys, the mnemonic phrases, registered email account(s), passwords, API keys, 2-factor authentication devices or backups, or any other codes that the User uses to access the Services. Any loss or compromise of the foregoing information and/or the User&rsquo;s personal information may result in unauthorized access to the User&rsquo;s Account by third parties and the loss or theft of digital assets from the digital wallet. The User is responsible for keeping its registered email address(es) up to date in the Account profile in order to receive any notices or alerts that Bastion may send. The User is responsible for ensuring that that the generated mnemonic phrases are stored securely offline by each Authorised User who is permitted access and/or control over them.</span></p><p class="c1"><span class="c4">3.4.9. The User shall ensure that the destination blockchain address is accurate before effecting any transfers from the User&rsquo;s Account.</span></p><p class="c1"><span class="c4">3.4.10. The User shall only receive or transfer Supported Digital Assets to the Account. The Supported Digital Assets are listed and displayed in the App. </span></p><p class="c1"><span class="c4">3.4.11. The User shall not receive or send any digital asset types which do not support the Merchant&rsquo;s signature algorithm before receiving the written approval.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">4. Intellectual Property Rights</span></p><p class="c1"><span class="c30 c32 c8 c28">4.1. Intellectual Property:</span></p><p class="c1"><span class="c4">4.1.1. The User acknowledges and agree that Bastion and/or the Merchant own all right, title, and interest in and to the Services, Software, Documentation, and all related technology and intellectual property rights. No other entity is entitled to grant or procure the grant of the limited license set out in this Wallet Agreement. Except as expressly provided herein, the User obtains no other rights under this Wallet Agreement in respect of the Services and Documentation including any related intellectual property rights.</span></p><p class="c1"><span class="c4">4.1.2. The User agrees not to, and shall procure that its Authorised Users, employees, directors, contractors or any other affiliate do not reverse engineer, decompile, disassemble, modify, update, create derivative works of, or otherwise tamper with Software or enable, assist or cause any third party to do so. The User agrees to and shall procure that its Authorised Users, employees, directors, contractors and any other affiliate keep the intellectual property in the Software confidential and not transfer, broadcast, publish, rent, license, lend, sell or otherwise distribute any Software or related licenses in whole or in part.</span></p><p class="c1"><span class="c4">4.1.3 For the avoidance of doubt, nothing in this Wallet Agreement shall be interpreted to grant any implied licenses under this Wallet Agreement.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c30 c8 c28 c32">4.2. User Data:</span></p><p class="c1"><span class="c4">4.2.1 The User represents and warrants that the User and its Authorised Persons own all right, title and interest in the User Data and shall have sole responsibility for the legality, reliability, integrity, accuracy and quality of all such User Data. By using the Services, Software and/or Documentation, the User grants a worldwide, non-exclusive license to use, process, display, copy and store the User Data for the purposes of providing our Services, enabling the User&rsquo;s use of the Software and/or Documentation, enabling Bastion and/or the Merchant to improve on the Services, Software and Documentation, and to collect and analyse anonymous information.</span></p><p class="c1"><span class="c4">4.2.2. The User may back up its User Data, transaction information and audit logs from the User&rsquo;s Account. Please refer to Bastion&rsquo;s Privacy Policy for further details on how Bastion may collect, use and treat User Data and the User&rsquo;s personal data.</span></p><p class="c1"><span>4.3. The User acknowledges User have read, and agrees to Baston </span><span class="c21 c17">Privacy Policy</span><span class="c4">.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c7 c24">5. Risk Warning</span></p><p class="c1"><span class="c7">THIS SECTION COVERS THE RISKS ASSOCIATED WITH THE USE OF THE SERVICES AND SOFTWARE</span></p><p class="c1"><span class="c4">5.1. Blockchain technology and other associated and related technologies are new. As with such developing technology, they come with various risks including those set out in this Clause 5. Prior to entering into this Wallet Agreement, the User acknowledges that it have been warned of the risks associated with the use of the Services, Software and/or Documentation and agree that subject always to Clause 8, Bastion is not responsible, and assumes no liability for the following risks:</span></p><p class="c1"><span>5.1.1. </span><span class="c8">Price Fluctuation</span><span class="c4">&nbsp;- losses may occur due to changes in macroeconomic policies, laws and regulations in any jurisdiction which result in changes in the value of the digital assets held by the User.</span></p><p class="c1"><span>5.1.2. </span><span class="c8">Unconfirmed Digital Assets Transfer</span><span class="c4">&nbsp;- the blockchain network of the digital asset the User wishes to transfer may be suffering from any delays or malfunction which result in the failure to confirm any transfer of such digital assets.</span></p><p class="c1"><span>5.1.3</span><span class="c8">. Risk of Transfer to Wrong Blockchain Address</span><span class="c4">&nbsp;- erroneously inputting the wrong destination blockchain address may which result in the irreversible loss of digital assets.</span></p><p class="c1"><span>5.1.4</span><span class="c8">. Risk of Loss or Theft of Offline Mnemonic Phrase</span><span class="c4">&nbsp;- the mnemonic phrase associated with the User Key Shard may be lost or stolen by nefarious hackers which may result in the User losing access to the digital assets stored at such associated blockchain addresses.</span></p><p class="c1"><span>5.1.5</span><span class="c8">. Risk of Security Weaknesses in the Blockchain Network and Protocol - </span><span class="c4">the software or infrastructure underlying the blockchain network, digital assets or protocol may unintentionally include weaknesses or bugs in the source code interfering with the use of or causing the loss of digital assets. As Bastion cannot influence the overall security of any network or blockchain protocols which the User interacts with, Bastion does not assume any responsibility for any risks associated with such blockchain networks and protocols, and cannot guarantee its function, security and availability.</span></p><p class="c1"><span>5.1.6. </span><span class="c8">Non-App Transfers</span><span class="c4">&nbsp;- as transfers of digital assets can be made without the use of the Services through the User&rsquo;s original private key, if the User transfers digital assets from its digital wallet, such transfers will not be protected by the Merchant&rsquo;s multi-party security processes and there is a higher risk that the digital assets being transferred may be stolen or transferred to the wrong wallet address.</span></p><p class="c1"><span>5.1.7</span><span class="c8">. Risk of Service Disruptions</span><span>&nbsp;- disruptions to the use of the Services, may prevent the User from, </span><span class="c8">inter alia,</span><span class="c4">&nbsp;successfully utilising or interacting with time restricted functions which Web3 protocols and decentralized finance applications may have. Bastion does not assume any responsibility for any risks associated with disruptions to the Services and the User&rsquo;s sole and only recourse for such disruptions to Services are set out in Clause 2 above.</span></p><p class="c1"><span class="c4">5.2. The User also acknowledges that the use of digital assets and blockchain based solutions, networks and blockchain protocols may involve serious risks. Any reminders Bastion gives to the User of the above risks shall not be taken as an admission or assumption of responsibility to issue further reminders of similar nature or of any other risks.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">6. Indemnification</span></p><p class="c1"><span>6.1. The User shall indemnify and hold harmless Bastion, its affiliates, Merchants and service providers, and each of its or their respective officers, directors, agents, employees, and representatives (collectively the &ldquo;</span><span class="c7">Indemnified Parties</span><span class="c4">&rdquo;), from and against any liabilities, damages, losses, costs and expenses, including but not limited to reasonable legal fees and costs and any fines, fees or penalties imposed by any regulatory authority, arising out of or incurred in connection with any third party claim, demand, action or proceeding (a &ldquo;Claim&rdquo;) arising out of or related to:</span></p><p class="c1"><span class="c4">(a) the User&rsquo;s access or use of the Services and/or Documentation;</span></p><p class="c1"><span class="c4">(b) the User&rsquo;s breach of this Wallet Agreement;</span></p><p class="c1"><span class="c4">(c) any material breach or inaccuracy of any of the User&rsquo;s representations, warranties or covenants in this Wallet Agreement;</span></p><p class="c1"><span class="c4">(d) the User&rsquo;s failure to provide true and accurate information in connection with the onboarding process or any failure to promptly update such information; or</span></p><p class="c1"><span class="c4">(e) the User&rsquo;s violation of any applicable laws, or the rights of any third party,</span></p><p class="c1"><span class="c4">except where such Claim arises from the gross negligence, fraud or willful misconduct of Bastion and/or its affiliates, and provided always that the User given reasonable notice of such Claim. Any liabilities, damages, losses, costs and expenses, fines, fees or penalties shall be paid to the Indemnified Parties on demand.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">7. Limitation of Liability</span></p><p class="c1"><span class="c7">PLEASE READ THIS SECTION CAREFULLY AS IT COVERS THE LIMITATIONS OF BASTION&rsquo;S LIABILITY.</span></p><p class="c1"><span class="c4">7.1. To the fullest extent permitted by applicable law and subject always to Clause 7.3:</span></p><p class="c1"><span class="c4">7.1.1. in no event shall Bastion, and/or any of its affiliates be liable for any indirect, special, incidental, consequential or exemplary damages of any kind (including, but not limited to, where related to loss of revenue, income or profits, loss of use or data, or damages for business interruption) arising out of or in any way related to the provision or use of the Services, or otherwise related to this Wallet Agreement, regardless of the form of action, whether based in contract, tort (including, but not limited to, simple negligence, whether active, passive or imputed), or any other legal or equitable theory (even if Bastion has been advised of the possibility of such damages and regardless of whether such damages were foreseeable); and</span></p><p class="c1"><span class="c4">7.1.2. in no event will Bastion&rsquo;s liability and that of our affiliates, suppliers and contractors, in aggregate, arising from or relating to the Services and this Wallet Agreement exceed the greater of: (a) the amount the User paid for the Services during the 12-month period immediately preceding the incident giving rise to that liability; and (b) two hundred United States dollars (US$200.00).</span></p><p class="c1"><span class="c4">7.2. Without prejudice to the generality of Clause 7.1. above, you agree that:</span></p><p class="c1"><span class="c4">7.2.1. Bastion shall not bear any liability, whatsoever, for any damage, losses or interruptions caused by any Viruses, spyware, scareware, Trojan horses, worms or other malware that may affect the User&rsquo;s devices or other equipment, or any phishing, spoofing or other attack, unless such damage or interruption directly resulted from our gross negligence, fraud, or willful misconduct;</span></p><p class="c1"><span class="c4">7.2.2. Bastion shall not be liable for any loss that the User may sustain due to compromise of login credentials, loss of mnemonic phrases, and/or failure to follow or act on any notices or alerts that we may send to the User;</span></p><p class="c1"><span class="c4">7.2.3. Bastion shall not be liable for any losses or liabilities arising from the input of wrong destination or recipient blockchain addresses; and</span></p><p class="c1"><span class="c4">7.2.4. Bastion shall not be liable for any losses or liabilities arising from or relating to a breach of the User&rsquo;s own obligations set out in this Wallet Agreement.</span></p><p class="c1"><span class="c4">7.3. The limitations set forth in this Clause 7 will not limit or exclude liability for the gross negligence, fraud or intentional or willful misconduct of Bastion or its affiliates.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">8. Confidentiality</span></p><p class="c1"><span>8.1. As used in this Wallet Agreement, &ldquo;</span><span class="c6">Confidential Information</span><span>&rdquo; means any information and data of any kind that a disclosing Party (&ldquo;</span><span class="c6">Discloser</span><span>&rdquo;) designates as being confidential or which, under the circumstances surrounding disclosure, ought to be treated as confidential whether disclosed before, on or after the date of the latest version of this &nbsp;Wallet Agreement by the Discloser or any of its representatives, to the receiving Party (&ldquo;</span><span class="c6">Recipient</span><span class="c4">&rdquo;) or any of its representatives, and includes without limitation, technology, information and/or personal data provided by the Discloser, its related corporations, affiliates, representatives, employees, agents, representatives, independent contractors, advisors or consultants, whether disclosed or communicated verbally, in writing, or in any other tangible form, and whether relating to the Discloser&rsquo;s business, operations, processes, plans, strategies, requirements, inventions, product or service information, pricing, know-how, design rights, trade secrets, software, systems, negotiations, discussions, and contracts with other companies, customers, the existence, nature, status and content of discussions or negotiations between the parties, including any termination of those discussions or negotiations, any copy, report, forecast, valuation, analysis, compilation, study, memorandum, note or other document or material prepared by or for the Recipient or any of its representatives that contains or reflects or is otherwise based upon (whether in whole or in part) any of the information described herein. including, without limitation information relating to the Discloser&rsquo;s business operations or business relationships, financial information, transaction records, fee arrangements, transactions, accounts, personal data, pricing information, business plans, customer lists, data, records, reports, trade secrets, software, formulas, inventions, techniques, strategies and any data or information designated as confidential by the Discloser or which would be understood by a reasonable person as being of a confidential nature.</span></p><p class="c1"><span class="c4">8.2 A Recipient will not disclose any Confidential Information to any third party without the prior written consent of the Discloser, except as provided to such third party&rsquo;s officers, directors, agents, employees, consultants, contractors and professional advisors who need to know the Confidential Information for the purposes of performance of this Wallet Agreement and who are informed of, and who agree to be or are otherwise bound by obligations of confidentiality no less restrictive than, the obligations set forth herein. The Recipient will protect such Confidential Information from any unauthorized access, use and disclosure and shall exercise in relation to such Confidential Information no lesser security and control measures and degree of care than those which the Recipient applies to its own Confidential Information. The Recipient shall not use the Discloser&rsquo;s Confidential Information for any purpose other than to perform its obligations or exercise its rights under this Wallet Agreement. The obligations herein shall not apply to:</span></p><ul class="c3 lst-kix_list_25-0 start"><li class="c1 c12 li-bullet-2"><span class="c4">any information that is or becomes generally publicly available through no fault of the Recipient;</span></li><li class="c1 c12 li-bullet-8"><span class="c4">any information that the Recipient obtains from a third party (other than in connection with this Wallet Agreement) that, to the Recipient&#39;s best knowledge, is not bound by a confidentiality agreement prohibiting such disclosure; and</span></li><li class="c1 c12 li-bullet-2"><span class="c4">any information that is independently developed or acquired by the Recipient without the use of or reference to Confidential Information of Discloser.</span></li></ul><p class="c1"><span class="c4">8.3 Confidential Information includes all documents and other tangible objects containing or representing Confidential Information and all copies or extracts thereof or notes derived therefrom that are in the possession or control of the Recipient and all of the foregoing shall be and remain the property of the Discloser. At the Discloser&rsquo;s request, the Recipient shall return or destroy all Confidential Information; provided, however, the Recipient may retain one copy of Confidential Information:</span></p><p class="c1"><span class="c4">8.3.1 if required by applicable laws; or</span></p><p class="c1"><span class="c4">8.3.2 pursuant to a bona fide and consistently applied document retention policy; provided, further, that in either case, any Confidential Information so retained by the Recipient shall remain subject to the confidentiality obligations of this Wallet Agreement.</span></p><p class="c1"><span class="c4">8.4 If the Confidential Information disclosed under this Wallet Agreement or any other agreement that we may enter into with you includes the personal data of the Discloser and/or personal data of any individual:</span></p><p class="c1"><span class="c4">8.4.1 the Discloser hereby consents to the collection, processing, use and disclosure of its personal data by the Recipient in accordance with this Wallet Agreement and our Privacy Policy; and</span></p><p class="c1"><span class="c4">8.4.2 the Discloser hereby undertakes, represents and warrants to the Recipient that the Discloser has notified such individual of the purposes for which the Recipient may use his/her personal data and has obtained such individual&rsquo;s consent for the collection, processing, use and disclosure of his/her personal data by the Recipient in accordance with this Wallet Agreement and our Privacy Policy.</span></p><p class="c1"><span class="c4">8.5 The Discloser acknowledges and agrees that any consent given pursuant to this Wallet Agreement in relation to personal data shall survive death, incapacity, bankruptcy or insolvency of any such individual and the termination or expiration of this Wallet Agreement.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">9. Term and Termination</span></p><p class="c1"><span>9.1</span><span class="c8">. Validity:</span><span>&nbsp;This Wallet Agreement shall become effective on the date that the User subscribes for and/or uses the Services and will continue for such period notified to the User in the quotation containing the Subscription Fee, unless earlier terminated in accordance with this Wallet Agreement (the &ldquo;</span><span class="c6">Initial Duration</span><span>&rdquo;). After the Initial Duration, the Terms will automatically renew for successive one-year periods (each a &ldquo;</span><span class="c6">Renewal</span><span class="c7">&nbsp;</span><span class="c6">Duration</span><span>&rdquo;), unless either Party notifies the other of its intention not to renew at least 30 days prior to the expiration of the then-current Duration. &ldquo;</span><span class="c6">Duration</span><span class="c4">&rdquo; means the Initial Duration and any Renewal Duration.</span></p><p class="c1"><span>9.2. </span><span class="c8">Termination:</span></p><p class="c1"><span class="c4">9.2.1. Bastion may terminate this agreement and the User&rsquo;s Account Subscription(s) with immediate effect by notice in writing if:</span></p><p class="c1"><span class="c4">*******. The User has breached any of the User&rsquo;s undertakings or obligations in this Wallet Agreement;</span></p><p class="c1"><span class="c4">*******. Any of the User&rsquo;s representations or warranties in this Wallet Agreement turn out to be false, misleading, inaccurate or incomplete;</span></p><p class="c1"><span class="c4">*******. The User has failed to pay any amount due under this Wallet Agreement on the due date for payment and remains in default not less than thirty (30) days after being notified in writing to make such payment;</span></p><p class="c1"><span class="c4">*******. The User has committed a material breach of any of this Wallet Agreement and (if such breach is remediable) fail to remedy that breach within a period of seven (7) calendar days after being notified in writing to do so; or</span></p><p class="c1"><span class="c4">*******. Bastion and/or the Merchant is/are required to do so under any applicable law(s) or pursuant to any request by any governmental or regulatory body.</span></p><p class="c1"><span class="c4">9.2.2 The User may terminate this Wallet Agreement and the Account Subscription(s) with immediate effect by notice in writing if Bastion and/or the Merchant:</span></p><ul class="c3 lst-kix_list_26-0 start"><li class="c1 c12 li-bullet-2"><span class="c4">discontinues support for any of the digital assets which the User holds in the digital wallet;</span></li><li class="c1 c12 li-bullet-2"><span class="c4">discontinues support for the signature algorithm which the User intends to rely on for any Supported Digital Assets;</span></li><li class="c1 c12 li-bullet-9"><span class="c4">revises the Subscription Fee and/or billing cycle and the User indicates that it does not wish to continue such Account Subscription(s) within fourteen (14) calendar days of such notice; or</span></li><li class="c1 c12 li-bullet-2"><span class="c4">revises the Committed Monthly Percentage.</span></li></ul><p class="c1"><span class="c4">9.2.3 In the event of termination pursuant to any of the events listed in Clause 9.2.2, Bastion may, in its absolute discretion, refund the User the pro-rated amount of the Subscription Fee paid for the relevant calendar month.</span></p><p class="c1"><span class="c4">9.2.4 On termination of this Wallet Agreement for any reason:</span></p><ul class="c3 lst-kix_list_27-0 start"><li class="c0 li-bullet-10"><span class="c4">all licenses granted under this Wallet Agreement shall immediately terminate and the User shall immediately cease all use of the Services, Software and/or the Documentation;</span></li><li class="c0 li-bullet-11"><span class="c4">each Party shall return and make no further use of any Confidential Information, Documentation and other items (and all copies of them) belonging to the other Party;</span></li><li class="c0 li-bullet-12"><span class="c4">Bastion may destroy or otherwise dispose of any of the User Data in our possession unless we receive, no later than ten days after the effective date of the termination of this Wallet Agreement, a written request for the delivery to the User of the then most recent back-up of your User Data. Bastion shall use commercially reasonable endeavours to deliver the electronic back-up to the User within 30 days of our receipt of such a written request, provided that the User, at that time, paid all fees and charges outstanding at and resulting from termination (whether or not due at the date of termination). The User shall pay all reasonable expenses incurred by us in returning or disposing of your User Data; and</span></li><li class="c0 li-bullet-11"><span class="c4">any rights, remedies, obligations or liabilities of the Parties that have accrued up to the date of termination, including the right to claim damages in respect of any breach of this Wallet Agreement which existed at or before the date of termination shall not be affected or prejudiced.</span></li></ul><p class="c1"><span class="c4">9.2.5 Clauses 5, 6, 7, 8, 9.2.3, 9.2.4, 9.2.5 and 11 shall survive any termination or expiry of this Wallet Agreement.</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">10. Force Majeure</span></p><p class="c1"><span class="c4">10.1. Bastion shall not be liable for any delays, failure in performance or interruption of the Services which result directly or indirectly from any Force Majeure Event.</span></p><p class="c1"><span>10.2. For the Purpose of this Clause 10 &ldquo;</span><span class="c6">Force Majeure Event</span><span class="c4">&rdquo; shall mean any event beyond Bastion&rsquo;s and/or the Merchant&rsquo;s reasonable control which renders impossible or hinders Bastion&rsquo;s performance of this Wallet Agreement, including without limitation:</span></p><p class="c1 c2"><span class="c4">(a) war, riot, civil unrest or revolution, sabotage, terrorism, insurrection, acts of civil or military authority, imposition of sanctions, embargo, breaking off of diplomatic relations or similar actions;</span></p><p class="c1 c2"><span class="c4">(b) terrorist attacks, civil war, civil commotions or riots;</span></p><p class="c1 c2"><span class="c4">(c) acts of God, epidemic, pandemic, flood, earthquake, typhoon or other natural disasters or adverse weather or environmental condition;</span></p><p class="c1 c2"><span class="c4">(d) any act of state or other exercise of sovereign, judicial or executive prerogative by any government or public authority, including expropriation, nationalization, imposition of an export or import restriction, quota or prohibition, or compulsory acquisition or acts claimed to be justified by executive necessity;</span></p><p class="c1 c2"><span class="c4">(e) fire, explosion or accidental damage;</span></p><p class="c1 c2"><span class="c4">(f) collapse of building structures or failure of plant machinery, computers or vehicles;</span></p><p class="c1 c2"><span class="c4">(g) interruption in telecommunications or Internet services or network provider services, or failure of equipment or software;</span></p><p class="c1 c2"><span class="c4">(h) hacks, mining attacks (including but not limited to double-spend attacks, majority mining power attacks and &ldquo;selfish-mining&rdquo; attacks), smurfing, phishing, sybil attacks, distributed denial of service, a &ldquo;fork&rdquo; of the blockchain which may result in more than one version of the Supported Digital Asset, cyber-attacks, and any fraudulent activity on the part of a third party;</span></p><p class="c1 c2"><span class="c4">(i) interruption or failure of utility service, including but not limited to electric power, gas or water; or</span></p><p class="c1 c2"><span class="c4">(j) any labour disputes, including but not limited to strikes, industrial action or lockout.</span></p><p class="c1"><span class="c4">The Party not affected by the Force Majeure Event may terminate this Wallet Agreement with immediate effect by providing written notice to the other Party if the other Party remains unable to perform due to the Force Majeure Event for a period of more than fifteen (15) days following the date of the Force Majeure Event.</span></p><p class="c1"><span class="c4">10.3. For the avoidance of doubt, neither occurrence of a Force Majeure Event or the termination of this Wallet Agreement in connection therewith shall relieve either Party from any accrued obligations then subsisting prior to the occurrence of the Force Majeure Event or termination of this Wallet Agreement in connection therewith (including any payment obligations for outstanding sums).</span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1 c5"><span class="c4"></span></p><p class="c1"><span class="c24 c7">11. Miscellaneous</span></p><p class="c1"><span>11.1. </span><span class="c8">Notices:</span></p><p class="c1"><span>11.1.1 Any notices, demands or other communications hereunder shall be in writing and may be served, in the case of Bastion only, by way of publication on our website accessible at </span><span class="c21 c26"><a class="c20" href="https://www.google.com/url?q=https://polity.li&amp;sa=D&amp;source=editors&amp;ust=1719482618230793&amp;usg=AOvVaw2rqujQVw47JDTQbaHoEPxw">https://polity.li</a></span><span class="c4">&nbsp;in-application notification, text messages, instant messaging and/or other electronic means or sent or distributed via email:</span></p><p class="c1"><span class="c4">Name: Bastion Cooperative. </span></p><p class="c1"><span>Email: </span><span class="c21 c26"><a class="c20" href="mailto:<EMAIL>"><EMAIL></a></span><span class="c4">.</span></p><p class="c36"><span>Element.io: </span><span class="c27 c21 c26">@politymvp:matrix.org</span></p><p class="c1"><span class="c4">If to you, to:</span></p><p class="c1"><span>Name: </span><span class="c13">____________________________</span><span class="c4">Name of person subscribing for the Account Subscription;</span></p><p class="c1"><span>Email: </span><span class="c13">____________________________</span><span class="c4">Your email address as notified to Bastion during the onboarding process and updated from time to time;</span></p><p class="c1"><span>Element.io: </span><span class="c13">____________________________</span><span class="c4">Your Element.io address as notified to Bastion during the onboarding process and updated from time to time;</span></p><p class="c1"><span class="c4">11.1.2. Any such communication shall be deemed duly given:</span></p><ul class="c3 lst-kix_list_28-0 start"><li class="c0 li-bullet-13"><span class="c4">in the case of publication on our website (applicable only to Bastion), to the User by close of business on the date of publication of such notice, demand or communication on our website, provided that if such day is not a Business Day or such time not a normal business hour then delivery shall be deemed to have occurred on the following Business Day;</span></li><li class="c0 li-bullet-11"><span class="c4">in the case of communication via in-application notification, text messaging, instant messaging and/or other electronic means, on the day such notification, text message, instant message and/or other electronic medium was sent;</span></li><li class="c0 li-bullet-11"><span class="c4">in the case of communication via email, when sent to Bastion&rsquo;s or the User&rsquo;s email address, by close of business of the date of transmission by the email server used by Bastion or the User and/or the service provider subject to confirmation of successful transmission, provided that if such day is not a Business Day or such time not a normal business hour then delivery shall be deemed to have occurred on the following Business Day.</span></li><li class="c0 li-bullet-13"><span class="c4">Bastion shall not be liable for any failures or delays in the delivery of such notice to the User due to reasons not attributable to Bastion (including but not limited to the provision of inaccurate or invalid contact details, and network failures by third party service providers).</span></li></ul><p class="c1"><span>11.2. </span><span class="c8">Entire Agreement</span><span class="c4">: This Wallet Agreement constitutes the entire agreement between the Parties with respect to the subject matter hereof and supersedes all prior oral and written discussions, memoranda, understandings and undertakings between them.</span></p><p class="c1"><span>11.3. </span><span class="c8">Remedies</span><span class="c4">: No remedy conferred by any of the provisions of this Wallet Agreement is intended to be exclusive of any other remedy which is otherwise available at law, in equity, by statute or otherwise, and each and every other remedy shall be cumulative and shall be in addition to every other remedy given hereunder or now or hereafter existing at law, in equity, by statute or otherwise, provided always that the Parties shall not be entitled to rescind this Wallet Agreement on grounds of misrepresentation. The election of any one or more of such remedies by any of the Parties shall not constitute a waiver by such Party of the right to pursue any other available remedies.</span></p><p class="c1"><span>11.4. </span><span class="c8">Waiver</span><span class="c4">: The rights and remedies of each Party shall not be affected by any failure to exercise or delay in exercising any right or remedy or by the giving of any indulgence or by anything whatsoever except a specific waiver or release in writing and any such waiver or release shall not prejudice or affect any other rights or remedies of such Party. No single or partial exercise of any right or remedy shall prevent any further or other exercise thereof or the exercise of any other right or remedy.</span></p><p class="c1"><span>11.5. </span><span class="c8">Time of Essence</span><span class="c4">: Any time or period mentioned in any provision of this Wallet Agreement may be extended by mutual agreement between the Parties but as regards any time, date or period originally fixed or any time, date or period so extended as aforesaid time shall be of the essence.</span></p><p class="c1"><span>11.6. </span><span class="c8">Successors and Assigns</span><span class="c4">: No Party shall have the right to assign all or any part of its interest in this Wallet Agreement without the prior written consent of the other Party. This Wallet Agreement shall be binding upon and shall inure to the benefit of the Parties and their successors and permitted assigns.</span></p><p class="c1"><span>11.7. </span><span class="c8">Severability:</span><span class="c4">&nbsp;If any provision or any portion of any provision of this &nbsp;Wallet Agreement or the application of any such provision or any portion thereof to any person or circumstance, shall be held invalid or unenforceable, the remaining portion of such provision and the remaining provisions of this Wallet Agreement, and the application of such provision of portion of such provision as is held invalid or unenforceable to persons or circumstances other than those as to which it is held invalid or unenforceable, shall not be affected thereby.</span></p><p class="c1"><span class="c4">11.8 Third Party Rights: A person who is not a party to this Wallet Agreement has no right under the Contracts (Rights of Third Parties) Act 2001, to enforce any Terms, but our assignees, affiliates, third party Merchants and contract service providers shall be deemed beneficiaries of this Wallet Agreement as if they are parties thereto and shall have the rights to enforce the provisions of this Wallet Agreement.</span></p><p class="c1"><span>11.9. </span><span class="c8">Governing Law</span><span class="c4">: This Wallet Agreement shall be governed by and construed in accordance with the laws of Liechtenstein.</span></p><p class="c1"><span>11.10. </span><span class="c8">Dispute Resolution</span><span class="c4">:</span></p><p class="c1"><span>11.10.1. Parties shall first attempt to resolve any dispute, controversy or claim arising under, out of, in connection with or in relation to this &nbsp;Wallet Agreement, including any dispute as to its existence, validity, interpretation, performance, breach or termination and any dispute relating to any non-contractual obligations arising out of or in connection with it (&ldquo;</span><span class="c6">Dispute</span><span>&rdquo;) shall, so far as possible, amicably by mutual consultation between Parties. The amicable resolution should be commenced as soon as possible after a disputing Party delivers a notice of dispute to another disputing Party (&ldquo;</span><span class="c6">Dispute Notice</span><span class="c4">&rdquo;).</span></p><p class="c1"><span class="c4">11.10.2. Failing an amicable settlement within 30 calendar days from the date of the Dispute Notice, the disputing Parties agree that the Dispute shall be referred to and finally resolved by arbitration administered by the Liechtenstein International Arbitration Court in accordance with its rules currently in force, which rules are deemed to be incorporated by reference in this Clause 11.10. The seat of the arbitration shall be Liechtenstein. The language of the arbitration shall be English.</span></p><p class="c1 c5"><span class="c4"></span></p><div><p class="c5 c37"><span class="c4"></span></p></div></body></html>