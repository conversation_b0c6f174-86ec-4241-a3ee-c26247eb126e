#!/bin/bash

# ---
# read inputs

SCRIPT_NAME=$0
CMD=$1
APP=$2

APPS_LIST=$(echo $(ls src/apps))
CMDS_LIST="start build"

# ---
# utils

function in_list() {
  LIST=$1
  VAL=$2
  for x in $LIST; do
    if [ "$x" = "$VAL" ]; then
        return 0
    fi
  done
  return 1
}

function format_list() {
  LIST=$1
  echo $LIST | tr ' ' ','
}

function show_usage() {
  echo usage:
  echo "      $SCRIPT_NAME {$(format_list "$CMDS_LIST")}"
  echo "      $SCRIPT_NAME {$(format_list "$CMDS_LIST")} {$(format_list "$APPS_LIST")}"
}

# ---
# validate command

if ! in_list "$CMDS_LIST" $CMD; then
  echo Wrong command
  show_usage
  exit 1
fi

# ---
# if app is "all", run everything

if [ -z $APP ]; then
  COMMANDS=$(
    for APP in $APPS_LIST; do
      echo "'$SCRIPT_NAME $CMD $APP'"
    done
  )
  echo "$CMD"ing ALL applications
  echo $COMMANDS | xargs npx concurrently
  exit 0
fi

# ---
# validate app name

if ! in_list "$APPS_LIST" $APP; then
  echo Wrong app name
  show_usage
  exit 1
fi

# ---
# run specific app

echo "$CMD"ing "'$APP'" application
source $(dirname $SCRIPT_NAME)/read-env.sh $APP
yarn $CMD
