const fs = require('fs')
const path = require('path')

// TODO: remove this whole script and generated data when backend will have a proper api endpoint
const ALL_COUNTRIES = require('world-countries')

const targetDir = path.resolve(process.cwd(), 'src/api/rtq/commonAPI')

if (fs.existsSync(targetDir)) {
  fs.writeFileSync(
    path.join(targetDir, 'countries.json'),
    JSON.stringify(generate(), null, 2)
  )
} else {
  console.error(
    `Can't generate countries list – target folder does not exist:\n - ${targetDir}`
  )
}

function generate() {
  return ALL_COUNTRIES.map(x => ({
    name: x.name.common,
    code: x.cca2,
    flag: x.flag,
  }))
}
