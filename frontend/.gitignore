# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
# `build` is used in prod env (defined in react-scripts config)
/build
# `dist` is used in dev env (webpack's default)
/dist

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.eslintcache
.stylelintcache
