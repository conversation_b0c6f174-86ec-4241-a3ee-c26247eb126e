## Brief app structure

App consists of 3 major parts: 
* `auth portal`: all of "sign up" / "sign in" logic; also, intermediate interface "choose avatar" for Investor users 
* `cabinet` (aka "user infrastructure"): personal cabinet for Adviser and Avatar users
* `operator`: kind of "admin panel", overview of system statistics. For Operator users only.

Those are independent services and all hosted on different (sub)domains.
Thus, they have no shared code chunks between each other. Each service bundle is a standalone app.  

After authorization, auth portal redirects user to either infrastructure service or operator dashboard, depending on user role.

Unauthorized user **MUST NOT** have access to cabinet / operator apps at all.

---

## Source code

* `apps/auth` – code of auth portal service
* `apps/cabinet` – code of user infrastructure service
* `apps/operator` – code of operator service
* the rest of `src` folder – shared code for all apps

---

## env config

By default, service will run at `localhost:3000`.

> Remember that you can use [react-app env vars](https://create-react-app.dev/docs/advanced-configuration) to customize launch.

> [Using env files with react-app](https://create-react-app.dev/docs/adding-custom-environment-variables#adding-development-environment-variables-in-env)

Common env settings are in `.env`.

Env settings for specific apps are in `.env.{app_name}`.

For your personal env settings, append `.local` to corresponding file name. 

Local files are ignored by git and take precedence over normal files.

App-specific files take precedence over general ones.

---

## Launch scripts

First of all – regardless of how you'll run FE, you must set the address of auth portal app.

By default, this app runs at `localhost:3000`, so do this: 
```
# .env.local
REACT_APP_AUTH_PORTAL_URL=http://localhost:3000/
```

### In Docker container

```
# launch dev server
yarn start

# build static files
yarn build
```

**NOTE**: remember to use corresponding `.env.{app}` file in container settings.

Nothing else should be required.

### Directly on host machine

It's a preferred way for developer, since building FE inside docker container takes nearly eternity and tends to have troubles with detecting file changes (especially on MacOS).

```
# run specific app
yarn app {start,build} $app_name_here

# run all apps at once
yarn app {start,build}
```

By default, any app runs at `localhost:3000`.

So to run multiple apps at once, you must set different ports for each:

```
# .env.auth.local
PORT=3000

# .env.cabinet.local
PORT=3001

# .env.operator.local
PORT=3002
```

## Backend options when running FE on host machine

#### [`json-server`](https://github.com/typicode/json-server)

```
yarn server
```

Provides dummy data for all endpoints.

It may not handle some mutations, but provides all queries.

See `frontend/server` folder.

By default, runs at `localhost:4000`.
Use `JSON_SERVER_PORT` var to change port.

To use it, target FE proxies to json-server. You can do it for all apps at once:
```
# .env.local
API_PROXY_TARGET=http://localhost:4000
BWI_API_PROXY_TARGET=http://localhost:4000
```

#### Docker

If you run real backend, target proxy for each app to a corresponding BE container:
```
# .env.auth.local
API_PROXY_TARGET=http://localhost:8080

# .env.cabinet.local
API_PROXY_TARGET=http://localhost:8081
BWI_API_PROXY_TARGET=http://localhost:8083

# .env.operator.local
API_PROXY_TARGET=http://localhost:8082
```

---

## Auth

With json-server, you don't need to go through Fractal.id. On auth portal, start with `/login?token=test` route – with it, you'll be able to log in as any user role.

### 2FA

Currently, 2FA is emulated. Verification code is static. Value see [here](src/apps/auth/features/User/api.ts) (in func `
verifySecurityCode`). 

## Logging

Logging is done via [debug](https://www.npmjs.com/package/debug) package. Namespace `bastion`.

In dev env, use `window.node_debug` object to manage logger behavior.

See `src/log.ts`.

### Custom env vars

| Name                               | Required |                                                        Default                                                        | Description                                                                                                                                      |        env file       |
|------------------------------------|:--------:|:---------------------------------------------------------------------------------------------------------------------:|--------------------------------------------------------------------------------------------------------------------------------------------------|:---------------------:|
| APP                                | **yes**  |                                                                                                                       | App name to launch                                                                                                                               |     `.env.{name}`     |
| REACT_APP_AUTH_PORTAL_URL          | **yes**  |                                                                                                                       | Where to redirect from cabinet app after user did log out                                                                                        |         `.env`        |
| REACT_APP_ELEMENTIO_HOST           |   *no*   |                                                    app.element.io                                                     | Where instance of `element.io` application hosts.                                                                                                |         `.env`        |
| REACT_APP_MATRIX_DOMAIN            |   *no*   |                                                      matrix.org                                                       | Domain in which `element.io` usernames are resolved - i.e. part after `:` in `@username:domain.name`                                             |         `.env`        |
| REACT_APP_MATRIX_SERVER            |   *no*   |                                         value of {{REACT_APP_MATRIX_DOMAIN}}                                          | API server used by `element.io` application. May differ arbitrary from MATRIX_DOMAIN.                                                            |         `.env`        |
| REACT_APP_MATRIX_USER              | **yes**  |                                                                                                                       | User ID of `element.io` system account.<br/>In short form – i.e. without `@` and `:MATRIX_DOMAIN` parts                                          |         `.env`        |
| REACT_APP_MATRIX_PWD               | **yes**  |                                                                                                                       | Password of `element.io` system account                                                                                                          |         `.env`        |
| REACT_APP_DEFAULT_POLLING_INTERVAL |   *no*   |                                                         30000                                                         | efault interval for all long-polling requests. <br/>In milliseconds.                                                                             | `.env` / `.env.{name}` |
| REACT_APP_DEBUG_FORMS              |   *no*   |                                                                                                                       | Enable fancy debugger interface for all forms by default. <br/>Set to `true` to enable. <br/>Only works when `NODE_ENV=development`              | `.env` / `.env.{name}` |
| API_PROXY_TARGET                   |   *no*   |                                                                                                                       | Where to proxy all requests starting with `/api`.<br/>Use for mocking real server (with `json-server` or whatever). <br/>See `src/setupProxy.js` |     `.env.{name}`     |
| BWI_API_PROXY_TARGET               |   *no*   |                                                                                                                       | Where to proxy all requests starting with `/bwi`.<br/>See `src/setupProxy.js`                                                                    |     `.env`     |
| ANALYZER_PORT                      |   *no*   |                                                         8888                                                          | Where to launch [webpack-bundle-analyzer](https://www.npmjs.com/package/webpack-bundle-analyzer)                                                 |     `.env.{name}`     |
| REACT_APP_FRACTAL_CLIENT_ID        | **yes**  |                                                                                                                       | [`client_id` param of Fractal App](https://docs.developer.fractal.id/getting-started#obtaining-client-id-and-client-secret)                      |      `.env.auth`      |
| REACT_APP_FRACTAL_AUTH_DOMAIN      | **yes**  |                                                                                                                       | `https://fractal.id` (prod) or `https://app.next.fractal.id` (sandbox)                                                                           |      `.env.auth`      |
| REACT_APP_BWI_HOST                 | **yes**  |                                      `http://wallet-infrastructure-service:8083`                                      | Host where for `wallet-infrastructure-service` located                                                                                           |       `.env`                                                                                                                                     |
| REACT_APP_BWI_JWT_DEV              |   *no*   |                                                                        | **[DEV ONLY]** JWT token to authorize at wallet-infrastructure-service. Use in dev env, when need to proxy to staging server.                    |       `.env`                                                                                                                                     |
| REACT_APP_WALLET_CONNECT2_PROJECT_ID              | **yes**  |                                                                        | `Project ID` of your app at [wallectonnect cloud](https://cloud.walletconnect.com/)                                                          |       `.env`                                                                                                                                     |
