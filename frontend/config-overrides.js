const fs = require('fs')
const path = require('path')

const {
  useBabelRc,
  override,
  addBundleVisualizer,
  getBabelLoader,
} = require('customize-cra')
const detectPort = require('detect-port')
const { merge } = require('lodash')
const LodashModuleReplacementPlugin = require('lodash-webpack-plugin')
const StyleLintPlugin = require('stylelint-webpack-plugin')
const { NormalModuleReplacementPlugin } = require('webpack')

const isProdEnv = process.env.NODE_ENV === 'production'
const APPS_FOLDER = 'src/apps'

module.exports = override(
  useBabelRc(),

  config => {
    // Loader handling files from node_modules
    const loader = getBabelLoader(config, true)

    merge(loader, {
      options: {
        // Somehow, setting "babelrc: true" does not work here. No idea why.
        // Plugins defined in babelrc are not applied here. Only inline definitions work.
        // TODO:
        //  this all should be solved by simply using preset-env
        //  but this "simply" causes weird build errors in seemingly unrelated packages
        //  Need to migrate this whole thing to Vite and forget of babel patching
        plugins: [
          require.resolve(
            '@babel/plugin-proposal-logical-assignment-operators'
          ),
          require.resolve('@babel/plugin-proposal-nullish-coalescing-operator'),
          require.resolve('@babel/plugin-proposal-optional-chaining'),
          require.resolve('@babel/plugin-proposal-class-properties'),
        ],
      },
    })

    return config
  },

  // @link https://www.npmjs.com/package/webpack-bundle-analyzer
  addBundleAnalyzerPlugin({
    analyzerHost: '0.0.0.0',
    openAnalyzer: false,
    analyzerMode: isProdEnv ? 'static' : 'server',
    generateStatsFile: true,
  }),

  // @link https://github.com/lodash/lodash-webpack-plugin
  addPlugin(
    new LodashModuleReplacementPlugin({
      paths: true,
      flattening: true,
      currying: true,
      collections: true,
    })
  ),

  // @link https://www.npmjs.com/package/stylelint-webpack-plugin
  addPlugin(
    new StyleLintPlugin({
      fix: true,
    })
  ),

  // Separate chunk for react-modules, for better build analysis.
  config => {
    merge(config.optimization, {
      splitChunks: {
        cacheGroups: {
          react: {
            test: /[\\/]node_modules[\\/].*react.*/,
            reuseExistingChunk: true,
          },
        },
      },
    })
    return config
  },

  // Fix resolution for native modules
  // @see https://github.com/reactioncommerce/reaction-component-library/issues/399#issuecomment-467860022
  config => {
    config.module.rules.push({
      test: /\.mjs$/,
      include: /node_modules/,
      type: 'javascript/auto',
    })

    return config
  },

  addPlugin(
    new NormalModuleReplacementPlugin(
      /^src\/App$/,
      path.join(APPS_FOLDER, resolveAppName())
    )
  )
)

// ---

function addPlugin(plugin) {
  return config => {
    config.plugins.push(plugin)
    return config
  }
}

function resolveAppName() {
  const APP_NAME = process.env.APP
  const appNames = fs
    .readdirSync(APPS_FOLDER)
    .map(x => path.basename(x, path.extname(x)))

  if (!appNames.includes(APP_NAME)) {
    throw new Error(
      `Unknown app name: ${APP_NAME}.\nValid values are: ${appNames.join(', ')}`
    )
  }

  return APP_NAME
}

function addBundleAnalyzerPlugin(opts) {
  let port = process.env.ANALYZER_PORT || 8888

  // Find free port, like CRA does for webpack-dev-server port
  // It's not 100% reliable, since build won't wait for this operation
  // (in difference from CRA, where it's integrated into workflow)
  // But mostly, we can expect it will have enough time while CRA runs all it's async startup logic.
  detectPort(port, (err, freePort) => {
    if (err) {
      console.log(err)
    }

    if (freePort.toString() !== port.toString()) {
      console.log(
        `\n[WBA] analyzer port ${port} is occupied. Switching to ${freePort}\n`
      )
      port = freePort
    }
  })

  return addBundleVisualizer({
    ...opts,
    // Use getter to provide dynamic value
    get analyzerPort() {
      return port
    },
  })
}
