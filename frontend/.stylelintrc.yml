# @see https://stylelint.io/user-guide/rules/list

extends:
  - stylelint-config-css-modules

defaultSeverity: warning

rules:
  # @see https://stylelint.io/user-guide/rules/regex#enforce-a-case
  # snake_case – because of css modules.
  selector-class-pattern: "(^([a-z][a-z0-9]*)(_[a-z0-9]+)*$)"

  # @see https://stylelint.io/user-guide/rules/list/font-family-name-quotes/#options
  font-family-name-quotes: "always-unless-keyword"

  max-line-length:
    - 120
    - ignore:
        - comments
      # fonts lists may be quite long
      ignorePattern: "/font\\-family:/"

  color-hex-length: "short"

  length-zero-no-unit:
    - true
    - ignoreFunctions:
        - "var"
        - "calc"
        - "/^--/"

  # Often we want to group some props manually
  declaration-empty-line-before: null
  custom-property-empty-line-before: null

  # In very rare cases it's needed, disable this rule locally
  declaration-no-important: true

  number-leading-zero: always

  at-rule-empty-line-before:
    - always
    - except:
        - "first-nested"
      ignore:
        - "after-comment"
        - "blockless-after-same-name-blockless"

      ignoreAtRules:
        - "if"
        - "else"
        - "each"
        - "for"
        - "return"

  value-list-comma-space-after: always
  value-list-comma-space-before: "never-single-line"
  # This one is quite strange and requires strange-looking codestyle
  value-list-comma-newline-after: null

  block-closing-brace-newline-after: always
  rule-empty-line-before:
    - always
    - ignore:
        - after-comment
        - first-nested

  comment-whitespace-inside: null
  comment-empty-line-before: null

  # It's really hard to track where and why it will trigger
  # Feels like it rather complicates code
  no-descending-specificity: null

overrides:
  - # css-only rules
    files: "**/*.css"
    rules:
      at-rule-no-unknown:
        - true
        - ignoreAtRules:
            # @see https://www.npmjs.com/package/postcss-mixins
            - "define-mixin"
            - "mixin"

  - # scss-only rules
    files: "**/*.scss"
    extends:
      # @see https://github.com/stylelint-scss/stylelint-scss#list-of-rules
      - stylelint-config-standard-scss
      - stylelint-config-css-modules

    rules:
      # This rule enforces using only placeholder selectors in `@extend`.
      # Which is not always the real usecase.
      scss/at-extend-no-missing-placeholder: null

      scss/at-import-partial-extension: null
      scss/at-import-partial-extension-whitelist:
        - module

      # @see https://github.com/stylelint-scss/stylelint-scss/blob/HEAD/src/rules/no-global-function-names/README.md
      # It's either not supported by node-sass, or smth else,
      # but following this rule cause compiler to crash with like `SassError: There is no module with the namespace "color"`
      # Also not supported by editors (in particular, WebStorm).
      scss/no-global-function-names: null

      # it prevent readable formatting of long expressions
      scss/operator-no-newline-before: null

      scss/double-slash-comment-empty-line-before: null

      block-closing-brace-newline-after:
        - always
        - ignoreAtRules:
            - "if"
            - "else"

