import { WalletStatus } from 'src/apps/cabinet/features/Wallet/types'
import {
  IBaseNotification,
  INewWalletNotification,
  ITransactionNotification,
  NotificationType,
} from 'src/types'

import C, { list } from './generators'
import wallet_routes from './wallets'

const ALL_WALLETS = [
  ...wallet_routes.wallets_dfns,
  ...wallet_routes.wallets_safeheron,
]

export function createNotification<T extends IBaseNotification>(data: {
  id?: T['id']
  type: T['notificationType']
  payload: T['payload']
}): T {
  const { id = C.integer(), type, payload } = data
  return {
    id,
    uuid: C.uuid,
    sentAt: C.iso_offset(C.integer(-12, 0)),
    createdAt: C.iso_offset(C.integer(-12, 0)),
    isRead: C.boolean,
    notificationType: type,
    meta: {},
    payload,
  } as T
}

export const wallet_notifications = list(5, (): INewWalletNotification => {
  const wallet = C.sample(ALL_WALLETS)
  return createNotification({
    id: C.integer(100, 200),
    type: NotificationType.Wallet,
    payload:
      wallet === undefined
        ? {
            status: C.sample(WalletStatus),
            request_uuid: C.uuid,
            wallet_name: C.company_name,
          }
        : {
            status: wallet.status,
            request_uuid: wallet.request_id,
            wallet_name: wallet.wallet_name,
          },
  })
})

export const txn_notifications = list(
  5,
  (): ITransactionNotification =>
    createNotification({
      id: C.integer(200, 300),
      type: NotificationType.Txn,
      payload: {
        status: C.state,
        transactionId: C.uuid,
        amount: C.integer().toString(),
        asset: C.crypto,
        merchant: C.name,
        sourceAccount: C.uuid,
        destinationAccount: C.uuid,
        sourceWalletAddress: C.uuid,
        destinationWalletAddress: C.uuid,
        // declinedBy?: C.company_name,
        // declineReason?: C.sentence,
      },
    })
)

export default [...wallet_notifications, ...txn_notifications]
