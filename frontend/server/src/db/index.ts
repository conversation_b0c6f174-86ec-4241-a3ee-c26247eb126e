import AdvisorRoutes from './Advisor'
import AvatarRoutes from './Avatar'
import InvestorRoutes from './Investor'
import OperatorRoutes from './Operator'
import { notifications as agreementNotifications } from './agreements'
import BiddingRoutes from './bidding'
import DefiRoutes from './defi'
import FavAssetRoutes from './fav_assets'
import otherNotifications from './notifications'
import UserRoutes from './users'
import WalletRoutes from './wallets'

// ---

const DB = {
  ...UserRoutes,
  ...InvestorRoutes,
  ...AvatarRoutes,
  ...AdvisorRoutes,
  ...OperatorRoutes,
  ...FavAssetRoutes,
  ...WalletRoutes,
  ...DefiRoutes,
  ...BiddingRoutes,

  notifications: [...agreementNotifications, ...otherNotifications],
}

export default DB
