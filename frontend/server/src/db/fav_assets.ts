import {
  IFavoriteAssetsResponse,
  IFavoriteWalletAssetRaw,
} from 'src/apps/cabinet/features/Wallet/types'

import C, { CRYPTO_ASSETS, list } from './generators'

const favouriteAssets: IFavoriteAssetsResponse = {
  static_addresses: list(
    3,
    (): IFavoriteWalletAssetRaw => ({
      name: C.name,
      asset_symbol: C.crypto,
      address: C.uuid,
    })
  ),
  dynamic_addresses: CRYPTO_ASSETS.map(
    (asset_symbol): IFavoriteWalletAssetRaw => ({
      name: C.name,
      asset_symbol,
      address: C.uuid,
    })
  ),
}

const routes = {
  favourites_address: favouriteAssets,
}

export default routes
