import { flatMap } from 'lodash'

import {
  AgreementStatus,
  AgreementType,
  IAgreementNotification,
  IAgreementRaw,
  NotificationType,
} from 'src/types'

import C, { list } from './generators'
import { createNotification } from './notifications'

function getPrevStatus(status: AgreementStatus): AgreementStatus | undefined {
  const statuses = Object.values(AgreementStatus)
  const idx = statuses.indexOf(status)
  return C.sample([undefined, ...statuses.slice(0, idx)])
}

function getDeclineReason(status: AgreementStatus) {
  return status === AgreementStatus.Declined ? C.sentence : ''
}

function getDeclineBy(status: AgreementStatus) {
  return status === AgreementStatus.Declined
    ? C.sample(['Investor', 'Platform'])
    : ''
}

const combinations = flatMap(Object.values(AgreementType), type =>
  Object.values(AgreementStatus).map(status => [type, status] as const)
)

const agreements = combinations.map(([type, status], id): IAgreementRaw => {
  return {
    agreementID: id.toString(),
    agreementDate: C.iso_offset(),
    agreementType: type,
    status,
    adviser: C.name,
    avatar: C.name,
    investment: C.integer(1000, 300000).toString(),
    fee: `${C.integer(1, 99)}%`,
    // photo: C.photo(),
    // description: C.sentences(C.integer(2, 15)),
    filePath: C.file('pdf-sample.pdf'),

    platform: '',
    declineReason: getDeclineReason(status),
    declinedBy: getDeclineBy(status),
  }
})

const notifications = list(agreements.length, (id): IAgreementNotification => {
  const agreement = agreements.find(
    x => x.agreementID === id.toString()
  ) as IAgreementRaw

  return createNotification({
    id,
    type: C.sample([
      NotificationType.SafeheronAcquisition,
      NotificationType.DfnsAcquisition,
      NotificationType.NodeAcquisition,
    ]),
    payload: {
      issuer: agreement.adviser,
      recipients: [agreement.avatar],
      agreementId: agreement.agreementID,
      status: agreement.status,
      statusPrev: getPrevStatus(agreement.status),
      declineReason: getDeclineReason(agreement.status),
      declinedBy: getDeclineBy(agreement.status),
    },
  })
})

export { notifications, agreements }
