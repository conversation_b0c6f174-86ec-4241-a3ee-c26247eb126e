import { flatMap } from 'lodash'

import {
  IProductTier,
  IStoreProduct,
} from 'src/apps/cabinet/features/Bidding/types'

import C, { list, sample } from './generators'

const PRODUCTS = list(
  3,
  (): IStoreProduct => ({
    id: C.uuid,
    name: C.company_name,
    description: C.sentence,
    merchant: sample(['dfns', 'safeheron']),
  })
)

const routes = {
  bidding_products: PRODUCTS,

  bidding_tiers: flatMap(PRODUCTS, ({ id: product_id }) =>
    ['bronze', 'silver', 'gold'].map(
      (tier, i): IProductTier => ({
        id: C.uuid,
        product_id,
        tier_name: tier,
        buy_now_price: 10 * (i + 1),
      })
    )
  ),
}

export default routes
