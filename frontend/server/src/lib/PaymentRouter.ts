import { Router } from 'express'

import {
  ITransactionAPIParams,
  ITransactionVerifyResponse,
  IWCCallResponseTxn,
  TransactionVerifyStatus,
  TxnWarningSeverity,
} from 'src/apps/cabinet/features/Wallet/types'

export function setupPaymentRouter() {
  const router = Router()

  router.post(
    ['/payment/:provider/scan', '/assets/:asset/invoke/scan'],
    (req, res) => {
      const { to: receiver } = req.body as ITransactionAPIParams

      /* For simplicity of testing, to don't add extra testing parameters to request
       * just use predefined receiver values */
      switch (receiver) {
        case 'crash': {
          res.sendStatus(500)
          return
        }

        case 'warn': {
          const response: ITransactionVerifyResponse = {
            action: TransactionVerifyStatus.Warn,
            warnings: [
              {
                kind: 'NEW_DOMAIN',
                message:
                  'This domain is new or has not been reviewed yet. Proceed with caution.',
                severity: TxnWarningSeverity.Warning,
              },
            ],
          }
          res.status(200).send(response)
          return
        }

        case 'block': {
          const response: ITransactionVerifyResponse = {
            action: TransactionVerifyStatus.Block,
            warnings: [
              {
                kind: 'NEW_DOMAIN',
                message:
                  'This domain is new or has not been reviewed yet. Proceed with caution.',
                severity: TxnWarningSeverity.Critical,
              },
            ],
          }
          res.status(200).send(response)
          return
        }

        default: {
          const response: ITransactionVerifyResponse = {
            action: TransactionVerifyStatus.None,
            warnings: [],
          }
          res.status(200).send(response)
          return
        }
      }
    }
  )

  router.post(
    [
      '/payment/:provider',
      /* Added only for eth_sendTransaction calls.
       * For full support of all methods should be a separate endpoint: */
      '/assets/:asset/invoke',
    ],
    async (req, res) => {
      const response: IWCCallResponseTxn = {
        status: 'test',
        transaction_hash: 'test',
        transaction_id: 'test',
      }
      await new Promise(res => setTimeout(res, 2000))
      res.status(200).send(response)
    }
  )

  return router
}
