import { Router } from 'express'
import { JsonServerRouter } from 'json-server'

import DB from '../db'

export function setupNotificationsRouter(
  db: JsonServerRouter<typeof DB>['db']
) {
  const router = Router()

  router.post('/notifications', (req, res) => {
    const ids = req.body
    console.log('got ids', ids)
    db.get('notifications')
      .filter(x => ids.includes(x.id))
      .forEach(x => {
        x.isRead = true
      })
      .write()

    res.sendStatus(200)
  })

  return router
}
