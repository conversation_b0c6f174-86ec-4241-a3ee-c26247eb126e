import { Router } from 'express'
import { JsonServerRouter } from 'json-server'
import qs from 'qs'

import {
  ICreateWalletPayload,
  ITransactionHistoryEntry,
  WalletProvider,
  WalletStatus,
} from 'src/apps/cabinet/features/Wallet/types'
import { IPaginationResponseRaw } from 'src/types'

import DB from '../db'
import { CRYPTO_ASSETS, sample } from '../db/generators'
import { generateWallet } from '../db/wallets'

export function setupWalletsRouter(db: JsonServerRouter<typeof DB>['db']) {
  const router = Router()

  router.get('/:provider/:id/assets', (req, res) => {
    const provider = req.params.provider as WalletProvider
    const wallets = db.get(`wallets_${provider}`).value()
    const wallet = wallets.find(x => x.request_id === req.params.id)
    if (wallet === undefined) {
      res.sendStatus(404)
    } else {
      // eslint-disable-next-line no-underscore-dangle
      res.status(200).send(wallet._assets)
    }
  })

  router.get('/:provider/list/active/assets', (req, res) => {
    res.status(200).send(CRYPTO_ASSETS)
  })

  router.get('/:provider/:id/:asset/address', (req, res) => {
    res
      .status(200)
      .send([{ asset_address: '******************************************' }])
  })

  router.get('/:id/:asset/transactions', (req, res) => {
    const { id: wallet } = req.params
    const { limit: s_limit = '0', offset: s_offset = '0' } = req.query
    const limit = +s_limit
    const offset = +s_offset

    const data = db
      .get('wallets_txns')
      .value()
      .map(x => {
        const sender = sample([wallet, x.from])
        const receiver = sender === wallet ? x.to : wallet
        return {
          ...x,
          from: sender,
          to: receiver,
        }
      })
    const nextOffset = offset + limit
    const page = data.slice(offset, limit === 0 ? undefined : nextOffset)
    const response: IPaginationResponseRaw<ITransactionHistoryEntry> = {
      data: page,
      pagination: {
        next:
          nextOffset < data.length
            ? buildPaginationLink({ offset: nextOffset, limit })
            : '',
        prev:
          offset > 0
            ? buildPaginationLink({
                offset: Math.max(0, offset - limit),
                limit,
              })
            : '',
        total: data.length,
      },
    }
    res.status(200).send(response)
  })

  router.get('/:id', (req, res) => {
    const { id } = req.params
    const data = [
      ...db.get(`wallets_dfns`).value(),
      ...db.get(`wallets_safeheron`).value(),
    ]
    const item = data.find(x => x.request_id === id)
    if (item === undefined) {
      return res.sendStatus(404)
    }

    // eslint-disable-next-line no-underscore-dangle
    res.status(200).send(item._details)
  })

  router.post('/', async (req, res) => {
    const params = req.body as ICreateWalletPayload
    const { type: provider, wallet_name } = params
    const list = db.get(`wallets_${provider}`).value()
    list.push(
      generateWallet({
        provider,
        wallet_name,
        status: WalletStatus.In_progress,
      })
    )
    await new Promise(res => setTimeout(res, 2000))
    res.sendStatus(201)
  })

  return router
}

function buildPaginationLink(query: { offset: number; limit: number }) {
  const { offset, limit } = query
  return `http://whatever?${qs.stringify({
    offset,
    limit: limit || undefined,
  })}`
}
