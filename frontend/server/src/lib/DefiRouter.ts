import { Router } from 'express'
import { JsonServerRouter } from 'json-server'

import DB from '../db'

export function setupDefiRouter(db: JsonServerRouter<typeof DB>['db']) {
  const router = Router()

  const static200 = (path: string, dbKey: keyof typeof DB) => {
    router.get(path, (req, res) => {
      res.status(200).send(db.get(dbKey).value())
    })
  }

  static200('/:id/assets', 'defi_assets')
  return router
}
