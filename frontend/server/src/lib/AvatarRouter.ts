import { Router } from 'express'
import { JsonServerRouter } from 'json-server'

import { AgreementStatus } from 'src/types'

import DB from '../db'

export function setupAvatarRouter(db: JsonServerRouter<typeof DB>['db']) {
  const router = Router()

  router.post('/agreements/auto-sign', (req, res) => {
    res.sendStatus(200)
  })

  router.post('/agreements/accept', (req, res) => {
    const { agreementId } = req.body

    db.get('avatar_agreements')
      .find(x => x.agreementID === agreementId)
      .set('status', AgreementStatus.Signed)
      .write()

    res.status(200).send({
      agreementId,
      status: AgreementStatus.Signed,
    })
  })

  router.post('/agreements/decline', (req, res) => {
    const { agreementId, declineReason } = req.body

    db.get('avatar_agreements')
      .find(x => x.agreementID === agreementId)
      .set('status', AgreementStatus.Declined)
      .set('declineReason', declineReason)
      .set('declineBy', 'Avatar')
      .write()

    res.status(200).send({
      agreementId,
      status: AgreementStatus.Declined,
    })
  })

  return router
}
