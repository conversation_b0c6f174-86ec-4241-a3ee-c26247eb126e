/* eslint-disable no-console */
import sysPath from 'path'

import debug from 'debug'
import { static as serveStatic } from 'express'
import expressRewrite from 'express-urlrewrite'
import * as jsonServer from 'json-server'

import { PUBLIC_URL } from './const'
import DB from './db'
import { setupFractalRouter } from './lib/AuthRouter'
import { setupAvatarRouter } from './lib/AvatarRouter'
import { setupBiddingRouter } from './lib/BiddingRouter'
import { setupDefiRouter } from './lib/DefiRouter'
import { setupInvestorRouter } from './lib/InvestorRouter'
import { setupNotificationsRouter } from './lib/NotificationRouter'
import { setupPaymentRouter } from './lib/PaymentRouter'
import { setupWalletsRouter } from './lib/WalletsRouter'

debug.enable('express-urlrewrite')
// import { create_jwt_middleware } from './lib/jwt'

// types for this lib somehow only allow string as `from`, although lib itself does accept regexps
const rewrite = (from: string | RegExp, to?: string) =>
  expressRewrite(from as string, to)

export function setupServer() {
  const server = jsonServer.create()

  const dbRouter = jsonServer.router(DB)
  const middlewares = jsonServer.defaults()

  server.use(middlewares)
  // server.use(create_jwt_middleware(/\/auth(?!\/avatar)/))

  server.use(jsonServer.bodyParser)

  server.use(PUBLIC_URL, serveStatic(sysPath.resolve(__dirname, '../public')))
  const PREFIX = '/api/v1'
  server.use(`${PREFIX}/fractal-auth`, setupFractalRouter(dbRouter.db))
  server.use(`${PREFIX}/investor`, setupInvestorRouter(dbRouter.db))
  server.use(`${PREFIX}/avatar`, setupAvatarRouter(dbRouter.db))
  server.use(`${PREFIX}/wallet`, setupWalletsRouter(dbRouter.db))
  server.use(`${PREFIX}/defi`, setupDefiRouter(dbRouter.db))
  server.use(`${PREFIX}`, setupBiddingRouter(dbRouter.db))
  server.use(`${PREFIX}`, setupPaymentRouter())

  // ---
  // static routes from db file:

  server.use(rewrite('/api/v1/*', '/$1'))
  server.use(rewrite('/api/*', '/$1'))

  server.use(setupNotificationsRouter(dbRouter.db))

  /* By complicated reasons, agreement details api require specifying both type and id to be found.
  Both params are specified as path params, which complicates our test db structure.
  For this fake backend it's not needed. Select agreements by just id. */
  server.use(rewrite('/:path/agreements/:type/:id', '/:path/agreements/:id'))

  /**
   * Since db.json router doesn't support /any/nested/paths, trying to be smart here:
   * - define db routes as `/any_nested_paths`
   * - look for "compatible" parts in received url and replace them with db routes
   */
  const dbRoutes = Object.keys(DB)
  server.use((req, res, next) => {
    const pseudo_slash = '_'
    const pseudo_url = req.url
      .replaceAll(/^\/|\/$/g, '')
      .replaceAll('/', pseudo_slash)
    const route =
      // try to find the best match first
      dbRoutes.find(path => pseudo_url === path) ||
      dbRoutes.find(path => pseudo_url.startsWith(path))
    if (route !== undefined) {
      const replaceable = route.replaceAll(pseudo_slash, '/')
      console.log(
        '[db-route found]: %s : %s -> %s',
        req.url,
        replaceable,
        route
      )
      req.url = req.url.replaceAll(replaceable, route)
    }
    next()
  })

  // default router must be after all others routes
  server.use(dbRouter)

  // ---

  return server
}
