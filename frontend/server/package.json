{"private": true, "scripts": {"start": "NODE_OPTIONS='--inspect' ts-node src/index.ts", "watch": "nodemon", "repl": "ts-node -P tsconfig.json", "ws-bidding": "nodemon --exec 'ts-node' src/ws-server/bidding.ts"}, "dependencies": {"@types/express-form-data": "^2.0.2", "@types/express-jwt": "^6.0.4", "@types/json-server": "^0.14.4", "@types/jsonwebtoken": "^8.5.8", "@types/ws": "^8.5.5", "casual": "^1.6.2", "express-form-data": "^2.0.22", "express-jwt": "^6.1.1", "express-urlrewrite": "^2.0.0", "json-server": "^0.17.0", "jsonwebtoken": "^8.5.1", "nodemon": "^2.0.15", "ts-node": "^10.7.0", "tsconfig-paths": "^4.0.0"}}