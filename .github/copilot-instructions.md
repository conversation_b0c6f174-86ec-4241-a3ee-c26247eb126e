
# main-overview

## Development Guidelines

- Only modify code directly relevant to the specific request. Avoid changing unrelated functionality.
- Never replace code with placeholders like `# ... rest of the processing ...`. Always include complete code.
- Break problems into smaller steps. Think through each step separately before implementing.
- Always provide a complete PLAN with REASONING based on evidence from code and logs before making changes.
- Explain your OBSERVATIONS clearly, then provide REASONING to identify the exact issue. Add console logs when needed to gather more information.


The Polity digital asset management platform consists of several interconnected business domains:

## Core Infrastructure

### Multi-Party Computation (MPC) Wallet System
Location: `wallet-infrastructure-service/internal/service/wallets/`

Implements dual-provider wallet architecture:
- DFNS integration for institutional-grade custody
- Safeheron for MPC-based custody
- Custom transaction validation flows
- Asset-specific handling for ETH, BTC, USDC
- Provider-specific credential management

### Authentication and Identity
Location: `auth-gateway/src/main/kotlin/com/dcs/authgateway/`

Implements virtual identity system:
- Avatar creation (max 3 per investor)
- Adviser-Avatar relationship management  
- Multi-role authorization (Investor, Adviser, Network Operator)
- FractalID integration for KYC/identity verification

## Business Operations

### Store and Auction System
Location: `store-auction/auction/internal/service/`

Manages digital asset marketplace:
- Real-time auction lifecycle management
- Tier-based product access control
- Multi-party bidding with synchronized price updates
- Auto-extension rules for auction countdown
- Bot bidding simulation for market liquidity

### Agreement Management
Location: `corda5-bastion/workflows/src/main/kotlin/net/corda/c5bastion/flows/`

Handles investment agreements:
- Multi-step verification workflow (PROPOSED -> SIGNED -> VERIFIED -> NOTARIZED)
- Investment threshold validation (≥100 EUR initial, ≥10 EUR subsequent)
- Three-party agreement structure (Adviser-Avatar-Platform)
- Automatic notification dispatch on state changes

### Vault Security
Location: `polity_vault/wallet_credentials/`

Manages credential security:
- Provider-specific credential schemas
- Rotation policy enforcement
- Multi-level access control
- Audit logging for financial compliance
- Hierarchical secret storage structure

The system emphasizes security and compliance while providing institutional-grade digital asset management capabilities through multiple wallet providers and a sophisticated virtual identity system.

$END$
