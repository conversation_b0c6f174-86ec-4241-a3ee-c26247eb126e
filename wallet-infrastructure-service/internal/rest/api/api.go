package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"golang.org/x/exp/slices"

	"github.com/Polity_MVP/wallet-infrastructure-service/container"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models"
	blowfishModels "github.com/Polity_MVP/wallet-infrastructure-service/internal/models/blowfish"
	defiModels "github.com/Polity_MVP/wallet-infrastructure-service/internal/models/defi"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/dfns"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/identity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/notification"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/pagination"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/polity"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/safeheron"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/models/vault"
	wcModels "github.com/Polity_MVP/wallet-infrastructure-service/internal/models/walletconnect"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/rest/api/utils"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service"
	defiProvider "github.com/Polity_MVP/wallet-infrastructure-service/internal/service/defi"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/status"
	wcService "github.com/Polity_MVP/wallet-infrastructure-service/internal/service/walletconnect"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/service/wallets/safeheron/callback"
	"github.com/Polity_MVP/wallet-infrastructure-service/internal/storage"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/authGateway"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/blowfish"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/explorers"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/polityVault"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/storeAuction/store"
	"github.com/Polity_MVP/wallet-infrastructure-service/providers/userInfrastructure"
	"github.com/go-playground/validator/v10"
	"github.com/valyala/fasthttp"
	"gorm.io/gorm"
)

const (
	userIdentity = "user_identity"

	requestIDKey   = "request_uuid"
	assetSymbolKey = "asset_symbol"
	limit          = "limit"
	defaultLimit   = 10
	offset         = "offset"
	defaultOffset  = 0
	balances       = "balances"

	avatarRole   = "AVATAR"
	operatorRole = "OPERATOR"

	storeFeature = "store_enabled"

	TransferConfirmedStatus = "wallet.transfer.confirmed"
	StatusEnabled           = "Enabled"
	StatusDisabled          = "Disabled"
)


// WalletInfrastructureController is responsible for processing request and applying the business logic
type WalletInfrastructureController struct {
	container                container.WalletInfrastructureContainer
	polityVaultClient        polityVault.Client
	userInfrastructureClient userInfrastructure.Client
	authGatewayClient        authGateway.Client
	blowfishService          blowfish.Service
	defiClient               defiProvider.Service
	dfnsWalletService        service.WalletInfrastructureService[vault.DFNSCredentials]
	dfnsCallbackService      service.DFNSCallbackService
	safeheronWalletService   service.WalletInfrastructureService[vault.SafeheronCredentials]
	safeheronCallbackService service.SafeheronCallbackService
	walletRequestsService    service.WalletRequestsService
	favoriteAddressService   service.FavoriteAddressService
	statusHandlerManager     *status.HandlerManager
	callHandlerManager       *wcService.HandlerManager
	moralisService           explorers.ExplorerService
	storeClient              store.Client
}

// NewWalletInfrastructureController constructs a controller
func NewWalletInfrastructureController(
	container container.WalletInfrastructureContainer,
	polityVaultClient polityVault.Client,
	userInfrastructureClient userInfrastructure.Client,
	authGatewayClient authGateway.Client,
	blowfishService blowfish.Service,
	defiClient defiProvider.Service,
	dfnsWalletService service.WalletInfrastructureService[vault.DFNSCredentials],
	dfnsCallbackService service.DFNSCallbackService,
	safeheronWalletService service.WalletInfrastructureService[vault.SafeheronCredentials],
	safeheronCallbackService service.SafeheronCallbackService,
	walletRequestsService service.WalletRequestsService,
	favoriteAddressService service.FavoriteAddressService,
	statusHandlerManager *status.HandlerManager,
	callHandlerManager *wcService.HandlerManager,
	moralisService explorers.ExplorerService,
	storeClient store.Client,
) *WalletInfrastructureController {
	return &WalletInfrastructureController{
		container:                container,
		polityVaultClient:        polityVaultClient,
		userInfrastructureClient: userInfrastructureClient,
		authGatewayClient:        authGatewayClient,
		blowfishService:          blowfishService,
		defiClient:               defiClient,
		dfnsWalletService:        dfnsWalletService,
		dfnsCallbackService:      dfnsCallbackService,
		safeheronWalletService:   safeheronWalletService,
		safeheronCallbackService: safeheronCallbackService,
		walletRequestsService:    walletRequestsService,
		favoriteAddressService:   favoriteAddressService,
		statusHandlerManager:     statusHandlerManager,
		callHandlerManager:       callHandlerManager,
		moralisService:           moralisService,
		storeClient:              storeClient,
	}
}

// MakePaymentDFNS works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Initiate transaction
//	@Description	Initiate transaction from DFNS wallet
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Param			transaction	body	polity.TransactionRequest	true	"Transaction details input"
//	@Router			/payment/dfns [post]
//	@Security		BearerAuth
//	@Success		200				{object}	polity.TransactionResponse
//	@Failure		400,401,404,500	{string}	error
func (w *WalletInfrastructureController) MakePaymentDFNS(ctx *fasthttp.RequestCtx) {
	var tx *polity.TransactionRequest

	err := json.Unmarshal(ctx.PostBody(), &tx)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when try to make payment: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	err = w.container.GetValidator().Validator.Struct(tx)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	verified := w.container.GetValidator().ValidateAssetSymbol(tx.Asset)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetDFNSWalletCredentials(user.Token)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsDFNSErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}

	transactionResult, err := w.dfnsWalletService.MakePayment(user.UserID, tx, credentials)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", paymentErr, walletsErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant make payment: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", paymentErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.container.GetLogger().Debug("Make Payment finished successfully. ")
	payload := notification.TransactionPayload{
		Asset:                    tx.Asset,
		Amount:                   fmt.Sprintf("-%s", tx.Value),
		TransactionID:            transactionResult.TransactionID,
		Status:                   transactionResult.Status,
		Merchant:                 wallets.DFNS,
		SourceAccount:            tx.From,
		DestinationWalletAddress: tx.To,
	}
	err = w.userInfrastructureClient.SendTransactionNotification(user.UserID, payload)
	if err != nil {
		w.container.GetLogger().Errorf("Cant send notification. Error: %v", err)
	}
	w.container.GetLogger().Debug("Notification to user infrastructure sent successfully. ")
	w.container.GetLogger().Debugf("Payload body for notification: %v", payload)

	w.JsonResponse(ctx, transactionResult, fasthttp.StatusOK)
}

// MakePaymentSafeHeron works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Initiate transaction
//	@Description	Initiate transaction from Safeheron wallet
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Param			transaction	body	polity.TransactionRequest	true	"Transaction details input"
//	@Router			/payment/safeheron [post]
//	@Security		BearerAuth
//	@Success		200				{object}	swagger.TransactionResponse
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) MakePaymentSafeHeron(ctx *fasthttp.RequestCtx) {
	var tx *polity.TransactionRequest

	err := json.Unmarshal(ctx.PostBody(), &tx)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when try to make payment: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	err = w.container.GetValidator().Validator.Struct(tx)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	verified := w.container.GetValidator().ValidateAssetSymbol(tx.Asset)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
		user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}

	transactionResult, err := w.safeheronWalletService.MakePayment(user.UserID, tx, credentials)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", paymentErr, walletsErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant make payment: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", paymentErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	payload := notification.TransactionPayload{
		Asset:                    tx.Asset,
		Amount:                   fmt.Sprintf("-%s", tx.Value),
		Merchant:                 wallets.SafeHeron,
		SourceAccount:            tx.From,
		DestinationWalletAddress: tx.To,
	}

	if transactionResult.TransactionID != "" {
		payload.TransactionID = transactionResult.TransactionID
	}
	if transactionResult.Status != "" {
		payload.Status = transactionResult.Status
	}

	err = w.userInfrastructureClient.SendTransactionNotification(user.UserID, payload)
	if err != nil {
		w.container.GetLogger().Errorf("Cant send notification")
	}

	w.JsonResponse(ctx, transactionResult, fasthttp.StatusOK)
}

// CreatePolityWallet works with request parameters and passes to the service layer
//
//	@Summary		Create polity wallet
//	@Description	Save wallet request details in db and passes request to Polity Vault
//	@Tags			wallets
//	@Accept			json
//	@Produce		json
//	@Param			wallet	body	swagger.WalletCreationRequest	true	"Request with details about Wallet to be created"
//	@Router			/wallet [post]
//	@Security		BearerAuth
//	@Success		201
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) CreatePolityWallet(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()
	var wallet *polity.Wallet

	err := json.Unmarshal(ctx.PostBody(), &wallet)
	if err != nil {
		logger.Errorf("Bad request when create wallet request %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	wallet.UserID = user.UserID
	token := user.Token

	if w.container.GetFlagger().GetFeatureFlag(storeFeature) {
		userWallets, err := w.walletRequestsService.GetPolityWalletsByTypeAndUserID(wallet.Type, user.UserID)
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s ", createWalletErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
			return
		}

		switch wallet.Type {
		case wallets.DFNS:
			var userOwnsDFNSWallet bool

			for _, v := range userWallets {
				if v.Type == wallets.DFNS {
					userOwnsDFNSWallet = true
					break
				}
			}

			if !userOwnsDFNSWallet {
				break
			}

			fallthrough
		default:
			permissions, err := w.storeClient.GetPermissionsForUsersTiers(token)
			if err != nil {
				logger.Errorf("Cant get permissions %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Cant get user permissions: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}

			var isWalletTypeExistsInPermissions bool
			var productQuantity int

			if len(permissions) == 0 {
				w.container.GetLogger().Debugf("permissions: %v", permissions)
				w.container.GetLogger().Debugf("permission: %v", permissions[0])
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s: %s", createWalletErr, "user doesnt own tiers"),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
				return
			}

			for _, response := range permissions {
				if response.Category == wallet.Type {
					isWalletTypeExistsInPermissions = true
					productQuantity = response.ProductQuantity
					break
				}
			}

			if !isWalletTypeExistsInPermissions {
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s: user doesnt own tiers to create %s wallet", createWalletErr, wallet.Type),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
				return
			}

			availableWalletsQuantityToCreate := productQuantity - len(userWallets)
			if wallet.Type == wallets.DFNS {
				availableWalletsQuantityToCreate++
			}
			if availableWalletsQuantityToCreate <= 0 {
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s: %s", createWalletErr, "user doesnt own products to create"),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
				return
			}
		}
	}

	err = w.container.GetValidator().Validator.Struct(wallet)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.walletRequestsService.CreatePolityWallet(wallet)
	if err != nil {
		if storage.IsUniqueViolationError(err) {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", createWalletErr, walletUniquenessErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}

		w.container.GetLogger().Errorf("Cant create wallet: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", createWalletErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	err = w.walletRequestsService.CreateWalletRequest(wallet, token)
	if err != nil {
		w.container.GetLogger().Errorf("Internal Error when create wallet request %v", err)

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", walletCreationRequestErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	err = w.authGatewayClient.SendSlackWalletCreatedNotification(token, notification.SlackWalletCreatedNotification{
		RequestID:  wallet.RequestID,
		UserID:     wallet.UserID,
		WalletType: wallet.Type,
	})
	if err != nil {
		w.container.GetLogger().Errorf("cant send notification: %v", err)
	}

	ctx.SetStatusCode(fasthttp.StatusCreated)
}

// GetPolityWallet works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get polity wallet
//	@Description	Get wallet for current user. You can’t access the endpoint if you are not the owner of the wallet (if user id from JWT is not equal to user id of the wallet owner)
//	@Tags			assets
//	@Produce		json
//	@Param			request_uuid	path	string			true	"Request ID of wallet"
//	@Param			wallet			body	swagger.Wallet	true	"Wallet detailed for current user details"
//	@Router			/wallet/{request_uuid} [get]
//	@Security		BearerAuth
//	@Success		200			{object}	swagger.Wallet
//	@Failure		400,401,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetPolityWallet(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()
	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))

	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	wallet, err := w.walletRequestsService.GetPolityWalletForUser(requestID, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", getWalletErr, walletsErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}

		logger.Errorf("Can't get wallet. Error : %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s : %s", getWalletErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	response := &polity.GetPolityWalletResponse{
		RequestID:  wallet.RequestID,
		Type:       wallet.Type,
		Address:    wallet.ReferencingAddress,
		WalletName: wallet.WalletName,
		Status:     wallet.Status,
		CreatedAt:  wallet.CreatedAt,
		ArchivedAt: wallet.ArchivedAt,
	}

	if err = json.NewEncoder(ctx).Encode(response); err != nil {
		logger.Errorf("Encoding response error")

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// ArchivePolityWallet works with request parameters and passes to the service layer
//
//	@Summary		Archives polity wallet
//	@Description	Set the wallet as archived
//	@Tags			wallets
//	@Accept			json
//	@Produce		json
//	@Param			wallet	body	polity.RequestID	true	"Request with id of polity wallet to be archived"
//	@Router			/wallet [delete]
//	@Security		BearerAuth
//	@Success		204
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ArchivePolityWallet(ctx *fasthttp.RequestCtx) {
	var walletID *polity.RequestID

	err := json.Unmarshal(ctx.PostBody(), &walletID)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when archive wallet %v", err)

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(walletID)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf(fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error())),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	err = w.walletRequestsService.ArchivePolityWallet(walletID.RequestID, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", getWalletErr, walletsErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}

		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusNoContent)
}

// RenamePolityWallet works with request parameters and passes to the service layer
//
//	@Summary		Rename wallet
//	@Description	Set the another wallet_name for wallet
//	@Tags			wallets
//	@Accept			json
//	@Produce		json
//	@Param			wallet	body	polity.RenameWalletRequest	true	"Request with wallet id and new name"
//	@Router			/wallet [patch]
//	@Security		BearerAuth
//	@Success		204
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) RenamePolityWallet(ctx *fasthttp.RequestCtx) {
	var request polity.RenameWalletRequest

	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when rename wallet %v", err)

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(request)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.walletRequestsService.UpdatePolityWalletName(request)
	if err != nil {
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusNoContent)
}

// ProcessPolityWalletStatusChange works with request parameters and passes to the service layer
//
//	@Summary		Finish wallet creation
//	@Description	Process polity wallet status change and create appropriate wallets with default assets on dfns/safeheron. Request field data depends on wallet type (dfns/safeheron)
//	@Tags			wallets
//	@Accept			json
//	@Produce		json
//	@Param			status	body	swagger.WalletCreationStatusUpdate	true	"Request status for specified request and appropriate credentials"
//	@Router			/wallet/webhook [post]
//	@Success		200
//	@Failure		401,500	{string}	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ProcessPolityWalletStatusChange(ctx *fasthttp.RequestCtx) {
	var statusUpdate vault.WalletCreationStatusUpdate
	logger := w.container.GetLogger()

	err := json.Unmarshal(ctx.PostBody(), &statusUpdate)
	if err != nil {
		logger.Errorf("Bad request when create polity wallet %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(statusUpdate)
	if err != nil {
		logger.Errorf("Not a valid body. Error: '%v'\n", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	_, err = statusUpdate.Status.Validate()
	if err != nil {
		logger.Errorf("Invalid Status Error: '%v'\n", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	statusHandler, err := w.statusHandlerManager.GetStatusHandler(statusUpdate.Status)
	if err != nil {
		logger.Errorf("Invalid status. Current status Error: '%v'\n", err)
		err = w.statusHandlerManager.HandleError(statusUpdate.RequestID, err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	err = statusHandler.Process(statusUpdate.RequestID, statusUpdate.Data)
	if err != nil {
		if err.Error() == "unauthorized" { // unauthorized via DFNS, because we have invalid jwt_token
			resp := models.ApiResponse{
				Message: "Invalid 'jwt_token' value",
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
		if err == gorm.ErrRecordNotFound {
			logger.Errorf("Record doesn't exist by request_uuid '%v'. Error: '%v'", statusUpdate.RequestID, err)
			resp := models.ApiResponse{
				Message: fmt.Sprintf(walletNotFoundErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		if strings.Contains(err.Error(), "json: cannot unmarshal") {
			logger.Errorf("Unmarshal Error: '%v'\n", err)
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
		if strings.EqualFold(err.Error(), status.ErrEmptyTokenForDfnsClient.Error()) {
			logger.Errorf("No token. Error: '%v' ", err)
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}

		_, ok := err.(validator.ValidationErrors)
		if ok {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		} else if strings.Contains(err.Error(), "validation error") {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
		logger.Errorf("Process failed. Error: '%v'\n", err)
		err = w.statusHandlerManager.HandleError(statusUpdate.RequestID, err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// TODO: This error handler mixes business logic with http error logic, it is not correct
func (w *WalletInfrastructureController) handleError(ctx *fasthttp.RequestCtx, requestID string, err error) {
	updateErr := w.walletRequestsService.UpdatePolityWalletStatus(requestID, vault.Failed)
	if updateErr != nil {
		w.container.GetLogger().Errorf(fmt.Sprintf("Unable to set Failed status: %v. Previous error: %v", updateErr, err))
		ctx.SetBody([]byte(err.Error()))
		return
	}
}

// CreateNewWallet works with request parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Create Wallet
//	@Description	Create new wallet for users wallet on dfns
//	@Tags			wallets
//	@Accept			json
//	@Produce		json
//	@Param			asset	body	polity.AddAssetRequest	true	"Request with details to create dfns wallet"
//	@Router			/wallet/dfns/asset [post]
//	@Security		BearerAuth
//	@Success		201
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) CreateNewWallet(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()
	var req polity.AddAssetRequest

	err := json.Unmarshal(ctx.PostBody(), &req)
	if err != nil {
		logger.Errorf("Bad request when create polity wallet %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(req)
	if err != nil {
		logger.Errorf("Validator failed. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	wallet, err := w.walletRequestsService.GetPolityWalletForUser(req.RequestID, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, walletsErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		logger.Errorf("Cant get polity wallet. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	dfnsCredentials, code, err := w.polityVaultClient.GetDFNSWalletCredentials(user.Token)
	if err != nil {
		logger.Errorf("Canc get dfns Credentials. Error: '%v'\n", err)
		// TODO: Take a look at error handling in HandlerManager, handling of business logic errors should be at business
		// TODO: logic level
		w.handleError(ctx, req.RequestID, err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsDFNSErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}
	assets, err := w.dfnsWalletService.GetAssets(req.RequestID, user.UserID, dfnsCredentials)
	if err != nil {
		logger.Errorf("Cant get assets for userID: '%v'. Error: '%v' ", user.UserID, err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}
	for _, asset := range assets.Assets {
		if req.AssetSymbol == asset.AssetSymbol {
			logger.Debugf("Current asset with such assetSymbol: '%v' exists. ", req.AssetSymbol)
			resp := models.ApiResponse{
				Message: assetUniquenessErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
	}

	if err := w.dfnsWalletService.AddAsset(wallet, req.AssetSymbol, dfnsCredentials); err != nil {
		logger.Errorf("Cant create new asset account. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	err = w.authGatewayClient.SendWalletStatisticNotification(notification.WalletStatisticsNotification{
		WalletType: wallets.DFNS,
		Count:      1,
	})
	if err != nil {
		logger.Errorf("cant send notification: %v", err)
	}

	ctx.SetStatusCode(fasthttp.StatusCreated)
}

// ResolveApprovalTask works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Resolve approval task
//	@Description	Resolve safeheron task to approve transaction
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Param			request_uuid	path	string								true	"Wallet Request ID of task to resolve"
//	@Param			task			body	safeheron.EncryptedCallbackRequest	true	"Encrypted details about task to be resolved"
//	@Router			/payment/safeheron/resolve/{request_uuid} [post]
//	@Security		BearerAuth
//	@Success		200		{object}	safeheron.EncryptedCallbackResponse
//	@Failure		401,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ResolveApprovalTask(ctx *fasthttp.RequestCtx) {
	var request *safeheron.EncryptedCallbackRequest

	if err := json.Unmarshal(ctx.PostBody(), &request); err != nil {
		w.container.GetLogger().Errorf("Cant unmarshall callback request: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
		"", w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions().UseCache())
	if err != nil {
		w.container.GetLogger().Errorf("Cant get credentials: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}

	result, err := w.safeheronCallbackService.ResolveApprovalTask(request, credentials)
	if err != nil {
		w.container.GetLogger().Errorf("Cant resolve approval task: %v", err.Error())
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	response, err := json.Marshal(result)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall callback response: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.container.GetLogger().Debug("Approval task is resolved")
	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// GetSafeheronAssets works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get assets list
//	@Description	Get list of assets on specified safeheron wallet
//	@Tags			assets
//	@Produce		json
//	@Param			request_uuid	path	string	true	"Request ID of wallet"
//	@Router			/wallet/safeheron/{request_uuid}/assets [get]
//	@Security		BearerAuth
//	@Success		200				{object}	polity.GetAssetsResponse
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetSafeheronAssets(ctx *fasthttp.RequestCtx) {
	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))

	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
		user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}

	assets, err := w.safeheronWalletService.GetAssets(requestID, user.UserID, credentials)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: assetNotFoundErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant get get assets: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.container.GetLogger().Debugf("Assets %v", assets)

	response, err := json.Marshal(assets)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall assets: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// GetSafeheronAssetAddresses works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get assets list
//	@Description	Get list of assets on specified safeheron wallet. Method returns all assets for each asset in wallet.
//	@Tags			assets
//	@Produce		json
//	@Param			request_uuid	path	string	true	"Request ID of wallet"
//	@Param			asset_symbol	path	string	true	"Asset for which address user is looking for"
//	@Router			/wallet/safeheron/{request_uuid}/{asset_symbol}/address [get]
//	@Security		BearerAuth
//	@Success		200				{object}	[]polity.AssetAddress
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetSafeheronAssetAddresses(ctx *fasthttp.RequestCtx) {
	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))
	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	asset := fmt.Sprintf("%v", ctx.UserValue(assetSymbolKey))
	verified = w.container.GetValidator().ValidateAssetSymbol(asset)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
		user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}

	addresses, err := w.safeheronWalletService.GetAssetAddresses(requestID, user.UserID, asset, credentials)
	if err != nil {
		if err.Error() == "asset doesn't exist" || err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: addressesNotFoundErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}

		w.container.GetLogger().Errorf("Cant get get asset address: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if len(addresses) == 0 {
		errNoAssets := fmt.Errorf("We have no asset addresses on Safeheron for current asset_symbol: '%v' ", asset)

		w.container.GetLogger().Error(errNoAssets)
		resp := models.ApiResponse{
			Message: errNoAssets.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
		return
	}

	response, err := json.Marshal(addresses)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall asset address: %v", err)

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// GetDFNSAssetAddresses works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get addresses of DFNS asset account
//	@Description	Get addresses of asset, which specified on dfns wallet. Method returns all assets for each asset in wallet
//	@Tags			assets
//	@Param			request_uuid	path	string	true	"Request ID of wallet"
//	@Param			asset_symbol	path	string	true	"Asset for which address user is looking for"
//	@Security		BearerAuth
//	@Produce		json
//	@Router			/wallet/dfns/{request_uuid}/{asset_symbol}/address [get]
//	@Security		BearerAuth
//	@Success		200			{object}	[]polity.AssetAddress
//	@Failure		400,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetDFNSAssetAddresses(ctx *fasthttp.RequestCtx) {
	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))
	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	asset := fmt.Sprintf("%v", ctx.UserValue(assetSymbolKey))
	verified = w.container.GetValidator().ValidateAssetSymbol(asset)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetDFNSWalletCredentials(user.Token)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsDFNSErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}

	addresses, err := w.dfnsWalletService.GetAssetAddresses(requestID, user.UserID, asset, credentials)
	if err != nil {
		if err.Error() == "asset doesn't exist" || err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: addressesNotFoundErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}

		w.container.GetLogger().Errorf("Cant get get asset address: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if len(addresses) == 0 {
		errNoAssets := fmt.Errorf("We have no asset addresses on DFNS for current asset_symbol: '%v' ", asset)

		w.container.GetLogger().Error(errNoAssets)
		resp := models.ApiResponse{
			Message: errNoAssets.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
		return
	}

	response, err := json.Marshal(addresses)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall asset address: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// GetDFNSAssets works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get assets list
//	@Description	Get list of asset accounts on dfns for specified wallet
//	@Tags			assets
//	@Produce		json
//	@Param			request_uuid	path	string	true	"Request ID of wallet"
//	@Router			/wallet/dfns/{request_uuid}/assets [get]
//	@Security		BearerAuth
//	@Success		200				{object}	polity.GetAssetsResponse
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetDFNSAssets(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()

	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))

	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	logger.Debugf("RequestID: '%s'\n", requestID)

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetDFNSWalletCredentials(user.Token)
	if err != nil {
		logger.Errorf("Cant get dfns credsntials. Error: '%v'\n", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsDFNSErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}
	assets, err := w.dfnsWalletService.GetAssets(requestID, user.UserID, credentials)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: assetNotFoundErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		logger.Errorf("Cant ger active assets. Error: '%v'\n", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if err = json.NewEncoder(ctx).Encode(assets); err != nil {
		logger.Errorf("Encoding response error")
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// GetDFNSActiveAssetsList works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get list of active DFNS assets for user
//	@Description	Get list of active DFNS assets for specified wallet in our system
//	@Tags			assets
//	@Produce		json
//	@Router			/wallet/dfns/list/active/assets [get]
//	@Security		BearerAuth
//	@Success		200				{array}		[]string
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetDFNSActiveAssetsList(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()

	listOfActiveDFNSNetworks := wallets.DFNSDefaultNetworks.DefaultNetworksList
	if len(listOfActiveDFNSNetworks) < 1 {
		logger.Error("No default list of networks for DFNS. ")

		resp := models.ApiResponse{
			Message: "No default list of networks for DFNS",
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
		return
	}

	var dfnsActiveAssets = make([]string, len(listOfActiveDFNSNetworks))
	for i, network := range listOfActiveDFNSNetworks {
		assetSymbol, ok := wallets.DFNSNetworkAssetsymbolMap[network]
		if !ok {
			err := fmt.Errorf("network: %v is not in DFNSNetworkAssetsymbolMap", network)
			logger.Error(err)
			resp := models.ApiResponse{
				Message: err.Error(),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		dfnsActiveAssets[i] = assetSymbol
	}

	response, err := json.Marshal(dfnsActiveAssets)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshal dfnsActiveAssets: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// GetSafeheronActiveAssetsList works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get list of active Safeheron assets for user
//	@Description	Get list of active Safeheron assets accounts for specified wallet in our system
//	@Tags			assets
//	@Produce		json
//	@Router			/wallet/safeheron/list/active/assets [get]
//	@Security		BearerAuth
//	@Success		200				{array}		[]string
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetSafeheronActiveAssetsList(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()

	safeheronActiveAssets := append(wallets.SafeheronAssetsLists.DefaultCoinsList, wallets.SafeheronAssetsLists.Web3List...)

	if len(safeheronActiveAssets) < 1 {
		logger.Error("No default list of Active Assets for Safeheron. ")

		resp := models.ApiResponse{
			Message: "No default list of Active Assets for Safeheron",
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
		return
	}

	response, err := json.Marshal(safeheronActiveAssets)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall safeheronActiveAssets: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// GetDFNSListOfWallets works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get list of active DFNS wallets.
//	@Description	Get list of active DFNS wallets for user. Specify optional parameter like /dfns?balances=true
//	@Tags			wallets
//	@Produce		json
//	@Router			/wallets/dfns/ [get]
//	@Security		BearerAuth
//	@Success		200			{object}	[]polity.WalletData
//	@Failure		401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetDFNSListOfWallets(ctx *fasthttp.RequestCtx) {
	var balancesFlag bool
	var err error

	logger := w.container.GetLogger()
	user := ctx.UserValue(userIdentity).(identity.Identity)

	formattedBalances := string(ctx.URI().QueryArgs().Peek(balances))
	if formattedBalances != "" {
		balancesFlag, err = strconv.ParseBool(formattedBalances)
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, balancesFlagErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
	}

	usersWallets, err := w.walletRequestsService.GetPolityWalletsByTypeAndUserID(wallets.DFNS, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: walletsErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		logger.Errorf("Cant get DFNS active wallets for userID: '%v'. Error: '%v'\n", user.UserID, err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	var resultWallets = make([]polity.WalletData, 0)

	for _, wallet := range usersWallets {
		resultWallets = append(resultWallets, polity.WalletData{
			RequestID:  wallet.RequestID,
			WalletName: wallet.WalletName,
			Status:     string(wallet.Status),
			CreatedAt:  wallet.CreatedAt,
		})
	}

	if balancesFlag {
		for k, wallet := range resultWallets {
			if !w.statusHandlerManager.IsActiveWalletStatus(vault.Status(wallet.Status)) {
				continue
			}
			credentials, code, err := w.polityVaultClient.GetDFNSWalletCredentials(user.Token)
			if err != nil {
				logger.Errorf("Cant get dfns credsntials. Error: '%v'\n", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s: %s", getCredentialsDFNSErr, err.Error()),
				}
				w.JsonResponse(ctx, resp, code)
				return
			}
			assets, err := w.dfnsWalletService.GetAssets(wallet.RequestID, user.UserID, credentials)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					resp := models.ApiResponse{
						Message: assetNotFoundErr,
					}
					w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
					return
				}
				logger.Errorf("Cant ger active assets. Error: '%v'\n", err)
				resp := models.ApiResponse{
					Message: err.Error(),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}

			resultWallets[k].BalanceUSD = assets.BalanceUSD
		}
	}

	response, err := json.Marshal(resultWallets)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall resultWallets: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// GetSafeheronListOfWallets works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Get list of active Safeheron wallets.
//	@Description	Get list of active Safeheron wallets for user. Specify optional parameter like /safeheron?balances=true
//	@Tags			wallets
//	@Produce		json
//	@Router			/wallets/safeheron/ [get]
//	@Security		BearerAuth
//	@Success		200			{object}	[]polity.WalletData
//	@Failure		401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetSafeheronListOfWallets(ctx *fasthttp.RequestCtx) {
	var balancesFlag bool
	var err error

	logger := w.container.GetLogger()
	user := ctx.UserValue(userIdentity).(identity.Identity)

	formattedBalances := string(ctx.URI().QueryArgs().Peek(balances))
	if formattedBalances != "" {
		balancesFlag, err = strconv.ParseBool(formattedBalances)
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, balancesFlagErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
	}

	usersWallets, err := w.walletRequestsService.GetPolityWalletsByTypeAndUserID(wallets.SafeHeron, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: walletsErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		logger.Errorf("Cant get Safeheron active wallets for userID: '%v'. Error: '%v'\n", user.UserID, err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	var resultWallets = make([]polity.WalletData, 0)

	for _, wallet := range usersWallets {
		resultWallets = append(resultWallets, polity.WalletData{
			RequestID:  wallet.RequestID,
			WalletName: wallet.WalletName,
			Status:     string(wallet.Status),
			CreatedAt:  wallet.CreatedAt,
		})
	}

	if balancesFlag {
		for k, wallet := range resultWallets {
			if !w.statusHandlerManager.IsActiveWalletStatus(vault.Status(wallet.Status)) {
				continue
			}
			credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
				user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
			if err != nil {
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
				}
				w.JsonResponse(ctx, resp, code)
				return
			}
			assets, err := w.safeheronWalletService.GetAssets(wallet.RequestID, user.UserID, credentials)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					resp := models.ApiResponse{
						Message: assetNotFoundErr,
					}
					w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
					return
				}
				w.container.GetLogger().Errorf("Cant get get assets: '%v' ", err)
				resp := models.ApiResponse{
					Message: err.Error(),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}

			resultWallets[k].BalanceUSD = assets.BalanceUSD
		}
	}

	response, err := json.Marshal(resultWallets)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall resultWallets: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// AddFavouriteAddress works with request parameters and passes to the service layer
//
//	@Summary		Add address to list of favourites
//	@Description	Add wallet address to the users' favourites list
//	@Tags			favourites
//	@Accept			json
//	@Produce		json
//	@Param			address	body	swagger.FavouriteAddressEntry	true	"Request with details about address to add"
//	@Router			/favourites/address [post]
//	@Security		BearerAuth
//	@Success		201
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) AddFavouriteAddress(ctx *fasthttp.RequestCtx) {
	var favouriteAddress *polity.FavouriteAddressEntry

	err := json.Unmarshal(ctx.PostBody(), &favouriteAddress)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when pinn adress %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	favouriteAddress.UserID = ctx.UserValue(userIdentity).(identity.Identity).UserID

	err = w.container.GetValidator().Validator.Struct(favouriteAddress)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	verified := w.container.GetValidator().ValidateWalletName(favouriteAddress.Name)
	if !verified {
		resp := models.ApiResponse{
			Message: walletNameErr,
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.favoriteAddressService.AddAddress(favouriteAddress)
	if err != nil {
		if err.Error() == addressUniquenessErr {
			resp := models.ApiResponse{
				Message: addressUniquenessErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}

		w.container.GetLogger().Errorf("Cant pin address: %v", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusCreated)
}

// GetFavouriteAddresses works with request parameters and passes to the service layer
//
//	@Summary		Get addresses from list of favourites
//	@Description	GetFavouriteAddresses returns static addresses and dynamic addresses for current user.
//	@Description	If we have role==Avatar -> return static and dynamic addresses. If jwt role!=Avatar -> return only dynamic addresses.
//	@Tags			favourites
//	@Produce		json
//	@Param			request_uuid	path	string	true	"Request ID of wallet"
//	@Router			/favourites/address [get]
//	@Security		BearerAuth
//	@Success		200	{object}	polity.GetAssetAddressesResponse
//	@Failure		500	{string}	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetFavouriteAddresses(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()
	role := ctx.UserValue(userIdentity).(identity.Identity).Role
	userID := ctx.UserValue(userIdentity).(identity.Identity).UserID

	var resultAddresses polity.GetAssetAddressesResponse
	data := w.container.GetConfig().StaticAddresses
	logger.Debugf("data: '%v'\n", data)

	// STEP_1. Get dynamic addresses.
	addresses, err := w.favoriteAddressService.GetAddressesByUserID(userID)
	if err != nil {
		logger.Errorf("Cant get addresses: %v", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}
	var dynamicAddresses []polity.FavouriteAddress
	for _, address := range addresses {
		dynamicAddresses = append(dynamicAddresses, polity.FavouriteAddress{
			AssetSymbol: address.AssetSymbol,
			Address:     address.Address,
			Name:        address.Name,
		})
	}
	resultAddresses.DynamicAddresses = dynamicAddresses

	// if we have role==Avatar -> return static and dynamic addresses.
	// if  jwt role!=Avatar -> return only dynamic addresses
	if role == avatarRole {
		var staticAddresses []polity.FavouriteAddress
		for i := 0; i < len(data)-1; i++ {
			if i <= len(data)-3 {
				staticAddresses = append(staticAddresses, polity.FavouriteAddress{
					AssetSymbol: data[i],
					Address:     data[i+1],
					Name:        data[i+2],
				})
				i = i + 2
			}
		}
		logger.Debugf("staticAddresses: '%v'\n", staticAddresses)

		resultAddresses.StaticAddresses = staticAddresses
	}

	response, err := json.Marshal(resultAddresses)
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall resultAddresses: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// RemoveFavouriteAddress works with request parameters and passes to the service layer
//
//	@Summary		Remove address from list of favourites
//	@Description	Remove the wallet address from the preconfigured favourite wallets list
//	@Tags			favourites
//	@Accept			json
//	@Produce		json
//	@Param			address	body	swagger.FavouriteAddressEntryToDelete	true	"Request with details about address to delete"
//	@Router			/favourites/address [delete]
//	@Security		BearerAuth
//	@Success		204
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) RemoveFavouriteAddress(ctx *fasthttp.RequestCtx) {
	var favouriteAddress *polity.FavouriteAddressEntry

	err := json.Unmarshal(ctx.PostBody(), &favouriteAddress)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	favouriteAddress.UserID = ctx.UserValue(userIdentity).(identity.Identity).UserID

	err = w.container.GetValidator().Validator.Struct(favouriteAddress)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.favoriteAddressService.RemoveAddress(favouriteAddress)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: addressNotFoundErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}

		w.container.GetLogger().Errorf("Cant remove address: %v", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusNoContent)
}

// AddSafeheronAsset works with request parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Create Asset
//	@Description	Set new asset for users wallet on safeheron
//	@Tags			assets
//	@Accept			json
//	@Produce		json
//	@Param			asset	body	polity.AddAssetRequest	true	"Request with details to create asset"
//	@Router			/wallet/safeheron/asset [post]
//	@Security		BearerAuth
//	@Success		201
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) AddSafeheronAsset(ctx *fasthttp.RequestCtx) {
	var request polity.AddAssetRequest
	logger := w.container.GetLogger()

	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when create polity wallet %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(request)
	if err != nil {
		w.container.GetLogger().Errorf("Validator failed. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
		user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}

	wallet, err := w.walletRequestsService.GetPolityWalletForUser(request.RequestID, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: walletsErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant get polity wallet. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	assets, err := w.safeheronWalletService.GetAssets(request.RequestID, user.UserID, credentials)
	if err != nil {
		logger.Errorf("Cant get assets for userID: '%v'. Error: '%v' ", user.UserID, err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}
	for _, asset := range assets.Assets {
		if request.AssetSymbol == asset.AssetSymbol {
			logger.Debugf("Current asset with such assetSymbol: '%v' exists. ", request.AssetSymbol)
			resp := models.ApiResponse{
				Message: assetUniquenessErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
	}

	if err = w.safeheronWalletService.AddAsset(wallet, request.AssetSymbol, credentials); err != nil {
		w.container.GetLogger().Errorf("Cant create new asset account. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusCreated)
}

// ArchiveSafeheronAsset works with request parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Archives Asset
//	@Description	Archives asset for users safeheron wallet
//	@Tags			assets
//	@Accept			json
//	@Produce		json
//	@Param			asset	body	polity.AddAssetRequest	true	"Request with details to archive asset"
//	@Router			/wallet/safeheron/asset [delete]
//	@Security		BearerAuth
//	@Success		204
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ArchiveSafeheronAsset(ctx *fasthttp.RequestCtx) {
	var request polity.AddAssetRequest

	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when archive asset %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(request)
	if err != nil {
		w.container.GetLogger().Errorf("Validator failed. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	if err = w.safeheronWalletService.ArchiveAsset(request.RequestID, user.UserID, request.AssetSymbol); err != nil {
		if err == gorm.ErrRecordNotFound {
			w.container.GetLogger().Errorf("Asset achived. Error: '%v'", err)
			resp := models.ApiResponse{
				Message: assetNotFoundErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant create new asset account. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusNoContent)
}

// ArchiveDFNSAsset works with request parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Archives Asset
//	@Description	Archives asset for users dfns wallet
//	@Tags			assets
//	@Accept			json
//	@Produce		json
//	@Param			asset	body	polity.AddAssetRequest	true	"Request with details to archive asset"
//	@Router			/wallet/dfns/asset [delete]
//	@Security		BearerAuth
//	@Success		204
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ArchiveDFNSAsset(ctx *fasthttp.RequestCtx) {
	var request polity.AddAssetRequest

	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when archive asset %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(request)
	if err != nil {
		w.container.GetLogger().Errorf("Validator failed. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %v", badRequestBodyErr, err),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)

	if err = w.dfnsWalletService.ArchiveAsset(request.RequestID, user.UserID, request.AssetSymbol); err != nil {
		if err == gorm.ErrRecordNotFound {
			w.container.GetLogger().Errorf("Asset achived. Error: '%v'", err)
			resp := models.ApiResponse{
				Message: assetNotFoundErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant create new asset account. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusNoContent)
}

// ProcessWalletConnectCall works with request and response parameters and passes to the service layer
//
//	@Summary		Invoke appropriate APIs
//	@Description	Invoke appropriate APIs of wallet managers (safeheron/dfns) for Wallet Connect call
//	@Tags			wallet connect
//	@Accept			json
//	@Produce		json
//	@Param			asset_symbol	path	string							true	"Asset Symbol of requested operation"
//	@Param			transaction		body	swagger.WalletConnectRequest	true	"Request details input"
//	@Router			/assets/{asset_symbol}/invoke [post]
//	@Security		BearerAuth
//	@Success		200			{object}	swagger.TransactionResponse
//	@Failure		400,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ProcessWalletConnectCall(ctx *fasthttp.RequestCtx) {
	var request *wcModels.Request

	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when try to make payment: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(request)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	assetSymbol := fmt.Sprintf("%v", ctx.UserValue(assetSymbolKey))
	verified := w.container.GetValidator().ValidateAssetSymbol(assetSymbol)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	callHandler, err := w.callHandlerManager.GetMethodHandler(assetSymbol, request.Method)
	if err != nil {
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)

	response, err := callHandler.Process(user, request, assetSymbol)
	if err != nil {
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if err = json.NewEncoder(ctx).Encode(response); err != nil {
		w.container.GetLogger().Errorf("Encoding response error")
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// ProcessSafeheronNotification works with request and response parameters, interacts with User Infrastructure
//
//	@Summary		Notify when user receives funds
//	@Description	Receives event notification from Safeheron and sends notification to User Infrastructure that funds have been received
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Param			request_uuid	path	string								true	"Wallet Request ID of notification to process"
//	@Param			notification	body	safeheron.EncryptedCallbackRequest	true	"Encrypted notification"
//	@Router			/payment/safeheron/notification/{request_uuid} [post]
//	@Success		200		{object}	safeheron.EncryptedCallbackResponse
//	@Failure		400,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ProcessSafeheronNotification(ctx *fasthttp.RequestCtx) {
	var request *safeheron.EncryptedCallbackRequest

	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))
	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	w.container.GetLogger().Debugf("YOU ARE IN NOTIFICATIONS, request_uuid:%s", requestID)

	credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
		"", w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
	if err != nil {
		w.container.GetLogger().Errorf("Cant get credentials: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}

	err = json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		w.container.GetLogger().Errorf("Cant unmarshall notification callback request: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("Cant unmarshall notification callback request: %v", err),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	payload, err := w.safeheronCallbackService.ProcessNotification(request, credentials)
	if err != nil {
		w.container.GetLogger().Errorf("Cant process notification: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("Can't process notification: %s", err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if payload != nil && payload.DestinationAccount != "" {
		destinationWallet, err := w.safeheronWalletService.GetPolityWallet(payload.DestinationAccount)
		if err != nil {
			w.container.GetLogger().Errorf("Cant get polity destinationWallet: %v", err)
			return
		}
		payload.DestinationAccount = destinationWallet.RequestID

		if destinationWallet.RequestID == requestID {
			if payload.SourceAccount != "" {
				sourceWallet, err := w.safeheronWalletService.GetPolityWallet(payload.SourceAccount)
				if err == nil {
					payload.SourceAccount = sourceWallet.RequestID
				} else {
					w.container.GetLogger().Errorf("Cant get polity sourceWallet: %v", err)
				}
			}

			w.container.GetLogger().Debugf("HERE FOR ACCOUNT: %s \nWE GOT WALLET: %v \nAND APPROPRIATE USER ID: %s", payload.DestinationAccount, destinationWallet, destinationWallet.UserID)
			w.container.GetLogger().Debugf("sending notification: %v", payload)

			err = w.userInfrastructureClient.SendTransactionNotification(destinationWallet.UserID, *payload)
			if err != nil {
				w.container.GetLogger().Errorf("Cant send notification. Error: %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Cant send notification: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
				return
			}

			w.container.GetLogger().Debug("SUCCESSFULLY SENT NOTIFICATION")
		}
	}

	response, err := json.Marshal(safeheron.EncryptedCallbackResponse{
		Code:    http.StatusOK,
		Message: callback.SuccessfulMessage,
	})
	if err != nil {
		w.container.GetLogger().Errorf("Cant marshall notification callback response: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

// ProcessDFNSNotification works with request and response parameters, interacts with User Infrastructure
//
//	@Summary		Notify when user receives funds
//	@Description	Receives event notification from DFNS and sends notification to User Infrastructure that funds have been received
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Param			notification	body	swagger.GetPaymentByIDResp	true	"Encrypted notification"
//	@Router			/payment/dfns/notification [post]
//	@Success		200		{object}	safeheron.EncryptedCallbackResponse
//	@Failure		400,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ProcessDFNSNotification(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()
	var request *dfns.GetPaymentByIDResp

	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		logger.Errorf("Cant unmarshall notification callback request: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("Can't unmarshall notification callback request: %s", err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	logger.Debugf("Webhook message successfully recieved. Body: '%v'\n", *request)

	payload, err := w.dfnsCallbackService.ProcessNotification(request)
	if err != nil {
		logger.Errorf("Cant process notification: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("Cant process notification: %s", err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if payload != nil {
		var destinationWallet *polity.Wallet
		logger.Debugf("Get wallet for payload.DestinationAccount: '%v'", payload.DestinationAccount)
		if payload.DestinationAccount != "" {
			destinationWallet, err = w.dfnsWalletService.GetPolityWallet(payload.DestinationAccount)
			if err == nil {
				payload.DestinationAccount = destinationWallet.RequestID
			} else {
				logger.Debugf("Cant get polity destinationWallet: %v", err)
			}
		}

		if payload.SourceAccount != "" {
			logger.Debugf("Get wallet for payload.SourceAccount: '%v'", payload.SourceAccount)
			sourceWallet, err := w.dfnsWalletService.GetPolityWallet(payload.SourceAccount)
			if err == nil {
				payload.SourceAccount = sourceWallet.RequestID
			} else {
				logger.Errorf("Cant get polity sourceWallet: %v", err)
			}
		}

		err = w.userInfrastructureClient.SendTransactionNotification(destinationWallet.UserID, *payload)
		if err != nil {
			logger.Errorf("Cant send notification. Error: %v", err)
			resp := models.ApiResponse{
				Message: fmt.Sprintf("Can't send notification: %s", err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
	}
	response, err := json.Marshal(safeheron.EncryptedCallbackResponse{
		Code:    http.StatusOK,
		Message: callback.SuccessfulMessage,
	})
	if err != nil {
		logger.Errorf("Can't marshall notification callback response: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("Can't marshall notification callback response: %s", err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, response, fasthttp.StatusOK)
}

func (w *WalletInfrastructureController) SubscribeToDFNSWebhookNotifications() error {
	logger := w.container.GetLogger()
	webhookUrl := w.container.GetConfig().DfnsWebhookUrl
	dfnsCredentials := &vault.DFNSCredentials{
		JwtToken:   w.container.GetConfig().DfnsToken,
		PrivateKey: polityVault.FormatPrivateKey(w.container.GetConfig().DfnsPrivateKey),
	}

	listWebhooks, err := w.dfnsCallbackService.GetListWebhooks(dfnsCredentials)
	if err != nil {
		w.container.GetLogger().Errorf("Cant get list list of dfns webhooks. Error: %v", err)
		return err
	}
	for _, webhook := range listWebhooks.Webhooks {
		if webhook.Url == webhookUrl && webhook.Status == StatusEnabled && slices.Contains(webhook.Events, TransferConfirmedStatus) {
			logger.Debugf("We subscribed this url earlier on DFNS webhook connection. ")
			return nil
		}
	}

	logger.Debugf("We didnt subscribe this url, so lets do it now.")
	request := &dfns.CreateWebhookRequest{
		Events: []string{TransferConfirmedStatus},
		Url:    webhookUrl,
	}

	response, err := w.dfnsCallbackService.CreateWebhook(request, dfnsCredentials)
	if err != nil {
		w.container.GetLogger().Errorf("Cant create subsctibtion for DFNS callback. Error: %v", err)
		return err
	}
	if slices.Contains(response.Events, TransferConfirmedStatus) && response.Status == StatusEnabled {
		logger.Debugf("We have already subscribed our service on webhook connection successfully. ")
		return err
	}

	logger.Debugf("CallbackSubscription created successfully. Response: '%v'", response)

	return nil
}

// GetDeFiProducts works with request and response parameters and passes to the service layer
//
//	@Summary		Get list of DeFi products, stored on the backend
//	@Description	Display list of the whitelisted products for DeFi.
//	@Tags			defi
//	@Produce		json
//	@Router			/defi/products/ [get]
//	@Security		BearerAuth
//	@Success		200			{object}	swagger.GetDeFiProductResponse
//	@Failure		400,404,500	{object}	swagger.GetDeFiProductResponse
func (w *WalletInfrastructureController) GetDeFiProducts(ctx *fasthttp.RequestCtx) {
	logger := w.container.GetLogger()
	products, err := w.walletRequestsService.GetDefiProducts()
	if err != nil {
		logger.Errorf("Can't get products. Error : %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("Can't get products: %s", err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	var response defiModels.GetDeFiProductResponse
	if len(products) == 0 {
		response.DefiProducts = make([]defiModels.DefiProduct, 0)
	} else {
		response.DefiProducts = products
	}

	if err = json.NewEncoder(ctx).Encode(&response); err != nil {
		logger.Errorf("Encoding response error")

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// GetDefiAssetsBalances works with request and response parameters and passes to the service layer
//
//	@Summary		Get assets balance
//	@Description	Get assets balance for users wallet
//	@Tags			defi
//	@Produce		json
//	@Router			/defi/{request_uuid}/assets/ [get]
//	@Param			request_uuid	path	string	true	"Wallet Request ID of notification to process"
//	@Security		BearerAuth
//	@Success		200			{object}	[]swagger.AssetBalance
//	@Failure		400,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetDefiAssetsBalances(ctx *fasthttp.RequestCtx) {
	assetsBalances := []defiModels.AssetBalance{}

	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))
	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	wallet, err := w.walletRequestsService.GetPolityWalletForUser(requestID, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: walletsErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant get polity wallet. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	switch wallet.Type {
	case wallets.SafeHeron:
		credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
			user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, code)
			return
		}

		for _, asset := range wallet.SafeheronWallet {
			address, err := w.safeheronWalletService.GetAssetAddresses(requestID, user.UserID, asset.AssetSymbol, credentials)
			if err != nil || len(address) < 1 {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}

			walletBalance, err := w.defiClient.GetWalletAssetsBalances(defiModels.Wallet{
				AssetSymbol: asset.AssetSymbol,
				Address:     address[0].Address,
			})
			if err != nil && err.Error() != defiProvider.ErrUnsupportedAsset {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			if walletBalance != nil {
				assetsBalances = append(assetsBalances, walletBalance...)
			}
		}

	case wallets.DFNS:
		for _, asset := range wallet.DfnsWallets {
			assetSymbol, ok := wallets.DFNSNetworkAssetsymbolMap[asset.Network]
			if !ok {
				msg := fmt.Sprintf("network: %v is not in DFNSNetworkAssetsymbolMap", asset.Network)
				w.container.GetLogger().Error(msg)
				resp := models.ApiResponse{
					Message: msg,
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			walletBalance, err := w.defiClient.GetWalletAssetsBalances(defiModels.Wallet{
				AssetSymbol: assetSymbol,
				Address:     asset.Address,
			})
			if err != nil && err.Error() != defiProvider.ErrUnsupportedAsset {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			if walletBalance != nil {
				assetsBalances = append(assetsBalances, walletBalance...)
			}
		}

	default:
		resp := models.ApiResponse{
			Message: fmt.Sprintf(walletTypeErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if err = json.NewEncoder(ctx).Encode(&assetsBalances); err != nil {
		w.container.GetLogger().Errorf("Encoding response error")

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// GetLastTransactions retrieves dates of the last transaction across user wallets
//
//	@Summary		Get list of last users transactions dates
//	@Description	Display dates of the last transactions across users wallets
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Param			users	body	polity.LastTransactionsRequest	true	"List of users"
//	@Router			/payment/last/ [post]
//	@Success		200			{object}	polity.LastTransactionsResponse
//	@Failure		400,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetLastTransactions(ctx *fasthttp.RequestCtx) {
	var request polity.LastTransactionsRequest
	var response polity.LastTransactionsResponse
	var addresses []polity.AssetAddress

	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(request)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	if len(request.Users) <= 0 {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, "no users provided in request"),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	for _, user := range request.Users {
		userWallets, err := w.walletRequestsService.GetPolityWalletsForUser(user.ID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				continue
			} else {
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
		}

		for _, wallet := range userWallets {
			lastDate := time.Time{}

			if request.WalletType != wallet.Type {
				continue
			}

			switch wallet.Type {
			case wallets.SafeHeron:
				credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
					"", w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
				if err != nil {
					w.container.GetLogger().Errorf("Cant get credentials: %v", err)
					resp := models.ApiResponse{
						Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
					}
					w.JsonResponse(ctx, resp, code)
					return
				}

				for _, asset := range wallet.SafeheronWallet {
					addresses, err = w.safeheronWalletService.GetAssetAddresses(wallet.RequestID, user.ID, asset.AssetSymbol, credentials)
					if err != nil {
						resp := models.ApiResponse{
							Message: fmt.Sprintf("Can't get wallet address: %s", err.Error()),
						}
						w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
						return
					}
					if len(addresses) <= 0 {
						continue
					}

					transactionsHistory, err := w.moralisService.GetTransactionsHistory(asset.AssetSymbol, addresses[0].Address, pagination.Parameters{Limit: 1, Offset: 0})
					if err != nil && !errors.Is(err, explorers.UnsupportedAssetErr) {
						resp := models.ApiResponse{
							Message: fmt.Sprintf("Can't get history: %s", err.Error()),
						}
						w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
						return
					}
					if transactionsHistory == nil {
						continue
					}
					if len(transactionsHistory.Data) <= 0 {
						continue
					}

					if transactionsHistory.Data[0].Date.After(lastDate) {
						lastDate = transactionsHistory.Data[0].Date
					}
				}
			case wallets.DFNS:
				for _, asset := range wallet.DfnsWallets {
					transactionsHistory, err := w.moralisService.GetTransactionsHistory(asset.Network, asset.Address, pagination.Parameters{Limit: 1, Offset: 0})
					if err != nil {
						resp := models.ApiResponse{
							Message: fmt.Sprintf("Can't get history: %s", err.Error()),
						}
						w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
						return
					}
					if len(transactionsHistory.Data) <= 0 {
						continue
					}

					if transactionsHistory.Data[0].Date.After(lastDate) {
						lastDate = transactionsHistory.Data[0].Date
					}
				}
			default:
				w.container.GetLogger().Errorf("Unknown wallet type: %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s: %s", "Unknown wallet vendor", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			response.Transactions = append(response.Transactions, polity.LastTransaction{
				UserID:    user.ID,
				RequestID: wallet.RequestID,
				Date:      lastDate,
			})
		}
	}

	if err = json.NewEncoder(ctx).Encode(response); err != nil {
		w.container.GetLogger().Errorf("Encoding response error")
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// GetAssetTransactions works with request and response parameters and passes to the service layer
//
//	@Summary		Get history of transactions for provided wallet asset
//	@Description	Get the full history of transaction for a particular wallet and its particular asset. Specify optional parameters like /transactions?offset=5&limit=5
//	@Tags			assets
//	@Param			request_uuid	path	string	true	"Request ID of wallet"
//	@Param			asset_symbol	path	string	true	"Asset for which user is looking for history of transactions"
//	@Param			limit			path	string	false	"Optional transactions limit, default = 10"
//	@Param			offset			path	string	false	"Optional transactions offset, default = 0"
//	@Security		BearerAuth
//	@Produce		json
//	@Router			/wallet/{request_uuid}/{asset_symbol}/transactions [get]
//	@Security		BearerAuth
//	@Success		200				{object}	swagger.TransactionsHistory
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetAssetTransactions(ctx *fasthttp.RequestCtx) {
	var transactionsHistory *polity.TransactionsHistory
	var nativeTransactionHistory *polity.TransactionsHistory
	var addresses []polity.AssetAddress
	var limitNumber, offsetNumber int64
	var err error

	w.container.GetLogger().Debug("get transaction history")

	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))
	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	asset := fmt.Sprintf("%v", ctx.UserValue(assetSymbolKey))
	verified = w.container.GetValidator().ValidateAssetSymbol(asset)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	formattedLimit := string(ctx.URI().QueryArgs().Peek(limit))
	if formattedLimit == "" {
		limitNumber = defaultLimit
	} else {
		limitNumber, err = strconv.ParseInt(formattedLimit, 10, 64)
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, limitErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
	}

	formattedOffset := string(ctx.URI().QueryArgs().Peek(offset))
	if formattedOffset == "" {
		offsetNumber = defaultOffset
	} else {
		offsetNumber, err = strconv.ParseInt(formattedOffset, 10, 64)
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", badRequestBodyErr, offsetErr),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
			return
		}
	}

	transactionsPagination := pagination.Parameters{
		Limit:  limitNumber,
		Offset: offsetNumber,
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	wallet, err := w.walletRequestsService.GetPolityWalletForUser(requestID, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: walletsErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant get polity wallet. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	switch wallet.Type {
	case wallets.SafeHeron:
		credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
			user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
		if err != nil {
			w.container.GetLogger().Errorf("Cant get credentials: %v", err)
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, code)
			return
		}
		addresses, err = w.safeheronWalletService.GetAssetAddresses(wallet.RequestID, user.UserID, asset, credentials)
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("Can't get wallet address: %s", err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
			return
		}
		if slices.Contains(w.container.GetConfig().PaymentsHistoryExclusionAssets, asset) {
			nativeTransactionHistory, err = w.safeheronWalletService.GetAssetTransactions(wallet, asset, transactionsPagination, credentials)
			if err != nil {
				w.container.GetLogger().Error("Cant get safeheron asset history")
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s: %s", getAssetHistoryErr, err.Error()),
				}
				w.JsonResponse(ctx, resp, code)
				return
			}
		}
	case wallets.DFNS:
		credentials, code, err := w.polityVaultClient.GetDFNSWalletCredentials(user.Token)
		if err != nil {
			w.container.GetLogger().Errorf("Cant get credentials: %v", err)
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", getCredentialsDFNSErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, code)
			return
		}
		addresses, err = w.dfnsWalletService.GetAssetAddresses(wallet.RequestID, user.UserID, asset, credentials)
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("Can't get wallet address: %s", err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
			return
		}
		if slices.Contains(w.container.GetConfig().PaymentsHistoryExclusionAssets, asset) {
			nativeTransactionHistory, err = w.dfnsWalletService.GetAssetTransactions(wallet, asset, transactionsPagination, credentials)
			if err != nil {
				w.container.GetLogger().Error("Cant get dfns asset history")
				resp := models.ApiResponse{
					Message: fmt.Sprintf("%s: %s", getAssetHistoryErr, err.Error()),
				}
				w.JsonResponse(ctx, resp, code)
				return
			}
		}
	default:
		w.container.GetLogger().Errorf("Unknown wallet type: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", "Unknown wallet vendor", err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if nativeTransactionHistory == nil {
		transactionsHistory, err = w.moralisService.GetTransactionsHistory(asset, addresses[0].Address, transactionsPagination)
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("Can't get history: %s", err.Error()),
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
			return
		}
	} else {
		transactionsHistory = nativeTransactionHistory
	}

	previousOffset, nextOffset := utils.GetOffsets(offsetNumber, limitNumber, transactionsHistory.Pagination)
	if previousOffset != -1 {
		transactionsHistory.Pagination.Previous = fmt.Sprintf("%s://%s/api/v1/wallet/%s/%s/transactions?limit=%s&offset=%d", string(ctx.Request.URI().Scheme()), string(ctx.Request.URI().Host()), requestID, asset, formattedLimit, previousOffset)
	}
	if nextOffset != -1 {
		transactionsHistory.Pagination.Next = fmt.Sprintf("%s://%s/api/v1/wallet/%s/%s/transactions?limit=%s&offset=%d", string(ctx.Request.URI().Scheme()), string(ctx.Request.URI().Host()), requestID, asset, formattedLimit, nextOffset)
	}

	encoder := json.NewEncoder(ctx)
	encoder.SetEscapeHTML(false)
	if err = encoder.Encode(transactionsHistory); err != nil {
		w.container.GetLogger().Errorf("Cant marshall transaction history: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// ScanPaymentDFNS works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Scan transaction
//	@Description	Scan transaction from DFNS wallet
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Param			transaction	body	polity.TransactionRequest	true	"Transaction details input"
//	@Router			/payment/dfns/scan/ [post]
//	@Security		BearerAuth
//	@Success		200				{object}	swagger.ScanTransactionResponse
//	@Failure		400,401,404,500	{string}	swagger.ApiResponse
func (w *WalletInfrastructureController) ScanPaymentDFNS(ctx *fasthttp.RequestCtx) {
	var tx *polity.TransactionRequest

	err := json.Unmarshal(ctx.PostBody(), &tx)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when try to make payment: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	err = w.container.GetValidator().Validator.Struct(tx)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	verified := w.container.GetValidator().ValidateAssetSymbol(tx.Asset)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetDFNSWalletCredentials(user.Token)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsDFNSErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}
	address, err := w.dfnsWalletService.GetAssetAddresses(tx.From, user.UserID, tx.Asset, credentials)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("cant get address: %s", err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	report, err := w.blowfishService.ScanTransaction(&blowfishModels.ScanTransactionRequest{
		TransactionObjects: []blowfishModels.TransactionObjects{
			{
				From:  address[0].Address,
				To:    tx.To,
				Data:  tx.Data,
				Value: tx.Value,
			},
		},
		UserAccount: address[0].Address,
		Metadata: blowfishModels.Metadata{
			Origin: string(ctx.Request.Header.Peek("Origin")),
		},
	}, tx.Asset)
	if err != nil {
		w.container.GetLogger().Errorf("cant scan transaction %s", err.Error())
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	w.JsonResponse(ctx, report, fasthttp.StatusOK)
}

// ScanPaymentSafeheron works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Scan transaction
//	@Description	Scan transaction from Safeheron wallet
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Param			transaction	body	polity.TransactionRequest	true	"Transaction details input"
//	@Router			/payment/safeheron/scan/ [post]
//	@Security		BearerAuth
//	@Success		200				{object}	swagger.ScanTransactionResponse
//	@Failure		400,401,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ScanPaymentSafeheron(ctx *fasthttp.RequestCtx) {
	var tx *polity.TransactionRequest

	err := json.Unmarshal(ctx.PostBody(), &tx)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when try to make payment: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	err = w.container.GetValidator().Validator.Struct(tx)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}
	verified := w.container.GetValidator().ValidateAssetSymbol(tx.Asset)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
		user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, code)
		return
	}
	address, err := w.safeheronWalletService.GetAssetAddresses(tx.From, user.UserID, tx.Asset, credentials)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("cant get address: %s", err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	report, err := w.blowfishService.ScanTransaction(&blowfishModels.ScanTransactionRequest{
		TransactionObjects: []blowfishModels.TransactionObjects{
			{
				From:  address[0].Address,
				To:    tx.To,
				Data:  tx.Data,
				Value: tx.Value,
			},
		},
		UserAccount: address[0].Address,
		Metadata: blowfishModels.Metadata{
			Origin: string(ctx.Request.Header.Peek("Origin")),
		},
	}, tx.Asset)
	if err != nil {
		w.container.GetLogger().Errorf("cant scan transaction %s", err.Error())
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
	}

	w.JsonResponse(ctx, report, fasthttp.StatusOK)
}

// ScanWalletConnectCall works with request and response parameters, interacts with Polity Vault and passes to the service layer
//
//	@Summary		Scan invoking appropriate APIs
//	@Description	Scan invoking appropriate APIs of wallet managers (safeheron/dfns) for Wallet Connect call
//	@Tags			wallet connect
//	@Accept			json
//	@Produce		json
//	@Param			asset_symbol	path	string							true	"Asset Symbol of requested operation"
//	@Param			transaction		body	swagger.WalletConnectRequest	true	"Request details input"
//	@Router			/assets/{asset_symbol}/invoke/scan/ [post]
//	@Security		BearerAuth
//	@Success		200			{object}	swagger.ScanTransactionResponse
//	@Failure		400,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) ScanWalletConnectCall(ctx *fasthttp.RequestCtx) {
	var request *wcModels.Request

	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		w.container.GetLogger().Errorf("Bad request when try to make payment: %v", err)
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	err = w.container.GetValidator().Validator.Struct(request)
	if err != nil {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	assetSymbol := fmt.Sprintf("%v", ctx.UserValue(assetSymbolKey))
	verified := w.container.GetValidator().ValidateAssetSymbol(assetSymbol)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, assetSymbolErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	callHandler, err := w.callHandlerManager.GetMethodHandler(assetSymbol, request.Method)
	if err != nil {
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)

	response, err := callHandler.Scan(user, request, assetSymbol)
	if err != nil {
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if err = json.NewEncoder(ctx).Encode(response); err != nil {
		w.container.GetLogger().Errorf("Encoding response error")
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// GetDepositsBalances works with request and response parameters and passes to the service layer
//
//	@Summary		Get deposits balances
//	@Description	Get deposits balances for users wallet
//	@Tags			defi
//	@Produce		json
//	@Router			/defi/{request_uuid}/deposits/ [get]
//	@Param			request_uuid	path	string	true	"Wallet Request ID"
//	@Security		BearerAuth
//	@Success		200			{object}	swagger.ProtocolBalancesResponse
//	@Failure		400,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetDepositsBalances(ctx *fasthttp.RequestCtx) {
	chainsProtocolBalances := defiModels.ProtocolBalancesResponse{}

	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))
	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	wallet, err := w.walletRequestsService.GetPolityWalletForUser(requestID, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: walletsErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant get polity wallet. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	switch wallet.Type {
	case wallets.SafeHeron:
		credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
			user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, code)
			return
		}

		for _, asset := range wallet.SafeheronWallet {
			address, err := w.safeheronWalletService.GetAssetAddresses(requestID, user.UserID, asset.AssetSymbol, credentials)
			if err != nil || len(address) < 1 {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}

			walletBalance, err := w.defiClient.GetWalletDepositsBalance(defiModels.Wallet{
				AssetSymbol: asset.AssetSymbol,
				Address:     address[0].Address,
			})
			if err != nil && err.Error() != defiProvider.ErrUnsupportedAsset {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			if walletBalance != nil {
				chainsProtocolBalances.ChainsProtocols = append(chainsProtocolBalances.ChainsProtocols, defiModels.ChainProtocols{
					AssetSymbol:      asset.AssetSymbol,
					ProtocolBalances: walletBalance.ProtocolBalances,
					TotalBorrowed:    walletBalance.TotalBorrowed,
					TotalSupplied:    walletBalance.TotalSupplied,
					TotalNetWorth:    walletBalance.TotalNetWorth,
					TotalRewarded:    walletBalance.TotalRewarded,
				})

				chainsProtocolBalances.TotalBorrowed += walletBalance.TotalBorrowed
				chainsProtocolBalances.TotalNetWorth += walletBalance.TotalNetWorth
				chainsProtocolBalances.TotalRewarded += walletBalance.TotalRewarded
				chainsProtocolBalances.TotalSupplied += walletBalance.TotalSupplied
			}
		}

	case wallets.DFNS:
		for _, asset := range wallet.DfnsWallets {
			assetSymbol, ok := wallets.DFNSNetworkAssetsymbolMap[asset.Network]
			if !ok {
				msg := fmt.Sprintf("network: %v is not in DFNSNetworkAssetsymbolMap", asset.Network)
				w.container.GetLogger().Error(msg)
				resp := models.ApiResponse{
					Message: msg,
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			walletBalance, err := w.defiClient.GetWalletDepositsBalance(defiModels.Wallet{
				AssetSymbol: assetSymbol,
				Address:     asset.Address,
			})
			if err != nil && err.Error() != defiProvider.ErrUnsupportedAsset {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			if walletBalance != nil {
				chainsProtocolBalances.ChainsProtocols = append(chainsProtocolBalances.ChainsProtocols, defiModels.ChainProtocols{
					AssetSymbol:      assetSymbol,
					ProtocolBalances: walletBalance.ProtocolBalances,
					TotalBorrowed:    walletBalance.TotalBorrowed,
					TotalSupplied:    walletBalance.TotalSupplied,
					TotalNetWorth:    walletBalance.TotalNetWorth,
					TotalRewarded:    walletBalance.TotalRewarded,
				})

				chainsProtocolBalances.TotalBorrowed += walletBalance.TotalBorrowed
				chainsProtocolBalances.TotalNetWorth += walletBalance.TotalNetWorth
				chainsProtocolBalances.TotalRewarded += walletBalance.TotalRewarded
				chainsProtocolBalances.TotalSupplied += walletBalance.TotalSupplied
			}
		}

	default:
		resp := models.ApiResponse{
			Message: fmt.Sprintf(walletTypeErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if err = json.NewEncoder(ctx).Encode(&chainsProtocolBalances); err != nil {
		w.container.GetLogger().Errorf("Encoding response error")

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}

// GetLendingBalances works with request and response parameters and passes to the service layer
//
//	@Summary		Get lending balances
//	@Description	Get lending balances for users wallet
//	@Tags			defi
//	@Produce		json
//	@Router			/defi/{request_uuid}/lending/ [get]
//	@Param			request_uuid	path	string	true	"Wallet Request ID"
//	@Security		BearerAuth
//	@Success		200			{object}	swagger.ProtocolBalancesResponse
//	@Failure		400,404,500	{object}	swagger.ApiResponse
func (w *WalletInfrastructureController) GetLendingBalances(ctx *fasthttp.RequestCtx) {
	chainsProtocolBalances := defiModels.ProtocolBalancesResponse{}

	requestID := fmt.Sprintf("%v", ctx.UserValue(requestIDKey))
	verified := w.container.GetValidator().ValidateRequestID(requestID)
	if !verified {
		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", badRequestBodyErr, requestIdErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusBadRequest)
		return
	}

	user := ctx.UserValue(userIdentity).(identity.Identity)
	wallet, err := w.walletRequestsService.GetPolityWalletForUser(requestID, user.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			resp := models.ApiResponse{
				Message: walletsErr,
			}
			w.JsonResponse(ctx, resp, fasthttp.StatusNotFound)
			return
		}
		w.container.GetLogger().Errorf("Cant get polity wallet. Error: '%v' ", err)
		resp := models.ApiResponse{
			Message: err.Error(),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	switch wallet.Type {
	case wallets.SafeHeron:
		credentials, code, err := w.polityVaultClient.GetSafeheronWalletCredentials(
			user.Token, w.container.GetConfig().SafeheronCosignerUuid, w.polityVaultClient.DefaultOptions())
		if err != nil {
			resp := models.ApiResponse{
				Message: fmt.Sprintf("%s: %s", getCredentialsSafeheronErr, err.Error()),
			}
			w.JsonResponse(ctx, resp, code)
			return
		}

		for _, asset := range wallet.SafeheronWallet {
			address, err := w.safeheronWalletService.GetAssetAddresses(requestID, user.UserID, asset.AssetSymbol, credentials)
			if err != nil || len(address) < 1 {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}

			walletBalance, err := w.defiClient.GetWalletLendingBalance(defiModels.Wallet{
				AssetSymbol: asset.AssetSymbol,
				Address:     address[0].Address,
			})
			if err != nil && err.Error() != defiProvider.ErrUnsupportedAsset {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			if walletBalance != nil {
				chainsProtocolBalances.ChainsProtocols = append(chainsProtocolBalances.ChainsProtocols, defiModels.ChainProtocols{
					AssetSymbol:      asset.AssetSymbol,
					ProtocolBalances: walletBalance.ProtocolBalances,
					TotalBorrowed:    walletBalance.TotalBorrowed,
					TotalSupplied:    walletBalance.TotalSupplied,
					TotalNetWorth:    walletBalance.TotalNetWorth,
					TotalRewarded:    walletBalance.TotalRewarded,
				})

				chainsProtocolBalances.TotalBorrowed += walletBalance.TotalBorrowed
				chainsProtocolBalances.TotalNetWorth += walletBalance.TotalNetWorth
				chainsProtocolBalances.TotalRewarded += walletBalance.TotalRewarded
				chainsProtocolBalances.TotalSupplied += walletBalance.TotalSupplied
			}
		}

	case wallets.DFNS:
		for _, asset := range wallet.DfnsWallets {
			assetSymbol, ok := wallets.DFNSNetworkAssetsymbolMap[asset.Network]
			if !ok {
				msg := fmt.Sprintf("network: %v is not in DFNSNetworkAssetsymbolMap", asset.Network)
				w.container.GetLogger().Error(msg)
				resp := models.ApiResponse{
					Message: msg,
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			walletBalance, err := w.defiClient.GetWalletLendingBalance(defiModels.Wallet{
				AssetSymbol: assetSymbol,
				Address:     asset.Address,
			})
			if err != nil && err.Error() != defiProvider.ErrUnsupportedAsset {
				w.container.GetLogger().Errorf("Can't get products. Error : %v", err)
				resp := models.ApiResponse{
					Message: fmt.Sprintf("Can't get products: %s", err.Error()),
				}
				w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
				return
			}
			if walletBalance != nil {
				chainsProtocolBalances.ChainsProtocols = append(chainsProtocolBalances.ChainsProtocols, defiModels.ChainProtocols{
					AssetSymbol:      assetSymbol,
					ProtocolBalances: walletBalance.ProtocolBalances,
					TotalBorrowed:    walletBalance.TotalBorrowed,
					TotalSupplied:    walletBalance.TotalSupplied,
					TotalNetWorth:    walletBalance.TotalNetWorth,
					TotalRewarded:    walletBalance.TotalRewarded,
				})

				chainsProtocolBalances.TotalBorrowed += walletBalance.TotalBorrowed
				chainsProtocolBalances.TotalNetWorth += walletBalance.TotalNetWorth
				chainsProtocolBalances.TotalRewarded += walletBalance.TotalRewarded
				chainsProtocolBalances.TotalSupplied += walletBalance.TotalSupplied
			}
		}

	default:
		resp := models.ApiResponse{
			Message: fmt.Sprintf(walletTypeErr),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	if err = json.NewEncoder(ctx).Encode(&chainsProtocolBalances); err != nil {
		w.container.GetLogger().Errorf("Encoding response error")

		resp := models.ApiResponse{
			Message: fmt.Sprintf("%s: %s", encodeResponseErr, err.Error()),
		}
		w.JsonResponse(ctx, resp, fasthttp.StatusInternalServerError)
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
}
