workspace {

    model {
        // External Users
        investor = person "Investor" "Individual or institutional investor who wants to invest in assets through the platform"
        adviser = person "Adviser" "Financial adviser who manages investor relationships and creates investment agreements"
        networkOperator = person "Network Operator" "Platform administrator who monitors system health and manages operations"
        avatar = person "Avatar" "Digital representation of an investor with limited permissions (max 3 per investor)"

        // External Systems
        dfnsProvider = softwareSystem "DFNS" "Multi-Party Computation wallet provider for secure asset custody" "External System"
        safeheronProvider = softwareSystem "Safeheron" "Multi-Party Computation wallet provider for secure asset custody" "External System"
        fractalId = softwareSystem "FractalID" "KYC/Identity verification service for user onboarding" "External System"
        hashicorpVault = softwareSystem "HashiCorp Vault" "Secrets management system for storing MPC provider credentials" "External System"
        blockchainNetworks = softwareSystem "Blockchain Networks" "External blockchain networks (Ethereum, Bitcoin, etc.) for asset transactions" "External System"
        matrixServer = softwareSystem "Matrix Server" "Decentralized communication protocol for real-time messaging" "External System"

        // Main Software System
        polityPlatform = softwareSystem "Polity Platform" "Multi-Party Computation wallet platform for secure digital asset management and investment agreements" {

            // Frontend Applications
            authPortal = container "Auth Portal" "Authentication and user onboarding interface" "React, JavaScript" "Web Browser"
            userCabinet = container "User Cabinet" "Personal dashboard for advisers and avatars to manage agreements and wallets" "React, JavaScript" "Web Browser"
            operatorDashboard = container "Operator Dashboard" "Administrative interface for network operators to monitor system statistics" "React, JavaScript" "Web Browser"

            // Backend Services
            authGateway = container "Auth Gateway" "Handles user authentication, avatar management, and FractalID integration" "Kotlin, Spring Boot" "Microservice" {
                // Controllers
                authGatewayAuthController = component "Auth Controller" "Handles user authentication and registration endpoints" "Spring Boot REST Controller"
                authGatewayAdviserController = component "Adviser Controller" "Manages adviser-specific operations and avatar management" "Spring Boot REST Controller"
                authGatewayInvestorAvatarController = component "Investor Avatar Controller" "Handles avatar login, registration, and management for investors" "Spring Boot REST Controller"
                authGatewayInvestorAdviserController = component "Investor Adviser Controller" "Manages investor-adviser relationships" "Spring Boot REST Controller"
                authGatewayInvestorController = component "Investor Controller" "Handles investor-specific operations" "Spring Boot REST Controller"
                authGatewayFractalIdAuthController = component "FractalID Auth Controller" "Integrates with FractalID for KYC authentication" "Spring Boot REST Controller"
                authGatewayCodeToUUIDController = component "Code to UUID Controller" "Manages code generation and UUID mapping" "Spring Boot REST Controller"
                authGatewayWalletStatisticsController = component "Wallet Statistics Controller" "Provides wallet statistics for network operators" "Spring Boot REST Controller"
                authGatewayNetworkOperatorController = component "Network Operator Controller" "Provides network operator dashboard functionality" "Spring Boot REST Controller"
                authGatewayEmailController = component "Email Controller" "Handles email operations" "Spring Boot REST Controller"
                authGatewaySlackNotificationController = component "Slack Notification Controller" "Sends notifications to Slack channels" "Spring Boot REST Controller"
                authGatewayHealthcheckController = component "Healthcheck Controller" "Provides health check endpoints" "Spring Boot REST Controller"

                // Services
                authGatewayAuthenticationService = component "Authentication Service" "Core authentication logic, token management, and user registration" "Spring Boot Service"
                authGatewayJwtService = component "JWT Service" "JWT token generation, validation, and claims management" "Spring Boot Service"
                authGatewayCustomerRouteService = component "Customer Route Service" "Manages customer routing, roles, and avatar relationships" "Spring Boot Service"
                authGatewayCodeToUUIDService = component "Code to UUID Service" "Generates and manages UUID codes for avatar operations" "Spring Boot Service"
                authGatewayTurnOnCoSignerService = component "Turn On CoSigner Service" "Manages co-signer activation for avatars" "Spring Boot Service"

                // Repositories
                authGatewayUserRepository = component "User Repository" "Data access layer for user entities" "JPA Repository"
                authGatewayTokenRepository = component "Token Repository" "Data access layer for JWT tokens" "JPA Repository"
                authGatewayCustomerRouteRepository = component "Customer Route Repository" "Data access layer for customer routes and roles" "JPA Repository"
                authGatewayCodeToUUIDRepository = component "Code to UUID Repository" "Data access layer for UUID code mappings" "JPA Repository"

                // Security Components
                authGatewayJwtAuthenticationFilter = component "JWT Authentication Filter" "Filters and validates JWT tokens in requests" "Spring Security Filter"
                authGatewaySecurityConfiguration = component "Security Configuration" "Spring Security configuration and CORS settings" "Spring Security Config"
                authGatewayApplicationConfig = component "Application Configuration" "Authentication providers and password encoding configuration" "Spring Configuration"

                // Component Relationships
                authGatewayAuthController -> authGatewayAuthenticationService "Uses for authentication operations"
                authGatewayAuthController -> authGatewayCustomerRouteService "Uses for customer route management"
                authGatewayAdviserController -> authGatewayCustomerRouteService "Uses for adviser operations"
                authGatewayAdviserController -> authGatewayJwtService "Uses for JWT operations"
                authGatewayInvestorAvatarController -> authGatewayAuthenticationService "Uses for avatar authentication"
                authGatewayInvestorAvatarController -> authGatewayCustomerRouteService "Uses for avatar management"
                authGatewayInvestorAvatarController -> authGatewayTurnOnCoSignerService "Uses for co-signer operations"
                authGatewayInvestorAdviserController -> authGatewayCustomerRouteService "Uses for investor-adviser relationships"
                authGatewayInvestorController -> authGatewayCustomerRouteService "Uses for investor operations"
                authGatewayFractalIdAuthController -> authGatewayAuthenticationService "Uses for FractalID authentication"
                authGatewayFractalIdAuthController -> authGatewayCustomerRouteService "Uses for user management"
                authGatewayCodeToUUIDController -> authGatewayCodeToUUIDService "Uses for code operations"
                authGatewayCodeToUUIDController -> authGatewayCodeToUUIDRepository "Uses for data access"
                authGatewayNetworkOperatorController -> authGatewayCustomerRouteService "Uses for network operator statistics"
                authGatewayWalletStatisticsController -> authGatewayCustomerRouteService "Uses for wallet statistics"

                authGatewayAuthenticationService -> authGatewayUserRepository "Stores and retrieves user data"
                authGatewayAuthenticationService -> authGatewayTokenRepository "Manages JWT tokens"
                authGatewayAuthenticationService -> authGatewayJwtService "Generates and validates tokens"
                authGatewayAuthenticationService -> authGatewayCustomerRouteService "Manages customer routes"
                authGatewayCustomerRouteService -> authGatewayCustomerRouteRepository "Stores and retrieves customer route data"
                authGatewayJwtService -> authGatewayCustomerRouteService "Gets customer route information for token claims"
                authGatewayCodeToUUIDService -> authGatewayCodeToUUIDRepository "Stores and retrieves UUID codes"

                authGatewayJwtAuthenticationFilter -> authGatewayJwtService "Validates JWT tokens"
                authGatewayJwtAuthenticationFilter -> authGatewayTokenRepository "Checks token validity"
                authGatewaySecurityConfiguration -> authGatewayJwtAuthenticationFilter "Uses for request filtering"
                authGatewaySecurityConfiguration -> authGatewayApplicationConfig "Uses authentication providers"
            }
            userInfrastructure = container "User Infrastructure Service" "Manages user data, agreements, avatars, and Corda blockchain integration" "Kotlin, Spring Boot" "Microservice" {
                // Controllers
                userInfraAdviserController = component "Adviser Controller" "Handles adviser operations for avatar management and agreements" "Spring Boot REST Controller"
                userInfraAvatarController = component "Avatar Controller" "Manages avatar agreement operations and blockchain interactions" "Spring Boot REST Controller"
                userInfraAuctionController = component "Auction Controller" "Deprecated controller for auction operations" "Spring Boot REST Controller"
                userInfraKeyExchangeController = component "Key Exchange Controller" "Handles public key exchange for secure communications" "Spring Boot REST Controller"
                userInfraNotificationController = component "Notification Controller" "Manages user notifications and webhooks" "Spring Boot REST Controller"
                userInfraHealthcheckController = component "Healthcheck Controller" "Provides health check endpoints" "Spring Boot REST Controller"

                // Services
                userInfraAgreementService = component "Agreement Service" "Manages blockchain agreements and contract operations" "Spring Boot Service"
                userInfraAvatarService = component "Avatar Service" "Handles avatar creation, management, and operations" "Spring Boot Service"
                userInfraNodeService = component "Node Service" "Manages Corda node operations and blockchain flows" "Spring Boot Service"
                userInfraPortalService = component "Portal Service" "Integrates with external portal for avatar notifications" "Spring Boot Service"
                userInfraJwtService = component "JWT Service" "JWT token validation and claims extraction" "Spring Boot Service"
                userInfraFileService = component "File Service" "Handles file upload and management operations" "Spring Boot Service"
                userInfraKeyExchangeService = component "Key Exchange Service" "Manages public key storage and exchange" "Spring Boot Service"
                userInfraNotificationService = component "Notification Service" "Handles notification creation and delivery" "Spring Boot Service"

                // Repositories
                userInfraAgreementRepository = component "Agreement Repository" "Data access layer for agreement entities" "JPA Repository"
                userInfraAvatarRepository = component "Avatar Repository" "Data access layer for avatar entities" "JPA Repository"
                userInfraNodeRepository = component "Node Repository" "Data access layer for node entities" "JPA Repository"
                userInfraKeyExchangeRepository = component "Key Exchange Repository" "Data access layer for key exchange entities" "JPA Repository"
                userInfraNotificationRepository = component "Notification Repository" "Data access layer for notification entities" "JPA Repository"

                // Component Relationships
                userInfraAdviserController -> userInfraAvatarService "Uses for avatar management"
                userInfraAdviserController -> userInfraAgreementService "Uses for agreement operations"
                userInfraAdviserController -> userInfraPortalService "Uses for portal integration"
                userInfraAdviserController -> userInfraNodeService "Uses for node operations"
                userInfraAdviserController -> userInfraJwtService "Uses for JWT validation"
                userInfraAdviserController -> userInfraFileService "Uses for file operations"
                userInfraAdviserController -> userInfraNotificationService "Uses for notifications"
                userInfraAdviserController -> userInfraKeyExchangeService "Uses for key exchange"

                userInfraAvatarController -> userInfraAvatarService "Uses for avatar operations"
                userInfraAvatarController -> userInfraAgreementService "Uses for agreement management"
                userInfraAvatarController -> userInfraJwtService "Uses for JWT validation"
                userInfraAvatarController -> userInfraFileService "Uses for file operations"
                userInfraAvatarController -> userInfraKeyExchangeService "Uses for key exchange"
                userInfraAvatarController -> userInfraNotificationService "Uses for notifications"

                userInfraKeyExchangeController -> userInfraJwtService "Uses for JWT validation"
                userInfraKeyExchangeController -> userInfraKeyExchangeService "Uses for key exchange operations"

                userInfraNotificationController -> userInfraJwtService "Uses for JWT validation"
                userInfraNotificationController -> userInfraNotificationService "Uses for notification operations"

                userInfraAgreementService -> userInfraAgreementRepository "Stores and retrieves agreement data"
                userInfraAgreementService -> userInfraNodeService "Uses for blockchain operations"
                userInfraAvatarService -> userInfraAvatarRepository "Stores and retrieves avatar data"
                userInfraNodeService -> userInfraNodeRepository "Stores and retrieves node data"
                userInfraKeyExchangeService -> userInfraKeyExchangeRepository "Stores and retrieves key exchange data"
                userInfraNotificationService -> userInfraNotificationRepository "Stores and retrieves notification data"

            }
            walletInfrastructure = container "Wallet Infrastructure Service" "Manages MPC wallets, transactions, and integrates with DFNS/Safeheron providers" "Go" "Microservice" {
                // Main Controller
                walletInfrastructureController = component "Wallet Infrastructure Controller" "Main API controller handling all wallet operations and routing" "Go REST Controller"

                // Router
                walletInfrastructureRouter = component "Wallet Infrastructure Router" "HTTP router for API endpoints and middleware" "Go Router"

                // Core Services
                dfnsWalletService = component "DFNS Wallet Service" "Manages DFNS MPC wallet operations and transactions" "Go Service"
                safeheronWalletService = component "Safeheron Wallet Service" "Manages Safeheron MPC wallet operations and transactions" "Go Service"
                walletRequestsService = component "Wallet Requests Service" "Handles wallet creation and management requests" "Go Service"
                defiService = component "DeFi Service" "Integrates with DeFi protocols and yield farming" "Go Service"
                blowfishService = component "Blowfish Service" "Transaction security scanning and risk assessment" "Go Service"

                // Callback Services
                dfnsCallbackService = component "DFNS Callback Service" "Handles DFNS webhook notifications and callbacks" "Go Service"
                safeheronCallbackService = component "Safeheron Callback Service" "Handles Safeheron webhook notifications and callbacks" "Go Service"

                // Handler Managers
                statusHandlerManager = component "Status Handler Manager" "Manages wallet status transitions and processing" "Go Service"
                walletconnectHandlerManager = component "WalletConnect Handler Manager" "Manages WalletConnect protocol operations" "Go Service"

                // WalletConnect Handlers
                personalSignHandler = component "Personal Sign Handler" "Handles personal message signing via WalletConnect" "Go Handler"
                ethPaymentHandler = component "ETH Payment Handler" "Handles Ethereum payments via WalletConnect" "Go Handler"

                // Status Handlers
                provisionedHandler = component "Provisioned Handler" "Handles provisioned wallet status processing" "Go Handler"
                pendingHandler = component "Pending Handler" "Handles pending wallet status processing" "Go Handler"
                inProgressHandler = component "In Progress Handler" "Handles in-progress wallet status processing" "Go Handler"
                failedHandler = component "Failed Handler" "Handles failed wallet status processing" "Go Handler"

                // Additional Services
                favoriteAddressService = component "Favorite Address Service" "Manages user's favorite wallet addresses" "Go Service"
                moralisService = component "Moralis Service" "Blockchain explorer integration for transaction data" "Go Service"

                // Storage
                walletInfrastructureStorage = component "Wallet Infrastructure Storage" "Database layer for wallet data persistence" "Go Storage"

                // Container
                walletInfrastructureContainer = component "Wallet Infrastructure Container" "Dependency injection container for configuration and services" "Go Container"

                // Component Relationships
                walletInfrastructureRouter -> walletInfrastructureController "Routes requests to controller"
                walletInfrastructureController -> dfnsWalletService "Uses for DFNS operations"
                walletInfrastructureController -> safeheronWalletService "Uses for Safeheron operations"
                walletInfrastructureController -> walletRequestsService "Uses for wallet management"
                walletInfrastructureController -> blowfishService "Uses for transaction scanning"
                walletInfrastructureController -> defiService "Uses for DeFi operations"
                walletInfrastructureController -> dfnsCallbackService "Uses for DFNS callbacks"
                walletInfrastructureController -> safeheronCallbackService "Uses for Safeheron callbacks"
                walletInfrastructureController -> statusHandlerManager "Uses for status management"
                walletInfrastructureController -> walletconnectHandlerManager "Uses for WalletConnect operations"
                walletInfrastructureController -> favoriteAddressService "Uses for favorite addresses"
                walletInfrastructureController -> moralisService "Uses for blockchain data"

                statusHandlerManager -> provisionedHandler "Manages provisioned status"
                statusHandlerManager -> pendingHandler "Manages pending status"
                statusHandlerManager -> inProgressHandler "Manages in-progress status"
                statusHandlerManager -> failedHandler "Manages failed status"

                walletconnectHandlerManager -> personalSignHandler "Manages personal signing"
                walletconnectHandlerManager -> ethPaymentHandler "Manages ETH payments"

                dfnsWalletService -> walletInfrastructureStorage "Stores DFNS wallet data"
                safeheronWalletService -> walletInfrastructureStorage "Stores Safeheron wallet data"
                walletRequestsService -> walletInfrastructureStorage "Stores wallet request data"
                favoriteAddressService -> walletInfrastructureStorage "Stores favorite address data"


            }
            polityVault = container "Polity Vault" "Manages MPC provider credentials and integrates with HashiCorp Vault" "Python, Django" "Microservice" {
                // Views (Controllers)
                polityVaultWalletCreationRequestListView = component "Wallet Creation Request List View" "Handles wallet creation request listing and creation" "Django REST View"
                polityVaultWalletCreationRequestDetailsView = component "Wallet Creation Request Details View" "Handles individual wallet creation request operations" "Django REST View"
                polityVaultDFNSWalletInfoListView = component "DFNS Wallet Info List View" "Manages DFNS wallet credential listing" "Django REST View"
                polityVaultDFNSWalletInfoDetailsView = component "DFNS Wallet Info Details View" "Handles DFNS wallet credential details and retrieval" "Django REST View"
                polityVaultSafeheronWalletInfoListView = component "Safeheron Wallet Info List View" "Manages Safeheron wallet credential listing" "Django REST View"
                polityVaultSafeheronWalletInfoDetailsView = component "Safeheron Wallet Info Details View" "Handles Safeheron wallet credential details and retrieval" "Django REST View"
                polityVaultCoSignerControlView = component "CoSigner Control View" "Manages Safeheron co-signer provisioning and control" "Django REST View"

                // Models
                polityVaultWalletCreationRequest = component "Wallet Creation Request Model" "Database model for wallet creation requests" "Django Model"
                polityVaultDFNSWalletInfo = component "DFNS Wallet Info Model" "Database model for DFNS wallet credentials and metadata" "Django Model"
                polityVaultSafeheronWalletInfo = component "Safeheron Wallet Info Model" "Database model for Safeheron wallet credentials and metadata" "Django Model"

                // Serializers
                polityVaultWalletCreationRequestSerializer = component "Wallet Creation Request Serializer" "Serializes wallet creation request data for API responses" "Django REST Serializer"
                polityVaultDFNSWalletInfoSerializer = component "DFNS Wallet Info Serializer" "Serializes DFNS wallet credential data for API responses" "Django REST Serializer"
                polityVaultSafeheronWalletInfoSerializer = component "Safeheron Wallet Info Serializer" "Serializes Safeheron wallet credential data for API responses" "Django REST Serializer"

                // Backend Services
                polityVaultHashiCorpVaultClient = component "HashiCorp Vault Client" "Manages secure credential storage and retrieval from HashiCorp Vault" "Python Service"
                polityVaultSafeheronManager = component "Safeheron Manager" "Manages Safeheron co-signer infrastructure and AWS resources" "Python Service"
                polityVaultCosignerManager = component "Cosigner Manager" "Handles co-signer lifecycle management and database operations" "Python Service"

                // Celery Tasks
                polityVaultProvisionTask = component "Provision Task" "Celery task for provisioning Safeheron co-signer instances" "Celery Task"
                polityVaultDeprovisionTask = component "Deprovision Task" "Celery task for deprovisioning Safeheron co-signer instances" "Celery Task"
                polityVaultLaunchCosignerTask = component "Launch Cosigner Task" "Celery task for launching co-signer instances" "Celery Task"
                polityVaultTriggerStopCosignersTask = component "Trigger Stop Cosigners Task" "Celery task for stopping idle co-signer instances" "Celery Task"

                // Authentication Middleware
                polityVaultJWTAuthMiddleware = component "JWT Authentication Middleware" "Validates JWT tokens and extracts user information" "Django Middleware"

                // AWS Integration Components
                polityVaultBotoManager = component "Boto Manager" "Manages AWS EC2 and S3 operations for co-signer infrastructure" "Python Service"
                polityVaultBotoSSH = component "Boto SSH" "Handles SSH connections and file transfers to EC2 instances" "Python Service"
                polityVaultCoSignerDbHelper = component "CoSigner DB Helper" "Manages co-signer database operations" "Python Service"

                // Component Relationships
                polityVaultWalletCreationRequestListView -> polityVaultWalletCreationRequest "Uses for data access"
                polityVaultWalletCreationRequestListView -> polityVaultWalletCreationRequestSerializer "Uses for serialization"
                polityVaultWalletCreationRequestDetailsView -> polityVaultWalletCreationRequest "Uses for data access"
                polityVaultWalletCreationRequestDetailsView -> polityVaultWalletCreationRequestSerializer "Uses for serialization"

                polityVaultDFNSWalletInfoListView -> polityVaultDFNSWalletInfo "Uses for data access"
                polityVaultDFNSWalletInfoListView -> polityVaultDFNSWalletInfoSerializer "Uses for serialization"
                polityVaultDFNSWalletInfoDetailsView -> polityVaultDFNSWalletInfo "Uses for data access"
                polityVaultDFNSWalletInfoDetailsView -> polityVaultDFNSWalletInfoSerializer "Uses for serialization"
                polityVaultDFNSWalletInfoDetailsView -> polityVaultHashiCorpVaultClient "Uses for credential retrieval"

                polityVaultSafeheronWalletInfoListView -> polityVaultSafeheronWalletInfo "Uses for data access"
                polityVaultSafeheronWalletInfoListView -> polityVaultSafeheronWalletInfoSerializer "Uses for serialization"
                polityVaultSafeheronWalletInfoDetailsView -> polityVaultSafeheronWalletInfo "Uses for data access"
                polityVaultSafeheronWalletInfoDetailsView -> polityVaultSafeheronWalletInfoSerializer "Uses for serialization"
                polityVaultSafeheronWalletInfoDetailsView -> polityVaultHashiCorpVaultClient "Uses for credential retrieval"

                polityVaultCoSignerControlView -> polityVaultSafeheronWalletInfo "Uses for co-signer management"
                polityVaultCoSignerControlView -> polityVaultProvisionTask "Triggers for co-signer provisioning"
                polityVaultCoSignerControlView -> polityVaultCosignerManager "Uses for co-signer operations"

                polityVaultDFNSWalletInfo -> polityVaultHashiCorpVaultClient "Uses for credential storage/retrieval"
                polityVaultSafeheronWalletInfo -> polityVaultHashiCorpVaultClient "Uses for credential storage/retrieval"

                polityVaultSafeheronManager -> polityVaultBotoManager "Uses for AWS operations"
                polityVaultSafeheronManager -> polityVaultBotoSSH "Uses for SSH operations"
                polityVaultSafeheronManager -> polityVaultCoSignerDbHelper "Uses for database operations"

                polityVaultProvisionTask -> polityVaultSafeheronManager "Uses for infrastructure provisioning"
                polityVaultProvisionTask -> polityVaultSafeheronWalletInfo "Updates provisioning status"
                polityVaultDeprovisionTask -> polityVaultSafeheronManager "Uses for infrastructure cleanup"
                polityVaultLaunchCosignerTask -> polityVaultBotoManager "Uses for instance management"
                polityVaultTriggerStopCosignersTask -> polityVaultBotoManager "Uses for instance management"
                polityVaultTriggerStopCosignersTask -> polityVaultSafeheronWalletInfo "Updates stopping status"
            }
            storeAuction = container "Store & Auction Service" "Handles product store and real-time auction functionality" "Go" "Microservice" {
                // Auction Controllers
                storeAuctionAuctionController = component "Auction Controller" "Handles auction operations, bidding, and WebSocket connections" "Go REST Controller"

                // Store Controllers
                storeAuctionStoreController = component "Store Controller" "Handles store operations and won lot management" "Go REST Controller"

                // Routers
                storeAuctionAuctionRouter = component "Auction Router" "Routes auction API endpoints and WebSocket connections" "Go Router"
                storeAuctionStoreRouter = component "Store Router" "Routes store API endpoints" "Go Router"

                // Core Services
                storeAuctionAuctionService = component "Auction Service" "Core auction logic, lot management, and real-time bidding" "Go Service"
                storeAuctionStoreService = component "Store Service" "Manages won lots and user permissions" "Go Service"
                storeAuctionBotService = component "Bot Service" "Automated bidding bot for auction participation" "Go Service"

                // Storage Components
                storeAuctionAuctionStorage = component "Auction Storage" "Database layer for auction data persistence" "Go Storage"
                storeAuctionStoreStorage = component "Store Storage" "Database layer for store data persistence" "Go Storage"

                // Container Components
                storeAuctionAuctionContainer = component "Auction Container" "Dependency injection container for auction services" "Go Container"
                storeAuctionStoreContainer = component "Store Container" "Dependency injection container for store services" "Go Container"

                // Models
                storeAuctionActiveLot = component "Active Lot Model" "Represents active auction lots with bidding state" "Go Model"
                storeAuctionWonLot = component "Won Lot Model" "Represents completed auction lots won by users" "Go Model"
                storeAuctionBid = component "Bid Model" "Represents individual bids placed on auction lots" "Go Model"
                storeAuctionTier = component "Tier Model" "Represents product tiers with pricing and availability" "Go Model"
                storeAuctionProduct = component "Product Model" "Represents products available for auction" "Go Model"

                // Middleware Components
                storeAuctionJWTMiddleware = component "JWT Middleware" "Validates JWT tokens and extracts user identity" "Go Middleware"
                storeAuctionCORSMiddleware = component "CORS Middleware" "Handles cross-origin resource sharing" "Go Middleware"

                // Configuration Components
                storeAuctionConfig = component "Configuration" "Application configuration management" "Go Config"
                storeAuctionValidator = component "Validator" "Request validation and data sanitization" "Go Validator"
                storeAuctionFlagger = component "Feature Flagger" "Feature flag management for store functionality" "Go Flagger"
                storeAuctionLogger = component "Logger" "Structured logging for auction and store operations" "Go Logger"

                // Component Relationships
                storeAuctionAuctionRouter -> storeAuctionAuctionController "Routes requests to auction controller"
                storeAuctionStoreRouter -> storeAuctionStoreController "Routes requests to store controller"

                storeAuctionAuctionController -> storeAuctionAuctionService "Uses for auction operations"
                storeAuctionAuctionController -> storeAuctionBotService "Uses for bot management"
                storeAuctionStoreController -> storeAuctionStoreService "Uses for store operations"

                storeAuctionAuctionService -> storeAuctionAuctionStorage "Uses for auction data persistence"
                storeAuctionStoreService -> storeAuctionStoreStorage "Uses for store data persistence"

                storeAuctionAuctionStorage -> storeAuctionActiveLot "Manages active lot data"
                storeAuctionAuctionStorage -> storeAuctionWonLot "Manages won lot data"
                storeAuctionAuctionStorage -> storeAuctionBid "Manages bid data"
                storeAuctionAuctionStorage -> storeAuctionTier "Manages tier data"
                storeAuctionAuctionStorage -> storeAuctionProduct "Manages product data"

                storeAuctionStoreStorage -> storeAuctionWonLot "Manages won lot data"
                storeAuctionStoreStorage -> storeAuctionTier "Manages tier data"

                storeAuctionAuctionController -> storeAuctionAuctionContainer "Uses for dependency injection"
                storeAuctionStoreController -> storeAuctionStoreContainer "Uses for dependency injection"

                storeAuctionAuctionContainer -> storeAuctionConfig "Provides configuration"
                storeAuctionAuctionContainer -> storeAuctionValidator "Provides validation"
                storeAuctionAuctionContainer -> storeAuctionLogger "Provides logging"

                storeAuctionStoreContainer -> storeAuctionConfig "Provides configuration"
                storeAuctionStoreContainer -> storeAuctionValidator "Provides validation"
                storeAuctionStoreContainer -> storeAuctionFlagger "Provides feature flags"
                storeAuctionStoreContainer -> storeAuctionLogger "Provides logging"

                storeAuctionJWTMiddleware -> storeAuctionAuctionController "Validates requests"
                storeAuctionJWTMiddleware -> storeAuctionStoreController "Validates requests"
                storeAuctionCORSMiddleware -> storeAuctionAuctionController "Handles CORS"
                storeAuctionCORSMiddleware -> storeAuctionStoreController "Handles CORS"

                storeAuctionBotService -> storeAuctionAuctionService "Uses for automated bidding"
            }
            operatorService = container "Operator Service" "Provides system statistics and monitoring capabilities for network operators" "Kotlin, Spring Boot" "Microservice" {
                // Controllers
                operatorNetworkOperatorController = component "Network Operator Controller" "Handles network operator statistics and monitoring endpoints" "Spring Boot REST Controller"

                // Services
                operatorRequestService = component "Request Service" "Manages HTTP requests to other services for statistics aggregation" "Spring Boot Service"
                operatorJwtService = component "JWT Service" "Handles JWT token validation and claims extraction" "Spring Boot Service"

                // DTOs (Data Transfer Objects)
                operatorTotalNetworkOperatorStatistics = component "Total Network Operator Statistics DTO" "Aggregated statistics data transfer object" "Kotlin Data Class"
                operatorPortalNetworkOperatorStatistics = component "Portal Network Operator Statistics DTO" "Portal-specific statistics data transfer object" "Kotlin Data Class"
                operatorNodeNetworkOperatorStatistic = component "Node Network Operator Statistic DTO" "Node-specific statistics data transfer object" "Kotlin Data Class"
                operatorWalletStatisticsResult = component "Wallet Statistics Result DTO" "Wallet statistics data transfer object" "Kotlin Data Class"
                operatorAgreementDto = component "Agreement DTO" "Agreement data transfer object for Corda integration" "Kotlin Data Class"

                // Flow Management Components
                operatorFlowStartResponse = component "Flow Start Response DTO" "Response object for Corda flow initiation" "Kotlin Data Class"
                operatorFlowRetrieveResponse = component "Flow Retrieve Response DTO" "Response object for Corda flow status retrieval" "Kotlin Data Class"

                // Configuration Components
                operatorSecurityConfiguration = component "Security Configuration" "Spring Security configuration for operator endpoints" "Spring Security Config"

                // Enums
                operatorRole = component "Role Enum" "User role enumeration for access control" "Kotlin Enum"
                operatorFlowStatus = component "Flow Status Enum" "Corda flow status enumeration" "Kotlin Enum"

                // Exception Handling
                operatorConnectionFlowException = component "Connection Flow Exception" "Exception for Corda flow connection errors" "Kotlin Exception"
                operatorFailedFlowException = component "Failed Flow Exception" "Exception for failed Corda flow operations" "Kotlin Exception"

                // Component Relationships
                operatorNetworkOperatorController -> operatorRequestService "Uses for statistics aggregation"
                operatorNetworkOperatorController -> operatorJwtService "Uses for JWT validation"
                operatorNetworkOperatorController -> operatorTotalNetworkOperatorStatistics "Returns aggregated statistics"

                operatorRequestService -> operatorPortalNetworkOperatorStatistics "Retrieves portal statistics"
                operatorRequestService -> operatorNodeNetworkOperatorStatistic "Retrieves node statistics"
                operatorRequestService -> operatorWalletStatisticsResult "Retrieves wallet statistics"
                operatorRequestService -> operatorAgreementDto "Handles agreement data"
                operatorRequestService -> operatorFlowStartResponse "Handles flow initiation"
                operatorRequestService -> operatorFlowRetrieveResponse "Handles flow status retrieval"

                operatorJwtService -> operatorRole "Uses for role validation"
                operatorRequestService -> operatorFlowStatus "Uses for flow status management"
                operatorRequestService -> operatorConnectionFlowException "Throws on connection errors"
                operatorRequestService -> operatorFailedFlowException "Throws on flow failures"

                operatorTotalNetworkOperatorStatistics -> operatorWalletStatisticsResult "Contains wallet statistics"
                operatorPortalNetworkOperatorStatistics -> operatorTotalNetworkOperatorStatistics "Contributes to total statistics"
                operatorNodeNetworkOperatorStatistic -> operatorTotalNetworkOperatorStatistics "Contributes to total statistics"

                operatorSecurityConfiguration -> operatorNetworkOperatorController "Secures endpoints"


            }
            corda5Bastion = container "Corda5 Bastion" "Blockchain service for managing investment agreements and smart contracts" "Kotlin, Corda 5" "Blockchain Node" {
                // Flows (Business Logic)
                corda5ProposeAgreementFlow = component "Propose Agreement Flow" "Initiates new investment agreement proposals between parties" "Corda Flow"
                corda5SignAgreementFlow = component "Sign Agreement Flow" "Handles agreement signing by avatar parties" "Corda Flow"
                corda5VerifyAgreementFlow = component "Verify Agreement Flow" "Verifies agreement terms and compliance" "Corda Flow"
                corda5NotarizeAgreementFlow = component "Notarize Agreement Flow" "Notarizes verified agreements on the blockchain" "Corda Flow"
                corda5DeclineAgreementFlow = component "Decline Agreement Flow" "Handles agreement rejection with reasons" "Corda Flow"
                corda5CompleteAgreementFlow = component "Complete Agreement Flow" "Finalizes completed or declined agreements" "Corda Flow"
                corda5ListAgreementsFlow = component "List Agreements Flow" "Retrieves agreement lists for parties" "Corda Flow"

                // States (Data Models)
                corda5AgreementState = component "Agreement State" "Represents investment agreement data on the blockchain" "Corda State"
                corda5AgreementStateDto = component "Agreement State DTO" "Data transfer object for agreement state serialization" "Kotlin Data Class"

                // Contracts (Validation Logic)
                corda5ProposeAgreementContract = component "Propose Agreement Contract" "Validates agreement transactions and state transitions" "Corda Contract"

                // Schema (Database Mapping)
                corda5AgreementSchema = component "Agreement Schema" "Database schema mapping for agreement persistence" "Corda Schema"

                // Status Management
                corda5Status = component "Status Enum" "Agreement status enumeration (PROPOSED, SIGNED, VERIFIED, etc.)" "Kotlin Enum"

                // Annexes (Supporting Documents)
                corda5Annex1 = component "Annex 1" "Supporting documentation and terms for agreements" "Corda Annex"

                // Utilities
                corda5CordaNotification = component "Corda Notification" "Notification system for agreement events" "Kotlin Utility"
                corda5SendNotification = component "Send Notification Utility" "Utility for sending notifications to external systems" "Kotlin Utility"

                // System Flows (Corda Framework)
                corda5CollectSignaturesFlow = component "Collect Signatures Flow" "Corda system flow for collecting party signatures" "Corda System Flow"
                corda5FinalityFlow = component "Finality Flow" "Corda system flow for transaction finalization" "Corda System Flow"
                corda5ReceiveFinalityFlow = component "Receive Finality Flow" "Corda system flow for receiving finalized transactions" "Corda System Flow"
                corda5SignTransactionFlow = component "Sign Transaction Flow" "Corda system flow for transaction signing" "Corda System Flow"

                // Component Relationships
                corda5ProposeAgreementFlow -> corda5AgreementState "Creates new agreement state"
                corda5ProposeAgreementFlow -> corda5ProposeAgreementContract "Uses for validation"
                corda5ProposeAgreementFlow -> corda5Status "Sets initial status"
                corda5ProposeAgreementFlow -> corda5CollectSignaturesFlow "Uses for signature collection"
                corda5ProposeAgreementFlow -> corda5FinalityFlow "Uses for transaction finalization"

                corda5SignAgreementFlow -> corda5AgreementState "Updates agreement state"
                corda5SignAgreementFlow -> corda5ProposeAgreementContract "Uses for validation"
                corda5SignAgreementFlow -> corda5Status "Updates to SIGNED status"
                corda5SignAgreementFlow -> corda5CollectSignaturesFlow "Uses for signature collection"
                corda5SignAgreementFlow -> corda5FinalityFlow "Uses for transaction finalization"

                corda5VerifyAgreementFlow -> corda5AgreementState "Updates agreement state"
                corda5VerifyAgreementFlow -> corda5ProposeAgreementContract "Uses for validation"
                corda5VerifyAgreementFlow -> corda5Status "Updates to VERIFIED status"
                corda5VerifyAgreementFlow -> corda5Annex1 "References supporting documents"
                corda5VerifyAgreementFlow -> corda5CordaNotification "Sends verification notifications"

                corda5NotarizeAgreementFlow -> corda5AgreementState "Updates agreement state"
                corda5NotarizeAgreementFlow -> corda5ProposeAgreementContract "Uses for validation"
                corda5NotarizeAgreementFlow -> corda5Status "Updates to NOTARIZED status"
                corda5NotarizeAgreementFlow -> corda5CollectSignaturesFlow "Uses for signature collection"
                corda5NotarizeAgreementFlow -> corda5FinalityFlow "Uses for transaction finalization"

                corda5DeclineAgreementFlow -> corda5AgreementState "Updates agreement state"
                corda5DeclineAgreementFlow -> corda5ProposeAgreementContract "Uses for validation"
                corda5DeclineAgreementFlow -> corda5Status "Updates to DECLINED status"
                corda5DeclineAgreementFlow -> corda5CollectSignaturesFlow "Uses for signature collection"
                corda5DeclineAgreementFlow -> corda5FinalityFlow "Uses for transaction finalization"

                corda5CompleteAgreementFlow -> corda5AgreementState "Consumes final agreement state"
                corda5CompleteAgreementFlow -> corda5ProposeAgreementContract "Uses for validation"
                corda5CompleteAgreementFlow -> corda5Status "Validates final status"
                corda5CompleteAgreementFlow -> corda5FinalityFlow "Uses for transaction finalization"

                corda5ListAgreementsFlow -> corda5AgreementState "Queries agreement states"
                corda5ListAgreementsFlow -> corda5AgreementStateDto "Returns serialized data"

                corda5AgreementState -> corda5ProposeAgreementContract "Belongs to contract"
                corda5AgreementState -> corda5AgreementSchema "Uses for persistence"
                corda5AgreementState -> corda5Status "Contains status information"
                corda5AgreementState -> corda5AgreementStateDto "Converts to DTO"

                corda5ProposeAgreementContract -> corda5Status "Validates status transitions"
                corda5ProposeAgreementContract -> corda5AgreementState "Validates state data"

                corda5CordaNotification -> corda5SendNotification "Uses for notification delivery"


            }

            // Data Stores
            authDatabase = container "Auth Database" "Stores user authentication data, roles, and avatar information" "PostgreSQL" "Database"
            userDatabase = container "User Database" "Stores user profiles, agreements, and avatar relationships" "PostgreSQL" "Database"
            vaultDatabase = container "Vault Database" "Stores encrypted MPC provider credentials and vault metadata" "PostgreSQL" "Database"
            storeDatabase = container "Store Database" "Stores product catalog and auction data" "PostgreSQL" "Database"
            operatorDatabase = container "Operator Database" "Stores system statistics and monitoring data" "PostgreSQL" "Database"
            redisCache = container "Redis Cache" "Provides caching, session storage, and Celery task queue" "Redis" "Cache"

            // Frontend Server
            frontendServer = container "Frontend Server" "Serves static frontend assets and provides API routing" "Node.js, Express" "Web Server"
        }

        // Cross-Container Component Relationships
        // Note: Most cross-container relationships are handled at the container level
        // to avoid duplicate relationship errors in Structurizr DSL

        // Matrix Server Integration (external system)
        corda5SendNotification -> matrixServer "Sends notifications via Matrix protocol"

        // Context Level Relationships
        investor -> polityPlatform "Uses platform to invest in digital assets through avatars"
        adviser -> polityPlatform "Creates and manages investment agreements for clients"
        networkOperator -> polityPlatform "Monitors system health and manages platform operations"
        avatar -> polityPlatform "Executes investment transactions on behalf of investors"

        polityPlatform -> dfnsProvider "Creates and manages MPC wallets, executes transactions"
        polityPlatform -> safeheronProvider "Creates and manages MPC wallets, executes transactions"
        polityPlatform -> fractalId "Performs KYC verification for user onboarding"
        polityPlatform -> hashicorpVault "Stores and retrieves MPC provider credentials securely"
        polityPlatform -> blockchainNetworks "Executes blockchain transactions for asset transfers"
        polityPlatform -> matrixServer "Provides real-time communication for avatars"

        // Container Level Relationships - Users to Frontend
        investor -> authPortal "Registers, logs in, and manages avatars"
        adviser -> authPortal "Logs in and accesses platform"
        networkOperator -> authPortal "Logs in to access operator dashboard"
        avatar -> authPortal "Logs in with limited permissions"

        adviser -> userCabinet "Manages agreements, avatars, and wallet operations"
        avatar -> userCabinet "Views agreements, manages wallet, and participates in auctions"
        networkOperator -> operatorDashboard "Monitors system statistics and health"

        // Frontend to Backend Service Relationships
        authPortal -> authGateway "Authenticates users, manages avatars, integrates with FractalID" "HTTPS/REST"
        authPortal -> userInfrastructure "Creates avatars and manages user relationships" "HTTPS/REST"

        userCabinet -> authGateway "Refreshes authentication tokens" "HTTPS/REST"
        userCabinet -> userInfrastructure "Manages agreements, avatars, and user data" "HTTPS/REST"
        userCabinet -> walletInfrastructure "Manages wallets and executes transactions" "HTTPS/REST"
        userCabinet -> storeAuction "Browses products and participates in auctions" "HTTPS/REST, WebSocket"

        operatorDashboard -> operatorService "Retrieves system statistics and monitoring data" "HTTPS/REST"

        frontendServer -> authGateway "Routes API requests for authentication" "HTTP"
        frontendServer -> userInfrastructure "Routes API requests for user operations" "HTTP"
        frontendServer -> walletInfrastructure "Routes API requests for wallet operations" "HTTP"
        frontendServer -> storeAuction "Routes API requests for store and auction operations" "HTTP"

        // Backend Service Relationships
        authGateway -> authDatabase "Stores and retrieves user authentication data" "JDBC"
        authGateway -> redisCache "Manages user sessions and caching" "Redis Protocol"
        authGateway -> fractalId "Performs KYC verification" "HTTPS/REST"

        userInfrastructure -> userDatabase "Stores and retrieves user and agreement data" "JDBC"
        userInfrastructure -> corda5Bastion "Creates and manages blockchain agreements" "HTTPS/REST"
        userInfrastructure -> authGateway "Notifies of avatar acquisitions" "HTTPS/REST"

        walletInfrastructure -> polityVault "Retrieves MPC provider credentials" "HTTPS/REST"
        walletInfrastructure -> dfnsProvider "Creates wallets and executes transactions" "HTTPS/REST"
        walletInfrastructure -> safeheronProvider "Creates wallets and executes transactions" "HTTPS/REST"
        walletInfrastructure -> blockchainNetworks "Executes blockchain transactions" "Blockchain Protocol"

        polityVault -> vaultDatabase "Stores encrypted credential metadata" "Django ORM"
        polityVault -> hashicorpVault "Stores and retrieves MPC provider credentials" "HTTPS/REST"
        polityVault -> redisCache "Caches credentials and manages Celery tasks" "Redis Protocol"

        storeAuction -> storeDatabase "Stores product and auction data" "Database Connection"
        storeAuction -> redisCache "Manages real-time auction state" "Redis Protocol"

        operatorService -> operatorDatabase "Stores system statistics" "JDBC"
        operatorService -> authGateway "Retrieves user statistics" "HTTPS/REST"
        operatorService -> userInfrastructure "Retrieves agreement statistics" "HTTPS/REST"
        operatorService -> walletInfrastructure "Retrieves wallet statistics" "HTTPS/REST"
        operatorService -> corda5Bastion "Retrieves blockchain statistics" "HTTPS/REST"

        corda5Bastion -> matrixServer "Provides communication for avatar interactions" "Matrix Protocol"

        // Code-level diagrams (Level 4 in C4 model) - Auth Gateway Service
        authGateway {
            // Auth Controller Code Level
            authGatewayAuthController {
                authControllerAuthenticateMethod = element "authenticate()" "Handles user login authentication" "Method"
                authControllerRegisterMethod = element "register()" "Handles user registration" "Method"
                authControllerRefreshTokenMethod = element "refreshToken()" "Handles JWT token refresh" "Method"

                authControllerAuthenticateMethod -> authGatewayAuthenticationService "calls authenticate()"
                authControllerRegisterMethod -> authGatewayAuthenticationService "calls register()"
                authControllerRefreshTokenMethod -> authGatewayAuthenticationService "calls refreshToken()"
                authControllerAuthenticateMethod -> authGatewayCustomerRouteService "calls findByUsernameAndFetchRoutesEagerly()"
                authControllerRegisterMethod -> authGatewayCustomerRouteService "calls save() and update()"
            }

            // Adviser Controller Code Level
            authGatewayAdviserController {
        adviserControllerGetAdviserListMethod = element "getAdviserList()" "Handles avatar node acquisition for advisers" "Method"

        adviserControllerGetAdviserListMethod -> authGatewayJwtService "calls getClaims()"
        adviserControllerGetAdviserListMethod -> authGatewayCustomerRouteService "calls findByUuid(), getByUsername(), update(), entityToAvatarDto()"
    }

    // Investor Avatar Controller Code Level
    authGatewayInvestorAvatarController {
        investorAvatarControllerLoginMethod = element "login()" "Handles avatar login authentication" "Method"
        investorAvatarControllerRegisterMethod = element "register()" "Handles avatar registration" "Method"
        investorAvatarControllerGetAvatarsMethod = element "getAvatars()" "Retrieves avatars for investor" "Method"
        investorAvatarControllerRenameAvatarMethod = element "renameAvatar()" "Handles avatar renaming" "Method"
        investorAvatarControllerTurnOnCoSignerMethod = element "turnOnCoSigner()" "Activates co-signer for avatar" "Method"

        investorAvatarControllerLoginMethod -> authGatewayAuthenticationService "calls authenticate()"
        investorAvatarControllerRegisterMethod -> authGatewayAuthenticationService "calls register()"
        investorAvatarControllerGetAvatarsMethod -> authGatewayCustomerRouteService "calls findAllByCustomerIdAndRole()"
        investorAvatarControllerRenameAvatarMethod -> authGatewayCustomerRouteService "calls update()"
        investorAvatarControllerTurnOnCoSignerMethod -> authGatewayTurnOnCoSignerService "calls turnOnCoSigner()"
        investorAvatarControllerLoginMethod -> authGatewayJwtService "calls getClaims()"
        investorAvatarControllerRegisterMethod -> authGatewayJwtService "calls getClaims()"
        investorAvatarControllerGetAvatarsMethod -> authGatewayJwtService "calls getClaims()"
        investorAvatarControllerRenameAvatarMethod -> authGatewayJwtService "calls getClaims()"
        investorAvatarControllerTurnOnCoSignerMethod -> authGatewayJwtService "calls getClaims()"
    }

    // Investor Adviser Controller Code Level
    authGatewayInvestorAdviserController {
        investorAdviserControllerGetAdviserListMethod = element "getAdviserList()" "Retrieves list of advisers with optional search" "Method"
        investorAdviserControllerGetAvatarMethod = element "getAvatar()" "Retrieves specific adviser by ID" "Method"

        investorAdviserControllerGetAdviserListMethod -> authGatewayCustomerRouteService "calls findCustomerRoutesByRoleContainingName(), findCustomerRoutesByRole(), entityToAdviserDto()"
        investorAdviserControllerGetAvatarMethod -> authGatewayCustomerRouteService "calls getCustomerRouteById(), entityToAdviserDto()"
    }

    // Investor Controller Code Level
    authGatewayInvestorController {
        investorControllerSendSlackNotificationMethod = element "sendSlackNotificationAvatarConnectWithAdviser()" "Sends Slack notification when avatar connects with adviser" "Method"

        investorControllerSendSlackNotificationMethod -> authGatewayJwtService "calls getClaims()"
        investorControllerSendSlackNotificationMethod -> authGatewayCustomerRouteService "calls findByUuid(), getCustomerRouteById()"
        investorControllerSendSlackNotificationMethod -> authGatewaySendSlackNotificationUseCaseService "calls sendSlackBusinessNotification()"
    }

    // FractalID Auth Controller Code Level
    authGatewayFractalIdAuthController {
        fractalIdAuthControllerRegisterMethod = element "registerUser()" "Handles FractalID user registration" "Method"
        fractalIdAuthControllerLoginMethod = element "authenticateUser()" "Handles FractalID user authentication" "Method"
        fractalIdAuthControllerRefreshTokenMethod = element "refreshToken()" "Handles JWT token refresh" "Method"
        fractalIdAuthControllerLoginInvestorMethod = element "loginInvestor()" "Handles investor login with FractalID token" "Method"
        fractalIdAuthControllerLoginMethod2 = element "login()" "Handles basic login for operators and advisers" "Method"

        fractalIdAuthControllerRegisterMethod -> authGatewayAuthenticationService "calls register()"
        fractalIdAuthControllerLoginMethod -> authGatewayAuthenticationService "calls authenticate()"
        fractalIdAuthControllerRefreshTokenMethod -> authGatewayAuthenticationService "calls refreshToken()"
        fractalIdAuthControllerLoginInvestorMethod -> authGatewayAuthenticationService "calls authenticate()"
        fractalIdAuthControllerLoginMethod2 -> authGatewayAuthenticationService "calls authenticate()"
        fractalIdAuthControllerRegisterMethod -> authGatewayCustomerRouteService "calls save(), update()"
        fractalIdAuthControllerLoginInvestorMethod -> authGatewayCustomerRouteService "calls findByUsernameAndFetchRoutesEagerly()"
        fractalIdAuthControllerLoginMethod2 -> authGatewayCustomerRouteService "calls findByUsernameAndFetchRoutesEagerly()"
    }

    // Code to UUID Controller Code Level
    authGatewayCodeToUUIDController {
        codeToUUIDControllerSetMethod = element "set()" "Generates new UUID code" "Method"
        codeToUUIDControllerGetMethod = element "get()" "Retrieves all UUID codes" "Method"

        codeToUUIDControllerSetMethod -> authGatewayCodeToUUIDService "calls generateCode()"
        codeToUUIDControllerGetMethod -> authGatewayCodeToUUIDRepository "calls findAll()"
    }

    // Wallet Statistics Controller Code Level
    authGatewayWalletStatisticsController {
        walletStatisticsControllerPostNotificationMethod = element "postNotification()" "Updates wallet statistics" "Method"
        walletStatisticsControllerGetWalletStatisticsMethod = element "getWalletStatistics()" "Retrieves all wallet statistics" "Method"
        walletStatisticsControllerGetAddressesStatisticsByIdMethod = element "getAddressesStatisticsById()" "Retrieves wallet statistics by type" "Method"

        walletStatisticsControllerPostNotificationMethod -> authGatewayWalletStatisticsService "calls updateWalletStatistics()"
        walletStatisticsControllerGetWalletStatisticsMethod -> authGatewayWalletStatisticsService "calls getAllWalletStatistics()"
        walletStatisticsControllerGetAddressesStatisticsByIdMethod -> authGatewayWalletStatisticsService "calls getWalletStatistics()"
    }

    // Network Operator Controller Code Level
    authGatewayNetworkOperatorController {
        networkOperatorControllerGetStatisticsMethod = element "getStatistics()" "Retrieves network operator statistics" "Method"

        networkOperatorControllerGetStatisticsMethod -> authGatewayCustomerRouteService "calls countCustomersByRole()"
        networkOperatorControllerGetStatisticsMethod -> authGatewayJwtService "calls getClaims()"
    }

    // Email Controller Code Level
    authGatewayEmailController {
        emailControllerSendEmailMethod = element "sendEmail()" "Sends email using SendGrid" "Method"

        emailControllerSendEmailMethod -> authGatewayEmailService "calls sendEmail()"
    }

    // Slack Notification Controller Code Level
    authGatewaySlackNotificationController {
        slackNotificationControllerSendSlackNotificationWalletCreatedMethod = element "sendSlackNotificationWalletCreated()" "Sends Slack notification for wallet creation" "Method"

        slackNotificationControllerSendSlackNotificationWalletCreatedMethod -> authGatewayJwtService "calls getClaims()"
        slackNotificationControllerSendSlackNotificationWalletCreatedMethod -> authGatewayCustomerRouteService "calls findByUuid()"
        slackNotificationControllerSendSlackNotificationWalletCreatedMethod -> authGatewaySendSlackNotificationUseCaseService "calls sendSlackTechNotification()"
    }

    // Healthcheck Controller Code Level
    authGatewayHealthcheckController {
        healthcheckControllerHealthcheckMethod = element "healthcheck()" "Returns HTTP 200 OK status" "Method"
    }

    // Authentication Service Code Level
    authGatewayAuthenticationService {
        authServiceAuthenticateMethod = element "authenticate()" "Authenticates user and generates JWT tokens" "Method"
        authServiceRegisterMethod = element "register()" "Registers new user and generates JWT tokens" "Method"
        authServiceRefreshTokenMethod = element "refreshToken()" "Refreshes JWT token using refresh token" "Method"
        authServiceRevokeAllUserTokensMethod = element "revokeAllUserTokens()" "Revokes all user tokens" "Method"
        authServiceSaveUserTokenMethod = element "saveUserToken()" "Saves JWT token to database" "Method"

        authServiceAuthenticateMethod -> authGatewayAuthenticationManager "uses for authentication"
        authServiceAuthenticateMethod -> authGatewayJwtService "calls generateToken(), generateRefreshToken()"
        authServiceRegisterMethod -> authGatewayUserRepository "calls save()"
        authServiceRegisterMethod -> authGatewayJwtService "calls generateToken(), generateRefreshToken()"
        authServiceRefreshTokenMethod -> authGatewayTokenRepository "calls findByToken()"
        authServiceRefreshTokenMethod -> authGatewayJwtService "calls getUserName(), isTokenExpired()"
        authServiceRevokeAllUserTokensMethod -> authGatewayTokenRepository "calls findAllValidTokensByUserId(), saveAll()"
        authServiceSaveUserTokenMethod -> authGatewayTokenRepository "calls save()"
    }

    // JWT Service Code Level
    authGatewayJwtService {
        jwtServiceGetUserNameMethod = element "getUserName()" "Extracts username from JWT token" "Method"
        jwtServiceGetExpirationMethod = element "getExpiration()" "Extracts expiration date from JWT token" "Method"
        jwtServiceGetClaimsMethod = element "getClaims()" "Extracts all claims from JWT token" "Method"
        jwtServiceIsTokenValidMethod = element "isTokenValid()" "Validates JWT token against user details" "Method"
        jwtServiceIsTokenExpiredMethod = element "isTokenExpired()" "Checks if JWT token is expired" "Method"
        jwtServiceGenerateTokenMethod = element "generateToken()" "Generates JWT access token" "Method"
        jwtServiceGenerateRefreshTokenMethod = element "generateRefreshToken()" "Generates JWT refresh token" "Method"
        jwtServiceBuildTokenMethod = element "buildToken()" "Builds JWT token with claims and expiration" "Method"

        jwtServiceGetUserNameMethod -> jwtServiceGetClaimsMethod "calls to extract subject"
        jwtServiceGetExpirationMethod -> jwtServiceGetClaimsMethod "calls to extract expiration"
        jwtServiceIsTokenValidMethod -> jwtServiceGetUserNameMethod "calls to get username"
        jwtServiceIsTokenValidMethod -> jwtServiceIsTokenExpiredMethod "calls to check expiration"
        jwtServiceGenerateTokenMethod -> jwtServiceBuildTokenMethod "calls with JWT_EXPIRATION"
        jwtServiceGenerateRefreshTokenMethod -> jwtServiceBuildTokenMethod "calls with REFRESH_EXPIRATION"
    }

    // Customer Route Service Code Level
    authGatewayCustomerRouteService {
        customerRouteServiceFindAllByCustomerIdAndRoleMethod = element "findAllByCustomerIdAndRole()" "Finds customer routes by customer ID and role" "Method"
        customerRouteServiceFindCustomerRoutesByRoleMethod = element "findCustomerRoutesByRole()" "Finds customer routes by role" "Method"
        customerRouteServiceFindByUsernameAndFetchRoutesEagerlyMethod = element "findByUsernameAndFetchRoutesEagerly()" "Finds customer route by username" "Method"
        customerRouteServiceIsExistsByUsernameMethod = element "isExistsByUsername()" "Checks if username exists" "Method"
        customerRouteServiceSaveMethod = element "save()" "Saves new customer route" "Method"
        customerRouteServiceUpdateMethod = element "update()" "Updates existing customer route" "Method"
        customerRouteServiceEntityToAdviserDtoMethod = element "entityToAdviserDto()" "Converts entity to adviser DTO" "Method"
        customerRouteServiceEntityToAvatarDtoMethod = element "entityToAvatarDto()" "Converts entity to avatar DTO" "Method"
        customerRouteServiceFindByUuidMethod = element "findByUuid()" "Finds customer route by UUID" "Method"
        customerRouteServiceGetByUsernameMethod = element "getByUsername()" "Gets customer route by username" "Method"
        customerRouteServiceCountCustomersByRoleMethod = element "countCustomersByRole()" "Counts customers by role for statistics" "Method"

        customerRouteServiceFindAllByCustomerIdAndRoleMethod -> authGatewayCustomerRouteRepository "calls findAllByCustomerIdAndRole()"
        customerRouteServiceFindCustomerRoutesByRoleMethod -> authGatewayCustomerRouteRepository "calls findByRole()"
        customerRouteServiceFindByUsernameAndFetchRoutesEagerlyMethod -> authGatewayCustomerRouteRepository "calls findByUsername()"
        customerRouteServiceIsExistsByUsernameMethod -> authGatewayCustomerRouteRepository "calls existsCustomerRouteByUsername()"
        customerRouteServiceSaveMethod -> authGatewayCustomerRouteRepository "calls save()"
        customerRouteServiceUpdateMethod -> authGatewayCustomerRouteRepository "calls save()"
        customerRouteServiceFindByUuidMethod -> authGatewayCustomerRouteRepository "calls findByUuid()"
        customerRouteServiceGetByUsernameMethod -> authGatewayCustomerRouteRepository "calls findByUsername()"
        customerRouteServiceCountCustomersByRoleMethod -> authGatewayCustomerRouteRepository "calls countByRole()"
    }

    // Code to UUID Service Code Level
    authGatewayCodeToUUIDService {
        codeToUUIDServiceGenerateCodeMethod = element "generateCode()" "Generates random 4-digit code and saves to database" "Method"

        codeToUUIDServiceGenerateCodeMethod -> authGatewayCodeToUUIDRepository "calls save()"
    }

    // Turn On CoSigner Service Code Level
    authGatewayTurnOnCoSignerService {
        turnOnCoSignerServiceTurnOnCoSignerMethod = element "turnOnCoSigner()" "Activates co-signer for avatar wallet" "Method"

        turnOnCoSignerServiceTurnOnCoSignerMethod -> polityVault "makes HTTP request to activate co-signer"
    }

    // User Repository Code Level
    authGatewayUserRepository {
        userRepositoryFindByUsernameMethod = element "findByUsername()" "Finds user entity by username" "JPA Method"
        userRepositoryExistsByUsernameMethod = element "existsByUsername()" "Checks if user exists by username" "JPA Method"
        userRepositorySaveMethod = element "save()" "Saves user entity" "JPA Method"
        userRepositoryFindByIdMethod = element "findById()" "Finds user entity by ID" "JPA Method"

        userRepositoryFindByUsernameMethod -> authGatewayPostgreSQLDB "queries users table"
        userRepositoryExistsByUsernameMethod -> authGatewayPostgreSQLDB "queries users table"
        userRepositorySaveMethod -> authGatewayPostgreSQLDB "inserts/updates users table"
        userRepositoryFindByIdMethod -> authGatewayPostgreSQLDB "queries users table"
    }

    // Token Repository Code Level
    authGatewayTokenRepository {
        tokenRepositoryFindAllValidTokensByUserIdMethod = element "findAllValidTokensByUserId()" "Finds all valid tokens for user" "JPA Method"
        tokenRepositoryFindByTokenMethod = element "findByToken()" "Finds token entity by token string" "JPA Method"
        tokenRepositorySaveMethod = element "save()" "Saves token entity" "JPA Method"
        tokenRepositorySaveAllMethod = element "saveAll()" "Saves multiple token entities" "JPA Method"

        tokenRepositoryFindAllValidTokensByUserIdMethod -> authGatewayPostgreSQLDB "queries tokens table with join"
        tokenRepositoryFindByTokenMethod -> authGatewayPostgreSQLDB "queries tokens table"
        tokenRepositorySaveMethod -> authGatewayPostgreSQLDB "inserts/updates tokens table"
        tokenRepositorySaveAllMethod -> authGatewayPostgreSQLDB "batch inserts/updates tokens table"
    }

    // Customer Route Repository Code Level
    authGatewayCustomerRouteRepository {
        customerRouteRepositoryFindAllByCustomerIdAndRoleMethod = element "findAllByCustomerIdAndRole()" "Finds customer routes by customer ID and role" "JPA Method"
        customerRouteRepositoryFindByRoleMethod = element "findByRole()" "Finds customer routes by role" "JPA Method"
        customerRouteRepositoryFindByUsernameMethod = element "findByUsername()" "Finds customer route by username" "JPA Method"
        customerRouteRepositoryExistsCustomerRouteByUsernameMethod = element "existsCustomerRouteByUsername()" "Checks if customer route exists by username" "JPA Method"
        customerRouteRepositoryExistsCustomerRouteByElementIdMethod = element "existsCustomerRouteByElementId()" "Checks if customer route exists by element ID" "JPA Method"
        customerRouteRepositorySaveMethod = element "save()" "Saves customer route entity" "JPA Method"
        customerRouteRepositoryFindByUuidMethod = element "findByUuid()" "Finds customer route by UUID" "JPA Method"
        customerRouteRepositoryCountByRoleMethod = element "countByRole()" "Counts customer routes by role" "JPA Method"

        customerRouteRepositoryFindAllByCustomerIdAndRoleMethod -> authGatewayPostgreSQLDB "queries customer_routes table"
        customerRouteRepositoryFindByRoleMethod -> authGatewayPostgreSQLDB "queries customer_routes table"
        customerRouteRepositoryFindByUsernameMethod -> authGatewayPostgreSQLDB "queries customer_routes table"
        customerRouteRepositoryExistsCustomerRouteByUsernameMethod -> authGatewayPostgreSQLDB "queries customer_routes table"
        customerRouteRepositoryExistsCustomerRouteByElementIdMethod -> authGatewayPostgreSQLDB "queries customer_routes table"
        customerRouteRepositorySaveMethod -> authGatewayPostgreSQLDB "inserts/updates customer_routes table"
        customerRouteRepositoryFindByUuidMethod -> authGatewayPostgreSQLDB "queries customer_routes table"
        customerRouteRepositoryCountByRoleMethod -> authGatewayPostgreSQLDB "queries customer_routes table"
    }

    // Code to UUID Repository Code Level
    authGatewayCodeToUUIDRepository {
        codeToUUIDRepositoryFindAllMethod = element "findAll()" "Finds all code to UUID mappings" "JPA Method"
        codeToUUIDRepositorySaveMethod = element "save()" "Saves code to UUID mapping" "JPA Method"

        codeToUUIDRepositoryFindAllMethod -> authGatewayPostgreSQLDB "queries code_to_uuid table"
        codeToUUIDRepositorySaveMethod -> authGatewayPostgreSQLDB "inserts/updates code_to_uuid table"
    }

    // JWT Authentication Filter Code Level
    authGatewayJwtAuthenticationFilter {
        jwtFilterDoFilterInternalMethod = element "doFilterInternal()" "Filters HTTP requests and validates JWT tokens" "Method"
        jwtFilterExtractJwtMethod = element "extractJwt()" "Extracts JWT token from Authorization header" "Method"
        jwtFilterValidateTokenMethod = element "validateToken()" "Validates JWT token and sets authentication context" "Method"

        jwtFilterDoFilterInternalMethod -> jwtFilterExtractJwtMethod "calls to extract token"
        jwtFilterDoFilterInternalMethod -> jwtFilterValidateTokenMethod "calls to validate token"
        jwtFilterValidateTokenMethod -> authGatewayJwtService "calls getUserName(), isTokenValid()"
        jwtFilterValidateTokenMethod -> authGatewayTokenRepository "calls findByToken()"
        jwtFilterValidateTokenMethod -> authGatewayUserDetailsService "calls loadUserByUsername()"
    }

    // Security Configuration Code Level
    authGatewaySecurityConfiguration {
        securityConfigFilterChainMethod = element "filterChain()" "Configures Spring Security filter chain" "Method"
        securityConfigCorsConfigurationMethod = element "corsConfiguration()" "Configures CORS settings" "Method"
        securityConfigAuthorizationMethod = element "authorization()" "Configures endpoint authorization rules" "Method"

        securityConfigFilterChainMethod -> securityConfigCorsConfigurationMethod "calls for CORS setup"
        securityConfigFilterChainMethod -> securityConfigAuthorizationMethod "calls for authorization setup"
        securityConfigFilterChainMethod -> authGatewayJwtAuthenticationFilter "adds to filter chain"
        securityConfigFilterChainMethod -> authGatewayAuthenticationProvider "sets authentication provider"
    }

    // Application Configuration Code Level
    authGatewayApplicationConfig {
        appConfigLogFilterMethod = element "logFilter()" "Creates request logging filter" "Method"
        appConfigUserDetailsServiceMethod = element "userDetailsService()" "Creates UserDetailsService bean" "Method"
        appConfigAuthenticationProviderMethod = element "authenticationProvider()" "Creates authentication provider bean" "Method"
        appConfigPasswordEncoderMethod = element "passwordEncoder()" "Creates password encoder bean" "Method"
        appConfigAuthenticationManagerMethod = element "authenticationManager()" "Creates authentication manager bean" "Method"

        appConfigUserDetailsServiceMethod -> authGatewayUserRepository "calls findByUsername()"
            appConfigAuthenticationProviderMethod -> appConfigUserDetailsServiceMethod "uses UserDetailsService"
            appConfigAuthenticationProviderMethod -> appConfigPasswordEncoderMethod "uses PasswordEncoder"
            appConfigAuthenticationManagerMethod -> appConfigAuthenticationProviderMethod "uses AuthenticationProvider"
        }

        // Code-level diagrams (Level 4 in C4 model) - User Infrastructure Service
        userInfrastructure {
            // Adviser Controller Code Level
            userInfraAdviserController {
        adviserControllerProposeAgreementMethod = element "proposeAgreement()" "Handles agreement proposal with file upload" "Method"
        adviserControllerGetAvatarsMethod = element "getAvatars()" "Retrieves all avatars for adviser" "Method"
        adviserControllerListAgreementsMethod = element "listAgreements()" "Lists agreements for specific avatar or all" "Method"
        adviserControllerGetAgreementMethod = element "getAgreement()" "Retrieves specific agreement by ID and type" "Method"

        adviserControllerProposeAgreementMethod -> userInfraJwtService "calls getAllClaimsFromToken()"
        adviserControllerProposeAgreementMethod -> userInfraAvatarService "calls getAvatarByName(), save()"
        adviserControllerProposeAgreementMethod -> userInfraAgreementService "calls proposeAgreementToBlockchain(), proposeAgreementToDB()"
        adviserControllerProposeAgreementMethod -> userInfraFileService "calls uploadFile()"
        adviserControllerProposeAgreementMethod -> userInfraNodeService "calls getNode()"
        adviserControllerProposeAgreementMethod -> userInfraPortalService "calls notifyOfAcquisition()"
        adviserControllerProposeAgreementMethod -> userInfraNotificationService "calls createNotification()"
        adviserControllerProposeAgreementMethod -> userInfraKeyExchangeService "calls verifySignature()"
        adviserControllerGetAvatarsMethod -> userInfraJwtService "calls getProfileNameFromToken()"
        adviserControllerGetAvatarsMethod -> userInfraAvatarService "calls getAvatars()"
        adviserControllerListAgreementsMethod -> userInfraJwtService "calls getProfileNameFromToken()"
        adviserControllerListAgreementsMethod -> userInfraAvatarService "calls getAvatarByName()"
        adviserControllerListAgreementsMethod -> userInfraAgreementService "calls listAgreements()"
        adviserControllerGetAgreementMethod -> userInfraJwtService "calls getProfileNameFromToken()"
        adviserControllerGetAgreementMethod -> userInfraAvatarService "calls getAvatarByName()"
        adviserControllerGetAgreementMethod -> userInfraAgreementService "calls getAgreement()"
    }

    // Avatar Controller Code Level
    userInfraAvatarController {
        avatarControllerDeclineAgreementMethod = element "declineAgreement()" "Handles agreement decline by avatar" "Method"
        avatarControllerAutoSignAgreementMethod = element "autoSignAgreement()" "Handles automatic agreement signing" "Method"
        avatarControllerAcceptAgreementMethod = element "acceptAgreement()" "Handles agreement acceptance by avatar" "Method"
        avatarControllerListAgreementsMethod = element "listAgreements()" "Lists agreements for avatar" "Method"
        avatarControllerGetAgreementMethod = element "getAgreement()" "Retrieves specific agreement for avatar" "Method"

        avatarControllerDeclineAgreementMethod -> userInfraJwtService "calls getProfileNameFromToken()"
        avatarControllerDeclineAgreementMethod -> userInfraAvatarService "calls getAvatarByName()"
        avatarControllerDeclineAgreementMethod -> userInfraAgreementService "calls declineAgreement(), completeAgreement()"
        avatarControllerDeclineAgreementMethod -> userInfraKeyExchangeService "calls verifySignature()"
        avatarControllerAutoSignAgreementMethod -> userInfraJwtService "calls getAllClaimsFromToken()"
        avatarControllerAutoSignAgreementMethod -> userInfraAvatarService "calls getAvatarByName()"
        avatarControllerAutoSignAgreementMethod -> userInfraAgreementService "calls acceptAgreement(), verifyAgreement(), notarizeAgreement(), completeAgreement()"
        avatarControllerAutoSignAgreementMethod -> userInfraNotificationService "calls createNotification()"
        avatarControllerAcceptAgreementMethod -> userInfraJwtService "calls getAllClaimsFromToken()"
        avatarControllerAcceptAgreementMethod -> userInfraAvatarService "calls getAvatarByName()"
        avatarControllerAcceptAgreementMethod -> userInfraAgreementService "calls acceptAgreement(), verifyAgreement(), notarizeAgreement(), completeAgreement()"
        avatarControllerAcceptAgreementMethod -> userInfraKeyExchangeService "calls verifySignature()"
        avatarControllerListAgreementsMethod -> userInfraJwtService "calls getProfileNameFromToken()"
        avatarControllerListAgreementsMethod -> userInfraAvatarService "calls getAvatarByName()"
        avatarControllerListAgreementsMethod -> userInfraAgreementService "calls listAgreements()"
        avatarControllerGetAgreementMethod -> userInfraJwtService "calls getProfileNameFromToken()"
        avatarControllerGetAgreementMethod -> userInfraAvatarService "calls getAvatarByName()"
        avatarControllerGetAgreementMethod -> userInfraAgreementService "calls getAgreement()"
    }

    // Auction Controller Code Level (Deprecated)
    userInfraAuctionController {
        auctionControllerProposeAgreementMethod = element "proposeAgreement()" "Deprecated method for auction agreement proposal" "Method"

        auctionControllerProposeAgreementMethod -> userInfraJwtService "calls getAllClaimsFromToken()"
        auctionControllerProposeAgreementMethod -> userInfraAvatarService "calls getAvatarByName(), save()"
        auctionControllerProposeAgreementMethod -> userInfraAgreementService "calls proposeAgreementToBlockchain(), proposeAgreementToDB()"
        auctionControllerProposeAgreementMethod -> userInfraFileService "calls uploadFile()"
        auctionControllerProposeAgreementMethod -> userInfraNodeService "calls getNode()"
        auctionControllerProposeAgreementMethod -> userInfraPortalService "calls notifyOfAcquisition()"
        auctionControllerProposeAgreementMethod -> userInfraNotificationService "calls createNotification()"
        auctionControllerProposeAgreementMethod -> userInfraKeyExchangeService "calls verifySignature()"
    }

    // Key Exchange Controller Code Level
    userInfraKeyExchangeController {
        keyExchangeControllerSavePublicKeyMethod = element "savePublicKey()" "Saves user's public key for cryptographic operations" "Method"

        keyExchangeControllerSavePublicKeyMethod -> userInfraJwtService "calls getAllClaimsFromToken()"
        keyExchangeControllerSavePublicKeyMethod -> userInfraKeyExchangeService "calls savePublicKey()"
    }

    // Notification Controller Code Level
    userInfraNotificationController {
        notificationControllerPostNotificationMethod = element "postNotification()" "Handles webhook notifications from external systems" "Method"
        notificationControllerReadNotificationsMethod = element "readNotifications()" "Marks notifications as read" "Method"
        notificationControllerGetNotificationsByCustomerMethod = element "getNotificationsByCustomer()" "Retrieves notifications for customer with filters" "Method"
        notificationControllerGetNotificationByIdMethod = element "getNotificationById()" "Retrieves specific notification by ID" "Method"

        notificationControllerPostNotificationMethod -> userInfraNotificationService "calls createNotification()"
        notificationControllerReadNotificationsMethod -> userInfraJwtService "calls getAllClaimsFromToken()"
        notificationControllerReadNotificationsMethod -> userInfraNotificationService "calls readNotification()"
        notificationControllerGetNotificationsByCustomerMethod -> userInfraJwtService "calls getAllClaimsFromToken()"
        notificationControllerGetNotificationsByCustomerMethod -> userInfraNotificationService "calls getNotificationsByCustomer()"
        notificationControllerGetNotificationByIdMethod -> userInfraNotificationService "calls getNotificationById()"
    }

    // Healthcheck Controller Code Level
    userInfraHealthcheckController {
        healthcheckControllerHealthcheckMethod = element "healthcheck()" "Returns HTTP 200 OK status" "Method"
    }

    // Agreement Service Code Level
    userInfraAgreementService {
        agreementServiceListAgreementsMethod = element "listAgreements()" "Lists agreements from database and blockchain" "Method"
        agreementServiceGetAgreementsMethod = element "getAgreements()" "Gets all agreements from database" "Method"
        agreementServiceGetAgreementByAvatarMethod = element "getAgreementByAvatar()" "Gets agreements for specific avatar" "Method"
        agreementServiceProposeAgreementToBlockchainMethod = element "proposeAgreementToBlockchain()" "Proposes agreement to Corda blockchain" "Method"
        agreementServiceAcceptAgreementMethod = element "acceptAgreement()" "Accepts agreement on blockchain" "Method"
        agreementServiceVerifyAgreementMethod = element "verifyAgreement()" "Verifies agreement on blockchain" "Method"
        agreementServiceNotarizeAgreementMethod = element "notarizeAgreement()" "Notarizes agreement on blockchain" "Method"
        agreementServiceCompleteAgreementMethod = element "completeAgreement()" "Completes agreement on blockchain" "Method"
        agreementServiceDeclineAgreementMethod = element "declineAgreement()" "Declines agreement on blockchain" "Method"
        agreementServiceEntityToDtoMethod = element "entityToDto()" "Converts agreement entity to DTO" "Method"
        agreementServiceProposeAgreementToDBMethod = element "proposeAgreementToDB()" "Saves agreement to database" "Method"
        agreementServiceGetAgreementsFromBlockchainMethod = element "getAgreementsFromBlockchain()" "Retrieves agreements from Corda blockchain" "Method"

        agreementServiceGetAgreementsMethod -> userInfraAgreementRepository "calls findAll()"
        agreementServiceGetAgreementByAvatarMethod -> userInfraAgreementRepository "calls findByAvatar()"
        agreementServiceProposeAgreementToDBMethod -> userInfraAgreementRepository "calls save()"
        agreementServiceProposeAgreementToBlockchainMethod -> corda5Bastion "makes HTTP request to propose agreement"
        agreementServiceAcceptAgreementMethod -> corda5Bastion "makes HTTP request to accept agreement"
        agreementServiceVerifyAgreementMethod -> corda5Bastion "makes HTTP request to verify agreement"
        agreementServiceNotarizeAgreementMethod -> corda5Bastion "makes HTTP request to notarize agreement"
        agreementServiceCompleteAgreementMethod -> corda5Bastion "makes HTTP request to complete agreement"
        agreementServiceDeclineAgreementMethod -> corda5Bastion "makes HTTP request to decline agreement"
        agreementServiceGetAgreementsFromBlockchainMethod -> corda5Bastion "makes HTTP request to list agreements"
        agreementServiceListAgreementsMethod -> agreementServiceGetAgreementsMethod "calls to get DB agreements"
        agreementServiceListAgreementsMethod -> agreementServiceGetAgreementsFromBlockchainMethod "calls to get blockchain agreements"
        agreementServiceListAgreementsMethod -> agreementServiceGetAgreementByAvatarMethod "calls for avatar-specific agreements"
    }

    // Avatar Service Code Level
    userInfraAvatarService {
        avatarServiceGetAvatarsMethod = element "getAvatars()" "Retrieves all avatars from database" "Method"
        avatarServiceGetAvatarByNameMethod = element "getAvatarByName()" "Finds avatar by name" "Method"
        avatarServiceSaveMethod = element "save()" "Saves avatar to database" "Method"

        avatarServiceGetAvatarsMethod -> userInfraAvatarRepository "calls findAll()"
        avatarServiceGetAvatarByNameMethod -> userInfraAvatarRepository "calls findByName()"
        avatarServiceSaveMethod -> userInfraAvatarRepository "calls save()"
    }

    // Node Service Code Level
    userInfraNodeService {
        nodeServiceGetNodeMethod = element "getNode()" "Gets available node for avatar assignment" "Method"
        nodeServiceGetNodeByCommonNameMethod = element "getNodeByCommonName()" "Finds node by common name" "Method"

        nodeServiceGetNodeMethod -> userInfraNodeRepository "calls findFirstByAcquiredDateIsNull()"
        nodeServiceGetNodeByCommonNameMethod -> userInfraNodeRepository "calls findByCommonName()"
    }

    // Portal Service Code Level
    userInfraPortalService {
        portalServiceNotifyOfAcquisitionMethod = element "notifyOfAcquisition()" "Notifies portal of avatar node acquisition" "Method"

        portalServiceNotifyOfAcquisitionMethod -> authPortal "makes HTTP request to notify acquisition"
    }

    // JWT Service Code Level
    userInfraJwtService {
        jwtServiceGetAllClaimsFromTokenMethod = element "getAllClaimsFromToken()" "Extracts all claims from JWT token" "Method"
        jwtServiceGetProfileNameFromTokenMethod = element "getProfileNameFromToken()" "Extracts profile name from JWT token" "Method"
        jwtServiceGetUsernameFromTokenMethod = element "getUsernameFromToken()" "Extracts username from JWT token" "Method"
        jwtServiceGetRoleFromTokenMethod = element "getRoleFromToken()" "Extracts role from JWT token" "Method"

        jwtServiceGetProfileNameFromTokenMethod -> jwtServiceGetAllClaimsFromTokenMethod "calls to get all claims"
        jwtServiceGetUsernameFromTokenMethod -> jwtServiceGetAllClaimsFromTokenMethod "calls to get all claims"
        jwtServiceGetRoleFromTokenMethod -> jwtServiceGetAllClaimsFromTokenMethod "calls to get all claims"
    }

    // File Service Code Level
    userInfraFileService {
        fileServiceUploadFileMethod = element "uploadFile()" "Uploads file to storage and returns path" "Method"
        fileServiceDeleteFileMethod = element "deleteFile()" "Deletes file from storage" "Method"
        fileServiceGetFileMethod = element "getFile()" "Retrieves file from storage" "Method"

        fileServiceUploadFileMethod -> userInfraFileStorage "writes file to disk"
        fileServiceDeleteFileMethod -> userInfraFileStorage "deletes file from disk"
        fileServiceGetFileMethod -> userInfraFileStorage "reads file from disk"
    }

    // Key Exchange Service Code Level
    userInfraKeyExchangeService {
        keyExchangeServiceSavePublicKeyMethod = element "savePublicKey()" "Saves user's public key for cryptographic operations" "Method"
        keyExchangeServiceVerifySignatureMethod = element "verifySignature()" "Verifies digital signature using stored public key" "Method"
        keyExchangeServiceGetPublicKeyMethod = element "getPublicKey()" "Retrieves user's public key" "Method"

        keyExchangeServiceSavePublicKeyMethod -> userInfraKeyExchangeRepository "calls save()"
        keyExchangeServiceVerifySignatureMethod -> userInfraKeyExchangeRepository "calls findById()"
        keyExchangeServiceGetPublicKeyMethod -> userInfraKeyExchangeRepository "calls findById()"
    }

    // Notification Service Code Level
    userInfraNotificationService {
        notificationServiceCreateNotificationMethod = element "createNotification()" "Creates new notification in database" "Method"
        notificationServiceReadNotificationMethod = element "readNotification()" "Marks notifications as read" "Method"
        notificationServiceGetNotificationsByCustomerMethod = element "getNotificationsByCustomer()" "Retrieves notifications for customer with filters" "Method"
        notificationServiceGetNotificationByIdMethod = element "getNotificationById()" "Retrieves specific notification by ID" "Method"

        notificationServiceCreateNotificationMethod -> userInfraNotificationRepository "calls save()"
        notificationServiceReadNotificationMethod -> userInfraNotificationRepository "calls findAllById(), saveAll()"
        notificationServiceGetNotificationsByCustomerMethod -> userInfraNotificationRepository "calls findByCustomerUuidAndFilters()"
        notificationServiceGetNotificationByIdMethod -> userInfraNotificationRepository "calls findById()"
    }

    // Agreement Repository Code Level
    userInfraAgreementRepository {
        agreementRepositoryFindAllMethod = element "findAll()" "Finds all agreements" "JPA Method"
        agreementRepositoryFindByAvatarMethod = element "findByAvatar()" "Finds agreements by avatar" "JPA Method"
        agreementRepositorySaveMethod = element "save()" "Saves agreement entity" "JPA Method"
        agreementRepositoryFindByIdMethod = element "findById()" "Finds agreement by ID" "JPA Method"

        agreementRepositoryFindAllMethod -> userInfraPostgreSQLDB "queries agreements table"
        agreementRepositoryFindByAvatarMethod -> userInfraPostgreSQLDB "queries agreements table with avatar join"
        agreementRepositorySaveMethod -> userInfraPostgreSQLDB "inserts/updates agreements table"
        agreementRepositoryFindByIdMethod -> userInfraPostgreSQLDB "queries agreements table"
    }

    // Avatar Repository Code Level
    userInfraAvatarRepository {
        avatarRepositoryFindAllMethod = element "findAll()" "Finds all avatars" "JPA Method"
        avatarRepositoryFindByNameMethod = element "findByName()" "Finds avatar by name" "JPA Method"
        avatarRepositorySaveMethod = element "save()" "Saves avatar entity" "JPA Method"
        avatarRepositoryFindByIdMethod = element "findById()" "Finds avatar by ID" "JPA Method"

        avatarRepositoryFindAllMethod -> userInfraPostgreSQLDB "queries avatars table"
        avatarRepositoryFindByNameMethod -> userInfraPostgreSQLDB "queries avatars table"
        avatarRepositorySaveMethod -> userInfraPostgreSQLDB "inserts/updates avatars table"
        avatarRepositoryFindByIdMethod -> userInfraPostgreSQLDB "queries avatars table"
    }

    // Node Repository Code Level
    userInfraNodeRepository {
        nodeRepositoryFindFirstByAcquiredDateIsNullMethod = element "findFirstByAcquiredDateIsNull()" "Finds first available node" "JPA Method"
        nodeRepositoryFindByCommonNameMethod = element "findByCommonName()" "Finds node by common name" "JPA Method"
        nodeRepositorySaveMethod = element "save()" "Saves node entity" "JPA Method"
        nodeRepositoryFindAllMethod = element "findAll()" "Finds all nodes" "JPA Method"

        nodeRepositoryFindFirstByAcquiredDateIsNullMethod -> userInfraPostgreSQLDB "queries nodes table"
        nodeRepositoryFindByCommonNameMethod -> userInfraPostgreSQLDB "queries nodes table"
        nodeRepositorySaveMethod -> userInfraPostgreSQLDB "inserts/updates nodes table"
        nodeRepositoryFindAllMethod -> userInfraPostgreSQLDB "queries nodes table"
    }

    // Key Exchange Repository Code Level
    userInfraKeyExchangeRepository {
        keyExchangeRepositorySaveMethod = element "save()" "Saves key exchange entity" "JPA Method"
        keyExchangeRepositoryFindByIdMethod = element "findById()" "Finds key exchange by customer ID" "JPA Method"
        keyExchangeRepositoryFindAllMethod = element "findAll()" "Finds all key exchanges" "JPA Method"

        keyExchangeRepositorySaveMethod -> userInfraPostgreSQLDB "inserts/updates key_exchange table"
        keyExchangeRepositoryFindByIdMethod -> userInfraPostgreSQLDB "queries key_exchange table"
        keyExchangeRepositoryFindAllMethod -> userInfraPostgreSQLDB "queries key_exchange table"
    }

    // Notification Repository Code Level
    userInfraNotificationRepository {
        notificationRepositorySaveMethod = element "save()" "Saves notification entity" "JPA Method"
        notificationRepositoryFindAllByIdMethod = element "findAllById()" "Finds notifications by IDs" "JPA Method"
        notificationRepositorySaveAllMethod = element "saveAll()" "Saves multiple notification entities" "JPA Method"
        notificationRepositoryFindByCustomerUuidAndFiltersMethod = element "findByCustomerUuidAndFilters()" "Finds notifications with filters" "JPA Method"
        notificationRepositoryFindByIdMethod = element "findById()" "Finds notification by ID" "JPA Method"

        notificationRepositorySaveMethod -> userInfraPostgreSQLDB "inserts/updates notifications table"
        notificationRepositoryFindAllByIdMethod -> userInfraPostgreSQLDB "queries notifications table"
            notificationRepositorySaveAllMethod -> userInfraPostgreSQLDB "batch inserts/updates notifications table"
            notificationRepositoryFindByCustomerUuidAndFiltersMethod -> userInfraPostgreSQLDB "queries notifications table with filters"
            notificationRepositoryFindByIdMethod -> userInfraPostgreSQLDB "queries notifications table"
        }

        // Code-level diagrams (Level 4 in C4 model) - Wallet Infrastructure Service
        walletInfrastructure {
            // Wallet Controller Code Level
            walletInfraWalletController {
        walletControllerCreateWalletMethod = element "CreateWallet()" "Creates new MPC wallet (DFNS/Safeheron)" "Method"
        walletControllerGetWalletMethod = element "GetWallet()" "Retrieves wallet details by request ID" "Method"
        walletControllerGetWalletsMethod = element "GetWallets()" "Retrieves all wallets for user by MPC type" "Method"
        walletControllerMakePaymentMethod = element "MakePayment()" "Processes payment transaction" "Method"
        walletControllerGetAssetsMethod = element "GetAssets()" "Retrieves wallet assets and balances" "Method"
        walletControllerAddAssetMethod = element "AddAsset()" "Adds new asset to wallet" "Method"
        walletControllerArchiveAssetMethod = element "ArchiveAsset()" "Archives asset from wallet" "Method"
        walletControllerGetAssetAddressesMethod = element "GetAssetAddresses()" "Retrieves addresses for specific asset" "Method"
        walletControllerGetTransactionHistoryMethod = element "GetTransactionHistory()" "Retrieves transaction history for asset" "Method"
        walletControllerProcessPolityWalletStatusChangeMethod = element "ProcessPolityWalletStatusChange()" "Handles wallet status updates from Polity Vault" "Method"
        walletControllerRenameWalletMethod = element "RenameWallet()" "Renames wallet" "Method"
        walletControllerArchiveWalletMethod = element "ArchiveWallet()" "Archives wallet" "Method"
        walletControllerPersonalSignMethod = element "PersonalSign()" "Signs personal message" "Method"
        walletControllerBroadcastTransactionMethod = element "BroadcastTransaction()" "Broadcasts transaction to blockchain" "Method"
        walletControllerScanWalletConnectCallMethod = element "ScanWalletConnectCall()" "Handles WalletConnect requests" "Method"

        walletControllerCreateWalletMethod -> walletInfraWalletRequestsService "calls CreateWalletRequest()"
        walletControllerGetWalletMethod -> walletInfraWalletRequestsService "calls GetPolityWalletForUser()"
        walletControllerGetWalletsMethod -> walletInfraWalletRequestsService "calls GetPolityWalletsByTypeAndUserID()"
        walletControllerMakePaymentMethod -> walletInfraDFNSService "calls MakePayment()"
        walletControllerMakePaymentMethod -> walletInfraSafeheronService "calls MakePayment()"
        walletControllerGetAssetsMethod -> walletInfraDFNSService "calls GetAssets()"
        walletControllerGetAssetsMethod -> walletInfraSafeheronService "calls GetAssets()"
        walletControllerAddAssetMethod -> walletInfraDFNSService "calls AddAsset()"
        walletControllerAddAssetMethod -> walletInfraSafeheronService "calls AddAsset()"
        walletControllerArchiveAssetMethod -> walletInfraDFNSService "calls ArchiveAsset()"
        walletControllerArchiveAssetMethod -> walletInfraSafeheronService "calls ArchiveAsset()"
        walletControllerGetAssetAddressesMethod -> walletInfraDFNSService "calls GetAssetAddresses()"
        walletControllerGetAssetAddressesMethod -> walletInfraSafeheronService "calls GetAssetAddresses()"
        walletControllerGetTransactionHistoryMethod -> walletInfraDFNSService "calls GetAssetTransactions()"
        walletControllerGetTransactionHistoryMethod -> walletInfraSafeheronService "calls GetAssetTransactions()"
        walletControllerProcessPolityWalletStatusChangeMethod -> walletInfraWalletRequestsService "calls UpdatePolityWalletStatus()"
        walletControllerRenameWalletMethod -> walletInfraWalletRequestsService "calls UpdatePolityWalletName()"
        walletControllerArchiveWalletMethod -> walletInfraWalletRequestsService "calls ArchivePolityWallet()"
        walletControllerPersonalSignMethod -> walletInfraDFNSService "calls PersonalSign()"
        walletControllerPersonalSignMethod -> walletInfraSafeheronService "calls PersonalSign()"
        walletControllerBroadcastTransactionMethod -> walletInfraDFNSService "calls BroadcastTransaction()"
        walletControllerBroadcastTransactionMethod -> walletInfraSafeheronService "calls BroadcastTransaction()"
        walletControllerScanWalletConnectCallMethod -> walletInfraWalletConnectService "calls ProcessRequest()"
    }

    // Favorite Address Controller Code Level
    walletInfraFavoriteAddressController {
        favoriteAddressControllerAddAddressMethod = element "AddAddress()" "Adds address to favorites" "Method"
        favoriteAddressControllerRemoveAddressMethod = element "RemoveAddress()" "Removes address from favorites" "Method"
        favoriteAddressControllerGetAddressesMethod = element "GetAddresses()" "Retrieves favorite addresses for user" "Method"
        favoriteAddressControllerAddAdvisorAddressesMethod = element "AddAdvisorAddresses()" "Adds advisor addresses to favorites" "Method"

        favoriteAddressControllerAddAddressMethod -> walletInfraFavoriteAddressService "calls AddAddress()"
        favoriteAddressControllerRemoveAddressMethod -> walletInfraFavoriteAddressService "calls RemoveAddress()"
        favoriteAddressControllerGetAddressesMethod -> walletInfraFavoriteAddressService "calls GetAddressesByUserID()"
        favoriteAddressControllerAddAdvisorAddressesMethod -> walletInfraFavoriteAddressService "calls AddAdvisorAddresses()"
    }

    // DeFi Controller Code Level
    walletInfraDeFiController {
        defiControllerGetProductsMethod = element "GetProducts()" "Retrieves available DeFi products" "Method"
        defiControllerGetAssetsMethod = element "GetAssets()" "Retrieves DeFi asset balances" "Method"
        defiControllerGetLendingMethod = element "GetLending()" "Retrieves DeFi lending positions" "Method"

        defiControllerGetProductsMethod -> walletInfraWalletRequestsService "calls GetDefiProducts()"
        defiControllerGetAssetsMethod -> walletInfraDeFiService "calls GetWalletAssetsBalances()"
        defiControllerGetLendingMethod -> walletInfraDeFiService "calls GetWalletLendingBalance()"
    }

    // Healthcheck Controller Code Level
    walletInfraHealthcheckController {
        healthcheckControllerHealthcheckMethod = element "healthcheck()" "Returns HTTP 200 OK status" "Method"
    }

    // DFNS Service Code Level
    walletInfraDFNSService {
        dfnsServiceInitializeAssetsMethod = element "InitializeAssets()" "Creates default wallet accounts for DFNS" "Method"
        dfnsServiceMakePaymentMethod = element "MakePayment()" "Processes payment through DFNS" "Method"
        dfnsServiceAddAssetMethod = element "AddAsset()" "Adds new asset account to DFNS wallet" "Method"
        dfnsServiceGetAssetsMethod = element "GetAssets()" "Retrieves DFNS wallet assets and balances" "Method"
        dfnsServiceArchiveAssetMethod = element "ArchiveAsset()" "Archives DFNS asset account" "Method"
        dfnsServiceGetPolityWalletMethod = element "GetPolityWallet()" "Retrieves Polity wallet by account" "Method"
        dfnsServiceGetAssetAddressesMethod = element "GetAssetAddresses()" "Retrieves DFNS asset addresses" "Method"
        dfnsServiceBroadcastTransactionMethod = element "BroadcastTransaction()" "Broadcasts transaction via DFNS" "Method"
        dfnsServiceGetAssetTransactionsMethod = element "GetAssetTransactions()" "Retrieves DFNS transaction history" "Method"
        dfnsServicePersonalSignMethod = element "PersonalSign()" "Signs personal message via DFNS" "Method"
        dfnsServiceCreateDefaultWalletAccountsMethod = element "createDefaultWalletAccounts()" "Creates accounts for default networks" "Method"
        dfnsServiceCreateDfnsWalletMethod = element "createDfnsWallet()" "Creates single DFNS wallet account" "Method"

        dfnsServiceInitializeAssetsMethod -> dfnsServiceCreateDefaultWalletAccountsMethod "calls to create default accounts"
        dfnsServiceCreateDefaultWalletAccountsMethod -> dfnsServiceCreateDfnsWalletMethod "calls for each network"
        dfnsServiceMakePaymentMethod -> walletInfraWalletStorage "calls GetPolityWalletID(), GetDFNSWallet()"
        dfnsServiceAddAssetMethod -> dfnsServiceCreateDfnsWalletMethod "calls to create asset account"
        dfnsServiceGetAssetsMethod -> walletInfraWalletStorage "calls GetDFNSWallets()"
        dfnsServiceArchiveAssetMethod -> walletInfraWalletStorage "calls ArchiveDFNSWallet()"
        dfnsServiceGetPolityWalletMethod -> walletInfraWalletStorage "calls GetPolityWallet()"
        dfnsServiceGetAssetAddressesMethod -> walletInfraWalletStorage "calls GetDFNSWallet()"
        dfnsServiceBroadcastTransactionMethod -> walletInfraWalletStorage "calls GetDFNSWallet()"
        dfnsServiceGetAssetTransactionsMethod -> walletInfraWalletStorage "calls GetDFNSWallet()"
        dfnsServicePersonalSignMethod -> walletInfraWalletStorage "calls GetDFNSWallet()"
        dfnsServiceCreateDfnsWalletMethod -> walletInfraWalletStorage "calls CreateDFNSWallet()"
    }

    // Safeheron Service Code Level
    walletInfraSafeheronService {
        safeheronServiceInitializeAssetsMethod = element "InitializeAssets()" "Creates default wallet accounts for Safeheron" "Method"
        safeheronServiceMakePaymentMethod = element "MakePayment()" "Processes payment through Safeheron" "Method"
        safeheronServiceAddAssetMethod = element "AddAsset()" "Adds new asset account to Safeheron wallet" "Method"
        safeheronServiceGetAssetsMethod = element "GetAssets()" "Retrieves Safeheron wallet assets and balances" "Method"
        safeheronServiceArchiveAssetMethod = element "ArchiveAsset()" "Archives Safeheron asset account" "Method"
        safeheronServiceGetPolityWalletMethod = element "GetPolityWallet()" "Retrieves Polity wallet by account" "Method"
        safeheronServiceGetAssetAddressesMethod = element "GetAssetAddresses()" "Retrieves Safeheron asset addresses" "Method"
        safeheronServiceBroadcastTransactionMethod = element "BroadcastTransaction()" "Broadcasts transaction via Safeheron" "Method"
        safeheronServiceGetAssetTransactionsMethod = element "GetAssetTransactions()" "Retrieves Safeheron transaction history" "Method"
        safeheronServicePersonalSignMethod = element "PersonalSign()" "Signs personal message via Safeheron" "Method"
        safeheronServiceCreateDefaultWalletAccountsMethod = element "createDefaultWalletAccounts()" "Creates accounts for default networks" "Method"
        safeheronServiceCreateSafeheronWalletMethod = element "createSafeheronWallet()" "Creates single Safeheron wallet account" "Method"

        safeheronServiceInitializeAssetsMethod -> safeheronServiceCreateDefaultWalletAccountsMethod "calls to create default accounts"
        safeheronServiceCreateDefaultWalletAccountsMethod -> safeheronServiceCreateSafeheronWalletMethod "calls for each network"
        safeheronServiceMakePaymentMethod -> walletInfraWalletStorage "calls GetPolityWalletID(), GetSafeheronWallet()"
        safeheronServiceAddAssetMethod -> safeheronServiceCreateSafeheronWalletMethod "calls to create asset account"
        safeheronServiceGetAssetsMethod -> walletInfraWalletStorage "calls GetSafeheronWallets()"
        safeheronServiceArchiveAssetMethod -> walletInfraWalletStorage "calls ArchiveSafeheronWallet()"
        safeheronServiceGetPolityWalletMethod -> walletInfraWalletStorage "calls GetPolityWallet()"
        safeheronServiceGetAssetAddressesMethod -> walletInfraWalletStorage "calls GetSafeheronWallet()"
        safeheronServiceBroadcastTransactionMethod -> walletInfraWalletStorage "calls GetSafeheronWallet()"
        safeheronServiceGetAssetTransactionsMethod -> walletInfraWalletStorage "calls GetSafeheronWallet()"
        safeheronServicePersonalSignMethod -> walletInfraWalletStorage "calls GetSafeheronWallet()"
        safeheronServiceCreateSafeheronWalletMethod -> walletInfraWalletStorage "calls CreateSafeheronWallet()"
    }

    // Wallet Requests Service Code Level
    walletInfraWalletRequestsService {
        walletRequestsServiceCreatePolityWalletMethod = element "CreatePolityWallet()" "Creates new Polity wallet record" "Method"
        walletRequestsServiceCreateWalletRequestMethod = element "CreateWalletRequest()" "Creates wallet creation request" "Method"
        walletRequestsServiceUpdatePolityWalletStatusMethod = element "UpdatePolityWalletStatus()" "Updates wallet status" "Method"
        walletRequestsServiceUpdatePolityWalletNameMethod = element "UpdatePolityWalletName()" "Updates wallet name" "Method"
        walletRequestsServiceGetPolityWalletMethod = element "GetPolityWallet()" "Retrieves wallet by request ID" "Method"
        walletRequestsServiceGetPolityWalletForUserMethod = element "GetPolityWalletForUser()" "Retrieves wallet for specific user" "Method"
        walletRequestsServiceGetDefiProductsMethod = element "GetDefiProducts()" "Retrieves available DeFi products" "Method"
        walletRequestsServiceGetPolityWalletsForUserMethod = element "GetPolityWalletsForUser()" "Retrieves all wallets for user" "Method"
        walletRequestsServiceGetPolityWalletsByTypeAndUserIDMethod = element "GetPolityWalletsByTypeAndUserID()" "Retrieves wallets by type and user" "Method"
        walletRequestsServiceArchivePolityWalletMethod = element "ArchivePolityWallet()" "Archives wallet" "Method"

        walletRequestsServiceCreatePolityWalletMethod -> walletInfraWalletStorage "calls CreatePolityWallet()"
        walletRequestsServiceCreateWalletRequestMethod -> polityVault "makes HTTP request to create wallet"
        walletRequestsServiceUpdatePolityWalletStatusMethod -> walletInfraWalletStorage "calls UpdatePolityWalletStatus()"
        walletRequestsServiceUpdatePolityWalletNameMethod -> walletInfraWalletStorage "calls UpdatePolityWalletName()"
        walletRequestsServiceGetPolityWalletMethod -> walletInfraWalletStorage "calls GetPolityWallet()"
        walletRequestsServiceGetPolityWalletForUserMethod -> walletInfraWalletStorage "calls GetPolityWalletForUser()"
        walletRequestsServiceGetDefiProductsMethod -> walletInfraWalletStorage "calls GetDefiProducts()"
        walletRequestsServiceGetPolityWalletsForUserMethod -> walletInfraWalletStorage "calls GetPolityWalletsForUser()"
        walletRequestsServiceGetPolityWalletsByTypeAndUserIDMethod -> walletInfraWalletStorage "calls GetPolityWalletsByTypeAndUserID()"
        walletRequestsServiceArchivePolityWalletMethod -> walletInfraWalletStorage "calls ArchivePolityWallet()"
    }

    // DFNS Callback Service Code Level
    walletInfraDFNSCallbackService {
        dfnsCallbackServiceProcessNotificationMethod = element "ProcessNotification()" "Processes DFNS webhook notifications" "Method"
        dfnsCallbackServiceCreateWebhookMethod = element "CreateWebhook()" "Creates DFNS webhook subscription" "Method"
        dfnsCallbackServiceGetListWebhooksMethod = element "GetListWebhooks()" "Retrieves list of DFNS webhooks" "Method"
        dfnsCallbackServiceDeleteWebhookMethod = element "DeleteWebhook()" "Deletes DFNS webhook subscription" "Method"

        dfnsCallbackServiceProcessNotificationMethod -> userInfrastructure "makes HTTP request to send notification"
        dfnsCallbackServiceCreateWebhookMethod -> walletInfraDFNSAPI "makes HTTP request to create webhook"
        dfnsCallbackServiceGetListWebhooksMethod -> walletInfraDFNSAPI "makes HTTP request to list webhooks"
        dfnsCallbackServiceDeleteWebhookMethod -> walletInfraDFNSAPI "makes HTTP request to delete webhook"
    }

    // Safeheron Callback Service Code Level
    walletInfraSafeheronCallbackService {
        safeheronCallbackServiceResolveApprovalTaskMethod = element "ResolveApprovalTask()" "Resolves Safeheron approval tasks" "Method"
        safeheronCallbackServiceProcessNotificationMethod = element "ProcessNotification()" "Processes Safeheron webhook notifications" "Method"

        safeheronCallbackServiceResolveApprovalTaskMethod -> walletInfraSafeheronAPI "makes HTTP request to resolve approval"
        safeheronCallbackServiceProcessNotificationMethod -> userInfrastructure "makes HTTP request to send notification"
    }

    // Favorite Address Service Code Level
    walletInfraFavoriteAddressService {
        favoriteAddressServiceAddAddressMethod = element "AddAddress()" "Adds address to favorites" "Method"
        favoriteAddressServiceAddAdvisorAddressesMethod = element "AddAdvisorAddresses()" "Adds advisor addresses to favorites" "Method"
        favoriteAddressServiceRemoveAddressMethod = element "RemoveAddress()" "Removes address from favorites" "Method"
        favoriteAddressServiceGetAddressesByUserIDMethod = element "GetAddressesByUserID()" "Retrieves favorite addresses for user" "Method"

        favoriteAddressServiceAddAddressMethod -> walletInfraWalletStorage "calls AddFavoriteAddress()"
        favoriteAddressServiceAddAdvisorAddressesMethod -> walletInfraWalletStorage "calls AddAdvisorAddresses()"
        favoriteAddressServiceRemoveAddressMethod -> walletInfraWalletStorage "calls RemoveFavoriteAddress()"
        favoriteAddressServiceGetAddressesByUserIDMethod -> walletInfraWalletStorage "calls GetFavoriteAddressesByUserID()"
    }

    // DeFi Service Code Level
    walletInfraDeFiService {
        defiServiceGetWalletAssetsBalancesMethod = element "GetWalletAssetsBalances()" "Retrieves DeFi asset balances for wallet" "Method"
        defiServiceGetWalletAssetBalanceMethod = element "GetWalletAssetBalance()" "Retrieves single DeFi asset balance" "Method"
        defiServiceGetWalletDepositsBalanceMethod = element "GetWalletDepositsBalance()" "Retrieves DeFi deposits balance" "Method"
        defiServiceGetWalletLendingBalanceMethod = element "GetWalletLendingBalance()" "Retrieves DeFi lending balance" "Method"

        defiServiceGetWalletAssetsBalancesMethod -> walletInfraDeFiAPI "makes GraphQL request to DeFi provider"
        defiServiceGetWalletAssetBalanceMethod -> walletInfraDeFiAPI "makes GraphQL request to DeFi provider"
        defiServiceGetWalletDepositsBalanceMethod -> walletInfraDeFiAPI "makes GraphQL request to DeFi provider"
        defiServiceGetWalletLendingBalanceMethod -> walletInfraDeFiAPI "makes GraphQL request to DeFi provider"
    }

    // WalletConnect Service Code Level
    walletInfraWalletConnectService {
        walletConnectServiceProcessRequestMethod = element "ProcessRequest()" "Processes WalletConnect requests" "Method"
        walletConnectServiceHandleSessionRequestMethod = element "HandleSessionRequest()" "Handles WalletConnect session requests" "Method"
        walletConnectServiceHandleTransactionRequestMethod = element "HandleTransactionRequest()" "Handles WalletConnect transaction requests" "Method"

        walletConnectServiceProcessRequestMethod -> walletConnectServiceHandleSessionRequestMethod "calls for session requests"
        walletConnectServiceProcessRequestMethod -> walletConnectServiceHandleTransactionRequestMethod "calls for transaction requests"
        walletConnectServiceHandleTransactionRequestMethod -> walletInfraDFNSService "calls MakePayment()"
        walletConnectServiceHandleTransactionRequestMethod -> walletInfraSafeheronService "calls MakePayment()"
    }

    // Wallet Storage Code Level
    walletInfraWalletStorage {
        walletStorageCreatePolityWalletMethod = element "CreatePolityWallet()" "Creates Polity wallet record in database" "Method"
        walletStorageGetPolityWalletMethod = element "GetPolityWallet()" "Retrieves Polity wallet by request ID" "Method"
        walletStorageGetPolityWalletForUserMethod = element "GetPolityWalletForUser()" "Retrieves Polity wallet for specific user" "Method"
        walletStorageGetPolityWalletsForUserMethod = element "GetPolityWalletsForUser()" "Retrieves all Polity wallets for user" "Method"
        walletStorageGetPolityWalletsByTypeAndUserIDMethod = element "GetPolityWalletsByTypeAndUserID()" "Retrieves Polity wallets by type and user" "Method"
        walletStorageUpdatePolityWalletStatusMethod = element "UpdatePolityWalletStatus()" "Updates Polity wallet status" "Method"
        walletStorageUpdatePolityWalletNameMethod = element "UpdatePolityWalletName()" "Updates Polity wallet name" "Method"
        walletStorageArchivePolityWalletMethod = element "ArchivePolityWallet()" "Archives Polity wallet" "Method"
        walletStorageGetPolityWalletIDMethod = element "GetPolityWalletID()" "Gets Polity wallet ID by address" "Method"
        walletStorageCreateDFNSWalletMethod = element "CreateDFNSWallet()" "Creates DFNS wallet record" "Method"
        walletStorageGetDFNSWalletMethod = element "GetDFNSWallet()" "Retrieves DFNS wallet by ID and network" "Method"
        walletStorageGetDFNSWalletsMethod = element "GetDFNSWallets()" "Retrieves all DFNS wallets for Polity wallet" "Method"
        walletStorageArchiveDFNSWalletMethod = element "ArchiveDFNSWallet()" "Archives DFNS wallet" "Method"
        walletStorageCreateSafeheronWalletMethod = element "CreateSafeheronWallet()" "Creates Safeheron wallet record" "Method"
        walletStorageGetSafeheronWalletMethod = element "GetSafeheronWallet()" "Retrieves Safeheron wallet by ID and network" "Method"
        walletStorageGetSafeheronWalletsMethod = element "GetSafeheronWallets()" "Retrieves all Safeheron wallets for Polity wallet" "Method"
        walletStorageArchiveSafeheronWalletMethod = element "ArchiveSafeheronWallet()" "Archives Safeheron wallet" "Method"
        walletStorageAddFavoriteAddressMethod = element "AddFavoriteAddress()" "Adds favorite address to database" "Method"
        walletStorageRemoveFavoriteAddressMethod = element "RemoveFavoriteAddress()" "Removes favorite address from database" "Method"
        walletStorageGetFavoriteAddressesByUserIDMethod = element "GetFavoriteAddressesByUserID()" "Retrieves favorite addresses for user" "Method"
        walletStorageAddAdvisorAddressesMethod = element "AddAdvisorAddresses()" "Adds advisor addresses to favorites" "Method"
        walletStorageGetDefiProductsMethod = element "GetDefiProducts()" "Retrieves DeFi products from database" "Method"

        walletStorageCreatePolityWalletMethod -> walletInfraPostgreSQLDB "inserts into polity_wallets table"
        walletStorageGetPolityWalletMethod -> walletInfraPostgreSQLDB "queries polity_wallets table"
        walletStorageGetPolityWalletForUserMethod -> walletInfraPostgreSQLDB "queries polity_wallets table with user filter"
        walletStorageGetPolityWalletsForUserMethod -> walletInfraPostgreSQLDB "queries polity_wallets table with user filter"
        walletStorageGetPolityWalletsByTypeAndUserIDMethod -> walletInfraPostgreSQLDB "queries polity_wallets table with type and user filter"
        walletStorageUpdatePolityWalletStatusMethod -> walletInfraPostgreSQLDB "updates polity_wallets table"
        walletStorageUpdatePolityWalletNameMethod -> walletInfraPostgreSQLDB "updates polity_wallets table"
        walletStorageArchivePolityWalletMethod -> walletInfraPostgreSQLDB "updates polity_wallets table"
        walletStorageGetPolityWalletIDMethod -> walletInfraPostgreSQLDB "queries polity_wallets table"
        walletStorageCreateDFNSWalletMethod -> walletInfraPostgreSQLDB "inserts into dfns_wallets table"
        walletStorageGetDFNSWalletMethod -> walletInfraPostgreSQLDB "queries dfns_wallets table"
        walletStorageGetDFNSWalletsMethod -> walletInfraPostgreSQLDB "queries dfns_wallets table"
        walletStorageArchiveDFNSWalletMethod -> walletInfraPostgreSQLDB "updates dfns_wallets table"
        walletStorageCreateSafeheronWalletMethod -> walletInfraPostgreSQLDB "inserts into safeheron_wallets table"
        walletStorageGetSafeheronWalletMethod -> walletInfraPostgreSQLDB "queries safeheron_wallets table"
        walletStorageGetSafeheronWalletsMethod -> walletInfraPostgreSQLDB "queries safeheron_wallets table"
        walletStorageArchiveSafeheronWalletMethod -> walletInfraPostgreSQLDB "updates safeheron_wallets table"
        walletStorageAddFavoriteAddressMethod -> walletInfraPostgreSQLDB "inserts into favorite_addresses table"
        walletStorageRemoveFavoriteAddressMethod -> walletInfraPostgreSQLDB "deletes from favorite_addresses table"
            walletStorageGetFavoriteAddressesByUserIDMethod -> walletInfraPostgreSQLDB "queries favorite_addresses table"
            walletStorageAddAdvisorAddressesMethod -> walletInfraPostgreSQLDB "batch inserts into favorite_addresses table"
            walletStorageGetDefiProductsMethod -> walletInfraPostgreSQLDB "queries defi_products table"
        }

        // Code-level diagrams (Level 4 in C4 model) - Polity Vault Service
        polityVault {
            // Wallet Creation Request List View Code Level
            polityVaultWalletCreationRequestListView {
        walletCreationRequestListViewGetMethod = element "get()" "Retrieves wallet creation requests for user" "Django View Method"
        walletCreationRequestListViewPostMethod = element "post()" "Creates new wallet creation request" "Django View Method"
        walletCreationRequestListViewGetObjectsMethod = element "get_objects()" "Helper method to get user's wallet creation requests" "Django View Method"

        walletCreationRequestListViewGetMethod -> walletCreationRequestListViewGetObjectsMethod "calls to get user requests"
        walletCreationRequestListViewGetObjectsMethod -> polityVaultWalletCreationRequestModel "calls objects.filter()"
        walletCreationRequestListViewPostMethod -> polityVaultWalletCreationRequestModel "calls save() via serializer"
    }

    // Wallet Creation Request Details View Code Level
    polityVaultWalletCreationRequestDetailsView {
        walletCreationRequestDetailsViewGetMethod = element "get()" "Retrieves specific wallet creation request" "Django View Method"
        walletCreationRequestDetailsViewPutMethod = element "put()" "Updates wallet creation request status" "Django View Method"
        walletCreationRequestDetailsViewDeleteMethod = element "delete()" "Deletes wallet creation request" "Django View Method"
        walletCreationRequestDetailsViewGetObjectMethod = element "get_object()" "Helper method to get specific request" "Django View Method"

        walletCreationRequestDetailsViewGetMethod -> walletCreationRequestDetailsViewGetObjectMethod "calls to get request"
        walletCreationRequestDetailsViewPutMethod -> walletCreationRequestDetailsViewGetObjectMethod "calls to get request"
        walletCreationRequestDetailsViewDeleteMethod -> walletCreationRequestDetailsViewGetObjectMethod "calls to get request"
        walletCreationRequestDetailsViewGetObjectMethod -> polityVaultWalletCreationRequestModel "calls objects.get()"
        walletCreationRequestDetailsViewPutMethod -> polityVaultWalletCreationRequestModel "calls save() via serializer"
        walletCreationRequestDetailsViewDeleteMethod -> polityVaultWalletCreationRequestModel "calls delete()"
    }

    // DFNS Wallet Info List View Code Level
    polityVaultDFNSWalletInfoListView {
        dfnsWalletInfoListViewGetMethod = element "get()" "Retrieves DFNS wallet info for user" "Django View Method"
        dfnsWalletInfoListViewGetObjectsMethod = element "get_objects()" "Helper method to get user's DFNS wallet info" "Django View Method"

        dfnsWalletInfoListViewGetMethod -> dfnsWalletInfoListViewGetObjectsMethod "calls to get user wallet info"
        dfnsWalletInfoListViewGetObjectsMethod -> polityVaultDFNSWalletInfoModel "calls objects.select_related().filter()"
    }

    // DFNS Wallet Info Details View Code Level
    polityVaultDFNSWalletInfoDetailsView {
        dfnsWalletInfoDetailsViewGetMethod = element "get()" "Retrieves DFNS wallet credentials for external access" "Django View Method"

        dfnsWalletInfoDetailsViewGetMethod -> polityVaultDFNSWalletInfoModel "calls objects.select_related().filter()"
    }

    // Safeheron Wallet Info List View Code Level
    polityVaultSafeheronWalletInfoListView {
        safeheronWalletInfoListViewGetMethod = element "get()" "Retrieves Safeheron wallet info for user" "Django View Method"
        safeheronWalletInfoListViewGetObjectsMethod = element "get_objects()" "Helper method to get user's Safeheron wallet info" "Django View Method"

        safeheronWalletInfoListViewGetMethod -> safeheronWalletInfoListViewGetObjectsMethod "calls to get user wallet info"
        safeheronWalletInfoListViewGetObjectsMethod -> polityVaultSafeheronWalletInfoModel "calls objects.select_related().filter()"
    }

    // Safeheron Wallet Info Details View Code Level
    polityVaultSafeheronWalletInfoDetailsView {
        safeheronWalletInfoDetailsViewGetMethod = element "get()" "Retrieves Safeheron wallet credentials by cosigner UUID" "Django View Method"
        safeheronWalletInfoDetailsViewGetObjectMethod = element "get_object()" "Helper method to get specific Safeheron wallet info" "Django View Method"

        safeheronWalletInfoDetailsViewGetMethod -> safeheronWalletInfoDetailsViewGetObjectMethod "calls to get wallet info"
        safeheronWalletInfoDetailsViewGetObjectMethod -> polityVaultSafeheronWalletInfoModel "calls objects.select_related().get()"
    }

    // CoSigner Control View Code Level
    polityVaultCoSignerControlView {
        coSignerControlViewPostMethod = element "post()" "Triggers cosigner provisioning for Safeheron wallet" "Django View Method"

        coSignerControlViewPostMethod -> polityVaultLaunchCosignerTask "calls launch_cosigner.delay()"
    }

    // Wallet Creation Request Model Code Level
    polityVaultWalletCreationRequestModel {
        walletCreationRequestModelSaveMethod = element "save()" "Saves wallet creation request to database" "Django Model Method"
        walletCreationRequestModelDeleteMethod = element "delete()" "Deletes wallet creation request from database" "Django Model Method"
        walletCreationRequestModelObjectsFilterMethod = element "objects.filter()" "Filters wallet creation requests" "Django ORM Method"
        walletCreationRequestModelObjectsGetMethod = element "objects.get()" "Gets specific wallet creation request" "Django ORM Method"
        walletCreationRequestModelStrMethod = element "__str__()" "String representation of wallet creation request" "Django Model Method"

        walletCreationRequestModelSaveMethod -> polityVaultPostgreSQLDB "inserts/updates wallet_credentials_walletcreationrequest table"
        walletCreationRequestModelDeleteMethod -> polityVaultPostgreSQLDB "deletes from wallet_credentials_walletcreationrequest table"
        walletCreationRequestModelObjectsFilterMethod -> polityVaultPostgreSQLDB "queries wallet_credentials_walletcreationrequest table"
        walletCreationRequestModelObjectsGetMethod -> polityVaultPostgreSQLDB "queries wallet_credentials_walletcreationrequest table"
    }

    // DFNS Wallet Info Model Code Level
    polityVaultDFNSWalletInfoModel {
        dfnsWalletInfoModelSecretProperty = element "secret" "Property to access DFNS credentials from HashiCorp Vault" "Django Model Property"
        dfnsWalletInfoModelJwtTokenProperty = element "jwt_token" "Property to access DFNS JWT token" "Django Model Property"
        dfnsWalletInfoModelPrivateKeyProperty = element "private_key" "Property to access DFNS private key" "Django Model Property"
        dfnsWalletInfoModelSaveToVaultMethod = element "save_to_vault()" "Saves DFNS credentials to HashiCorp Vault" "Django Model Method"
        dfnsWalletInfoModelGetFromVaultMethod = element "get_from_vault()" "Retrieves DFNS credentials from HashiCorp Vault" "Django Model Method"
        dfnsWalletInfoModelSecretToJsonMethod = element "secret_to_json()" "Converts secret to JSON format" "Django Model Method"
        dfnsWalletInfoModelObjectsSelectRelatedFilterMethod = element "objects.select_related().filter()" "Queries DFNS wallet info with related objects" "Django ORM Method"

        dfnsWalletInfoModelSecretProperty -> dfnsWalletInfoModelGetFromVaultMethod "calls when secret not cached"
        dfnsWalletInfoModelJwtTokenProperty -> dfnsWalletInfoModelSecretProperty "accesses secret to get JWT token"
        dfnsWalletInfoModelPrivateKeyProperty -> dfnsWalletInfoModelSecretProperty "accesses secret to get private key"
        dfnsWalletInfoModelSaveToVaultMethod -> hashiCorpVault "stores credentials in Vault"
        dfnsWalletInfoModelGetFromVaultMethod -> hashiCorpVault "retrieves credentials from Vault"
        dfnsWalletInfoModelObjectsSelectRelatedFilterMethod -> polityVaultPostgreSQLDB "queries wallet_credentials_dfnswalletinfo table with joins"
    }

    // Safeheron Wallet Info Model Code Level
    polityVaultSafeheronWalletInfoModel {
        safeheronWalletInfoModelSecretProperty = element "secret" "Property to access Safeheron credentials from HashiCorp Vault" "Django Model Property"
        safeheronWalletInfoModelPublicKeyProperty = element "public_key" "Property to access Safeheron public key" "Django Model Property"
        safeheronWalletInfoModelPrivateKeyProperty = element "private_key" "Property to access Safeheron private key" "Django Model Property"
        safeheronWalletInfoModelApiKeyProperty = element "api_key" "Property to access Safeheron API key" "Django Model Property"
        safeheronWalletInfoModelPlatformPublicKeyProperty = element "platform_public_key" "Property to access Safeheron platform public key" "Django Model Property"
        safeheronWalletInfoModelNotifPlatformPubKeyProperty = element "notif_platform_pub_key" "Property to access Safeheron notification platform public key" "Django Model Property"
        safeheronWalletInfoModelCosignerPublicKeyProperty = element "cosigner_public_key" "Property to access cosigner public key" "Django Model Property"
        safeheronWalletInfoModelCosignerPrivateKeyProperty = element "cosigner_private_key" "Property to access cosigner private key" "Django Model Property"
        safeheronWalletInfoModelCosignerApiKeyProperty = element "cosigner_api_key" "Property to access cosigner API key" "Django Model Property"
        safeheronWalletInfoModelSaveToVaultMethod = element "save_to_vault()" "Saves Safeheron credentials to HashiCorp Vault" "Django Model Method"
        safeheronWalletInfoModelGetFromVaultMethod = element "get_from_vault()" "Retrieves Safeheron credentials from HashiCorp Vault" "Django Model Method"
        safeheronWalletInfoModelSecretToJsonMethod = element "secret_to_json()" "Converts secret to JSON format" "Django Model Method"
        safeheronWalletInfoModelObjectsSelectRelatedGetMethod = element "objects.select_related().get()" "Gets Safeheron wallet info with related objects" "Django ORM Method"
        safeheronWalletInfoModelObjectsSelectRelatedFilterMethod = element "objects.select_related().filter()" "Queries Safeheron wallet info with related objects" "Django ORM Method"

        safeheronWalletInfoModelSecretProperty -> safeheronWalletInfoModelGetFromVaultMethod "calls when secret not cached"
        safeheronWalletInfoModelPublicKeyProperty -> safeheronWalletInfoModelSecretProperty "accesses secret to get public key"
        safeheronWalletInfoModelPrivateKeyProperty -> safeheronWalletInfoModelSecretProperty "accesses secret to get private key"
        safeheronWalletInfoModelApiKeyProperty -> safeheronWalletInfoModelSecretProperty "accesses secret to get API key"
        safeheronWalletInfoModelPlatformPublicKeyProperty -> safeheronWalletInfoModelSecretProperty "accesses secret to get platform public key"
        safeheronWalletInfoModelNotifPlatformPubKeyProperty -> safeheronWalletInfoModelSecretProperty "accesses secret to get notification platform public key"
        safeheronWalletInfoModelCosignerPublicKeyProperty -> safeheronWalletInfoModelSecretProperty "accesses secret to get cosigner public key"
        safeheronWalletInfoModelCosignerPrivateKeyProperty -> safeheronWalletInfoModelSecretProperty "accesses secret to get cosigner private key"
        safeheronWalletInfoModelCosignerApiKeyProperty -> safeheronWalletInfoModelSecretProperty "accesses secret to get cosigner API key"
        safeheronWalletInfoModelSaveToVaultMethod -> hashiCorpVault "stores credentials in Vault"
        safeheronWalletInfoModelGetFromVaultMethod -> hashiCorpVault "retrieves credentials from Vault"
        safeheronWalletInfoModelObjectsSelectRelatedGetMethod -> polityVaultPostgreSQLDB "queries wallet_credentials_safeheronwalletinfo table with joins"
        safeheronWalletInfoModelObjectsSelectRelatedFilterMethod -> polityVaultPostgreSQLDB "queries wallet_credentials_safeheronwalletinfo table with joins"
    }

    // Launch Cosigner Task Code Level
    polityVaultLaunchCosignerTask {
        launchCosignerTaskExecuteMethod = element "launch_cosigner()" "Celery task to provision Safeheron cosigner" "Celery Task Method"

        launchCosignerTaskExecuteMethod -> polityVaultProvisionTask "calls provision.delay()"
    }

    // Provision Task Code Level
    polityVaultProvisionTask {
        provisionTaskExecuteMethod = element "provision()" "Celery task to provision Safeheron cosigner on AWS EC2" "Celery Task Method"
        provisionTaskCreateFileMethod = element "create_file()" "Creates configuration files for cosigner" "Task Helper Method"
        provisionTaskSetupEC2InstanceMethod = element "setup_ec2_instance()" "Sets up EC2 instance for cosigner" "Task Helper Method"
        provisionTaskConfigureSafeheronClientMethod = element "configure_safeheron_client()" "Configures Safeheron MPC client" "Task Helper Method"
        provisionTaskUploadToS3Method = element "upload_to_s3()" "Uploads configuration files to S3" "Task Helper Method"

        provisionTaskExecuteMethod -> provisionTaskCreateFileMethod "calls to create config files"
        provisionTaskExecuteMethod -> provisionTaskSetupEC2InstanceMethod "calls to setup EC2"
        provisionTaskExecuteMethod -> provisionTaskConfigureSafeheronClientMethod "calls to configure MPC client"
        provisionTaskExecuteMethod -> provisionTaskUploadToS3Method "calls to upload files"
        provisionTaskSetupEC2InstanceMethod -> awsEC2 "creates and configures EC2 instance"
        provisionTaskUploadToS3Method -> awsS3 "uploads files to S3 bucket"
        provisionTaskConfigureSafeheronClientMethod -> safeheronAPI "configures MPC client connection"
    }

    // Deprovision Task Code Level
    polityVaultDeprovisionTask {
        deprovisionTaskExecuteMethod = element "deprovision()" "Celery task to deprovision Safeheron cosigner" "Celery Task Method"
        deprovisionTaskTerminateEC2InstanceMethod = element "terminate_ec2_instance()" "Terminates EC2 instance" "Task Helper Method"
        deprovisionTaskCleanupS3FilesMethod = element "cleanup_s3_files()" "Cleans up S3 files" "Task Helper Method"

        deprovisionTaskExecuteMethod -> deprovisionTaskTerminateEC2InstanceMethod "calls to terminate EC2"
        deprovisionTaskExecuteMethod -> deprovisionTaskCleanupS3FilesMethod "calls to cleanup S3"
        deprovisionTaskTerminateEC2InstanceMethod -> awsEC2 "terminates EC2 instance"
        deprovisionTaskCleanupS3FilesMethod -> awsS3 "deletes files from S3 bucket"
    }

    // Cosigner Manager Code Level
    polityVaultCosignerManager {
        cosignerManagerCreateInstanceMethod = element "create_instance()" "Creates new EC2 instance for cosigner" "Manager Method"
        cosignerManagerTerminateInstanceMethod = element "terminate_instance()" "Terminates EC2 instance" "Manager Method"
        cosignerManagerGetInstanceStatusMethod = element "get_instance_status()" "Gets EC2 instance status" "Manager Method"
        cosignerManagerConfigureSecurityGroupMethod = element "configure_security_group()" "Configures EC2 security group" "Manager Method"

        cosignerManagerCreateInstanceMethod -> awsEC2 "creates EC2 instance"
        cosignerManagerTerminateInstanceMethod -> awsEC2 "terminates EC2 instance"
        cosignerManagerGetInstanceStatusMethod -> awsEC2 "queries instance status"
        cosignerManagerConfigureSecurityGroupMethod -> awsEC2 "configures security group"
    }

    // Safeheron Manager Code Level
    polityVaultSafeheronManager {
        safeheronManagerBoto3Property = element "boto3" "AWS Boto3 client wrapper" "Manager Property"
        safeheronManagerBoto3SshProperty = element "boto3_ssh" "SSH client for EC2 instances" "Manager Property"
        safeheronManagerIsS3FolderExistMethod = element "is_s3_folder_exist()" "Checks if S3 folder exists" "Manager Method"
        safeheronManagerDownloadFileS3Method = element "download_file_s3()" "Downloads file from S3" "Manager Method"
        safeheronManagerUploadFileS3Method = element "upload_file_s3()" "Uploads file to S3" "Manager Method"
        safeheronManagerScpSendFileMethod = element "scp_send_file()" "Sends file via SCP to EC2" "Manager Method"
        safeheronManagerScpGetFileMethod = element "scp_get_file()" "Gets file via SCP from EC2" "Manager Method"
        safeheronManagerSshCommandMethod = element "ssh_command()" "Executes SSH command on EC2" "Manager Method"
        safeheronManagerExistsRemoteMethod = element "exists_remote()" "Checks if remote file exists" "Manager Method"

        safeheronManagerIsS3FolderExistMethod -> awsS3 "checks S3 folder existence"
        safeheronManagerDownloadFileS3Method -> awsS3 "downloads file from S3"
        safeheronManagerUploadFileS3Method -> awsS3 "uploads file to S3"
        safeheronManagerScpSendFileMethod -> awsEC2 "sends file to EC2 via SCP"
            safeheronManagerScpGetFileMethod -> awsEC2 "gets file from EC2 via SCP"
            safeheronManagerSshCommandMethod -> awsEC2 "executes command on EC2 via SSH"
            safeheronManagerExistsRemoteMethod -> awsEC2 "checks file existence on EC2"
        }

        // Code-level diagrams (Level 4 in C4 model) - Store & Auction Service
        storeAuction {
            // Auction Controller Code Level
            storeAuctionAuctionController {
        auctionControllerGetProductsMethod = element "GetProducts()" "Retrieves all available auction products" "Controller Method"
        auctionControllerGetTiersByProductMethod = element "GetTiersByProduct()" "Retrieves tiers for specific product" "Controller Method"
        auctionControllerGetTierByIDMethod = element "GetTierByID()" "Retrieves specific tier by ID" "Controller Method"
        auctionControllerGetLotsAddressMethod = element "GetLotsAddress()" "Gets WebSocket address for active lots" "Controller Method"
        auctionControllerMakeBidMethod = element "MakeBid()" "Processes user bid on auction lot" "Controller Method"
        auctionControllerBuyNowMethod = element "BuyNow()" "Processes immediate purchase of tier" "Controller Method"
        auctionControllerGetBidsMethod = element "GetBids()" "Retrieves bid history for lot" "Controller Method"
        auctionControllerCancelWonLotMethod = element "CancelWonLot()" "Cancels won lot by agreement ID" "Controller Method"
        auctionControllerWebSocketHandlerMethod = element "WebSocketHandler()" "Handles WebSocket connections for real-time updates" "Controller Method"
        auctionControllerUpgradeConnectionMethod = element "UpgradeConnection()" "Upgrades HTTP connection to WebSocket" "Controller Method"

        auctionControllerGetProductsMethod -> storeAuctionAuctionService "calls GetProducts()"
        auctionControllerGetTiersByProductMethod -> storeAuctionAuctionService "calls GetTiersByProduct()"
        auctionControllerGetTierByIDMethod -> storeAuctionAuctionService "calls GetTierByID()"
        auctionControllerGetLotsAddressMethod -> storeAuctionAuctionService "calls GetLotsAddress()"
        auctionControllerMakeBidMethod -> storeAuctionAuctionService "calls MakeBid()"
        auctionControllerBuyNowMethod -> storeAuctionAuctionService "calls BuyNow()"
        auctionControllerGetBidsMethod -> storeAuctionAuctionService "calls GetBids()"
        auctionControllerCancelWonLotMethod -> storeAuctionAuctionService "calls CancelWonLot()"
        auctionControllerWebSocketHandlerMethod -> auctionControllerUpgradeConnectionMethod "calls to upgrade connection"
    }

    // Store Controller Code Level
    storeAuctionStoreController {
        storeControllerGetWonLotsMethod = element "GetWonLots()" "Retrieves won lots for user" "Controller Method"
        storeControllerGetPermissionsForWonLotsMethod = element "GetPermissionsForWonLots()" "Retrieves permissions for user's won lots" "Controller Method"

        storeControllerGetWonLotsMethod -> storeAuctionStoreService "calls GetWonLots()"
        storeControllerGetPermissionsForWonLotsMethod -> storeAuctionStoreService "calls GetPermissionsForWonLots()"
    }

    // Auction Service Code Level
    storeAuctionAuctionService {
        auctionServiceRunActiveLotsMethod = element "RunActiveLots()" "Main loop for managing active auction lots" "Service Method"
        auctionServiceGetLotsAddressMethod = element "GetLotsAddress()" "Gets WebSocket address and current lots state" "Service Method"
        auctionServiceGetTiersByProductMethod = element "GetTiersByProduct()" "Retrieves tiers for specific product" "Service Method"
        auctionServiceGetTierByIDMethod = element "GetTierByID()" "Retrieves specific tier by ID" "Service Method"
        auctionServiceBuyNowMethod = element "BuyNow()" "Processes immediate purchase of tier" "Service Method"
        auctionServiceGetTiersMethod = element "GetTiers()" "Retrieves all available tiers" "Service Method"
        auctionServiceSubscribeMethod = element "Subscribe()" "Subscribes to active lot updates for tier" "Service Method"
        auctionServiceSubscribeWonLotsMethod = element "SubscribeWonLots()" "Subscribes to won lot notifications for tier" "Service Method"
        auctionServiceMakeBidMethod = element "MakeBid()" "Processes user bid on auction lot" "Service Method"
        auctionServiceCreateActiveLotMethod = element "CreateActiveLot()" "Creates new active auction lot" "Service Method"
        auctionServiceGetProductsMethod = element "GetProducts()" "Retrieves all available auction products" "Service Method"
        auctionServiceGetBidsMethod = element "GetBids()" "Retrieves bid history for lot" "Service Method"
        auctionServiceCreateAgreementMethod = element "CreateAgreement()" "Creates agreement for won lot" "Service Method"
        auctionServiceCancelWonLotMethod = element "CancelWonLot()" "Cancels won lot by agreement ID" "Service Method"

        auctionServiceGetLotsAddressMethod -> storeAuctionAuctionStorage "calls GetActiveLots()"
        auctionServiceGetTiersByProductMethod -> storeAuctionAuctionStorage "calls GetTiersByProduct()"
        auctionServiceGetTierByIDMethod -> storeAuctionAuctionStorage "calls GetTierByID()"
        auctionServiceBuyNowMethod -> storeAuctionAuctionStorage "calls CreateWonLot()"
        auctionServiceBuyNowMethod -> userInfrastructure "makes HTTP request to create agreement"
        auctionServiceGetTiersMethod -> storeAuctionAuctionStorage "calls GetTiers()"
        auctionServiceMakeBidMethod -> storeAuctionAuctionStorage "calls CreateBid()"
        auctionServiceCreateActiveLotMethod -> storeAuctionAuctionStorage "calls CreateActiveLot()"
        auctionServiceGetProductsMethod -> storeAuctionAuctionStorage "calls GetProducts()"
        auctionServiceGetBidsMethod -> storeAuctionAuctionStorage "calls GetBids()"
        auctionServiceCreateAgreementMethod -> userInfrastructure "makes HTTP request to create agreement"
        auctionServiceCancelWonLotMethod -> storeAuctionAuctionStorage "calls UpdateWonLotStatus()"
    }

    // Store Service Code Level
    storeAuctionStoreService {
        storeServiceGetWonLotsMethod = element "GetWonLots()" "Retrieves won lots for user" "Service Method"
        storeServiceGetPermissionsForWonLotsMethod = element "GetPermissionsForWonLots()" "Retrieves permissions for user's won lots" "Service Method"

        storeServiceGetWonLotsMethod -> storeAuctionStoreStorage "calls GetWonLots()"
        storeServiceGetPermissionsForWonLotsMethod -> storeAuctionStoreStorage "calls GetPermissionsForWonLots()"
    }

    // Bot Service Code Level
    storeAuctionBotService {
        botServiceRunMethod = element "Run()" "Main loop for bot bidding behavior" "Service Method"
        botServiceMakeBidMethod = element "MakeBid()" "Makes automated bid on auction lot" "Service Method"
        botServiceCalculateBidAmountMethod = element "CalculateBidAmount()" "Calculates optimal bid amount" "Service Method"
        botServiceShouldBidMethod = element "ShouldBid()" "Determines if bot should bid on lot" "Service Method"

        botServiceRunMethod -> botServiceShouldBidMethod "calls to check if should bid"
        botServiceRunMethod -> botServiceMakeBidMethod "calls to make bid"
        botServiceMakeBidMethod -> botServiceCalculateBidAmountMethod "calls to calculate bid amount"
        botServiceMakeBidMethod -> storeAuctionAuctionService "calls MakeBid()"
    }

    // Auction Storage Code Level
    storeAuctionAuctionStorage {
        auctionStorageGetProductsMethod = element "GetProducts()" "Retrieves all products from database" "Storage Method"
        auctionStorageGetTiersMethod = element "GetTiers()" "Retrieves all tiers from database" "Storage Method"
        auctionStorageGetTiersByProductMethod = element "GetTiersByProduct()" "Retrieves tiers for specific product" "Storage Method"
        auctionStorageGetTierByIDMethod = element "GetTierByID()" "Retrieves specific tier by ID" "Storage Method"
        auctionStorageCreateActiveLotMethod = element "CreateActiveLot()" "Creates new active lot in database" "Storage Method"
        auctionStorageGetActiveLotMethod = element "GetActiveLot()" "Retrieves active lot by ID" "Storage Method"
        auctionStorageGetActiveLotsMethod = element "GetActiveLots()" "Retrieves all active lots" "Storage Method"
        auctionStorageUpdateActiveLotMethod = element "UpdateActiveLot()" "Updates active lot in database" "Storage Method"
        auctionStorageDeleteActiveLotMethod = element "DeleteActiveLot()" "Deletes active lot from database" "Storage Method"
        auctionStorageCreateWonLotMethod = element "CreateWonLot()" "Creates won lot record in database" "Storage Method"
        auctionStorageGetWonLotMethod = element "GetWonLot()" "Retrieves won lot by ID" "Storage Method"
        auctionStorageUpdateWonLotStatusMethod = element "UpdateWonLotStatus()" "Updates won lot status" "Storage Method"
        auctionStorageUpdateAgreementIDMethod = element "UpdateAgreementID()" "Updates agreement ID for won lot" "Storage Method"
        auctionStorageCreateBidMethod = element "CreateBid()" "Creates bid record in database" "Storage Method"
        auctionStorageGetBidsMethod = element "GetBids()" "Retrieves bids for specific lot" "Storage Method"

        auctionStorageGetProductsMethod -> storeAuctionPostgreSQLDB "queries products table"
        auctionStorageGetTiersMethod -> storeAuctionPostgreSQLDB "queries tiers table"
        auctionStorageGetTiersByProductMethod -> storeAuctionPostgreSQLDB "queries tiers table with product filter"
        auctionStorageGetTierByIDMethod -> storeAuctionPostgreSQLDB "queries tiers table by ID"
        auctionStorageCreateActiveLotMethod -> storeAuctionPostgreSQLDB "inserts into active_lots table"
        auctionStorageGetActiveLotMethod -> storeAuctionPostgreSQLDB "queries active_lots table"
        auctionStorageGetActiveLotsMethod -> storeAuctionPostgreSQLDB "queries active_lots table"
        auctionStorageUpdateActiveLotMethod -> storeAuctionPostgreSQLDB "updates active_lots table"
        auctionStorageDeleteActiveLotMethod -> storeAuctionPostgreSQLDB "deletes from active_lots table"
        auctionStorageCreateWonLotMethod -> storeAuctionPostgreSQLDB "inserts into won_lots table"
        auctionStorageGetWonLotMethod -> storeAuctionPostgreSQLDB "queries won_lots table"
        auctionStorageUpdateWonLotStatusMethod -> storeAuctionPostgreSQLDB "updates won_lots table"
        auctionStorageUpdateAgreementIDMethod -> storeAuctionPostgreSQLDB "updates won_lots table"
        auctionStorageCreateBidMethod -> storeAuctionPostgreSQLDB "inserts into bids table"
        auctionStorageGetBidsMethod -> storeAuctionPostgreSQLDB "queries bids table"
    }

    // Store Storage Code Level
    storeAuctionStoreStorage {
        storeStorageGetWonLotsMethod = element "GetWonLots()" "Retrieves won lots for user from database" "Storage Method"
        storeStorageGetPermissionsForWonLotsMethod = element "GetPermissionsForWonLots()" "Retrieves permissions for user's won lots" "Storage Method"

        storeStorageGetWonLotsMethod -> storeAuctionPostgreSQLDB "queries won_lots table with user filter"
        storeStorageGetPermissionsForWonLotsMethod -> storeAuctionPostgreSQLDB "queries permissions table with joins"
    }

    // Product Model Code Level
    storeAuctionProductModel {
        productModelIDField = element "ID" "Primary key for product" "Model Field"
        productModelCategoryField = element "Category" "Product category (e.g., wallet)" "Model Field"
        productModelMerchantField = element "Merchant" "Product merchant (e.g., safeheron)" "Model Field"
        productModelMinPriceField = element "MinPrice" "Minimum price for product" "Model Field"
        productModelNameField = element "Name" "Product name" "Model Field"
        productModelDescriptionField = element "Description" "Product description" "Model Field"
        productModelCreatedAtField = element "CreatedAt" "Record creation timestamp" "Model Field"
        productModelUpdatedAtField = element "UpdatedAt" "Record update timestamp" "Model Field"
        productModelDeletedAtField = element "DeletedAt" "Soft delete timestamp" "Model Field"
    }

    // Tier Model Code Level
    storeAuctionTierModel {
        tierModelIDField = element "ID" "Primary key for tier" "Model Field"
        tierModelNameField = element "Name" "Tier name" "Model Field"
        tierModelProductQuantityField = element "ProductQuantity" "Quantity of products in tier" "Model Field"
        tierModelBuyNowPriceField = element "BuyNowPrice" "Buy now price for tier" "Model Field"
        tierModelProductIDField = element "ProductID" "Foreign key to product" "Model Field"
        tierModelPermissionIDField = element "PermissionID" "Foreign key to permission" "Model Field"
        tierModelCreatedAtField = element "CreatedAt" "Record creation timestamp" "Model Field"
        tierModelUpdatedAtField = element "UpdatedAt" "Record update timestamp" "Model Field"
        tierModelDeletedAtField = element "DeletedAt" "Soft delete timestamp" "Model Field"

        tierModelProductIDField -> storeAuctionProductModel "references Product"
        tierModelPermissionIDField -> storeAuctionPermissionModel "references Permission"
    }

    // Active Lot Model Code Level
    storeAuctionActiveLotModel {
        activeLotModelIDField = element "ID" "Primary key for active lot" "Model Field"
        activeLotModelPriceField = element "Price" "Current price of active lot" "Model Field"
        activeLotModelUserIDField = element "UserID" "ID of user with winning bid" "Model Field"
        activeLotModelTimeLeftField = element "TimeLeft" "Time remaining for lot" "Model Field"
        activeLotModelTierIDField = element "TierID" "Foreign key to tier" "Model Field"
        activeLotModelCreatedAtField = element "CreatedAt" "Record creation timestamp" "Model Field"
        activeLotModelUpdatedAtField = element "UpdatedAt" "Record update timestamp" "Model Field"
        activeLotModelDeletedAtField = element "DeletedAt" "Soft delete timestamp" "Model Field"

        activeLotModelTierIDField -> storeAuctionTierModel "references Tier"
    }

    // Won Lot Model Code Level
    storeAuctionWonLotModel {
        wonLotModelIDField = element "ID" "Primary key for won lot" "Model Field"
        wonLotModelPriceField = element "Price" "Final price of won lot" "Model Field"
        wonLotModelUserIDField = element "UserID" "ID of user who won lot" "Model Field"
        wonLotModelCloseTimeField = element "CloseTime" "Time when lot was closed" "Model Field"
        wonLotModelStatusField = element "Status" "Status of won lot (won, cancelled)" "Model Field"
        wonLotModelAgreementIDField = element "AgreementID" "ID of associated agreement" "Model Field"
        wonLotModelTierIDField = element "TierID" "Foreign key to tier" "Model Field"
        wonLotModelCreatedAtField = element "CreatedAt" "Record creation timestamp" "Model Field"
        wonLotModelUpdatedAtField = element "UpdatedAt" "Record update timestamp" "Model Field"
        wonLotModelDeletedAtField = element "DeletedAt" "Soft delete timestamp" "Model Field"

        wonLotModelTierIDField -> storeAuctionTierModel "references Tier"
    }

    // Bid Model Code Level
    storeAuctionBidModel {
        bidModelIDField = element "ID" "Primary key for bid" "Model Field"
        bidModelAmountField = element "Amount" "Bid amount" "Model Field"
        bidModelActiveLotIDField = element "ActiveLotID" "Foreign key to active lot" "Model Field"
        bidModelCreatedAtField = element "CreatedAt" "Record creation timestamp" "Model Field"
        bidModelUpdatedAtField = element "UpdatedAt" "Record update timestamp" "Model Field"
        bidModelDeletedAtField = element "DeletedAt" "Soft delete timestamp" "Model Field"

        bidModelActiveLotIDField -> storeAuctionActiveLotModel "references ActiveLot"
    }

    // Permission Model Code Level
    storeAuctionPermissionModel {
        permissionModelIDField = element "ID" "Primary key for permission" "Model Field"
        permissionModelCategoryField = element "Category" "Permission category" "Model Field"
        permissionModelActionField = element "Action" "Permission action" "Model Field"
        permissionModelCreatedAtField = element "CreatedAt" "Record creation timestamp" "Model Field"
        permissionModelUpdatedAtField = element "UpdatedAt" "Record update timestamp" "Model Field"
        permissionModelDeletedAtField = element "DeletedAt" "Soft delete timestamp" "Model Field"
    }

    // WebSocket Manager Code Level
    storeAuctionWebSocketManager {
        webSocketManagerUpgradeConnectionMethod = element "UpgradeConnection()" "Upgrades HTTP connection to WebSocket" "Manager Method"
        webSocketManagerHandleConnectionMethod = element "HandleConnection()" "Handles WebSocket connection lifecycle" "Manager Method"
        webSocketManagerBroadcastLotsUpdateMethod = element "BroadcastLotsUpdate()" "Broadcasts lot updates to subscribers" "Manager Method"
        webSocketManagerBroadcastWonLotMethod = element "BroadcastWonLot()" "Broadcasts won lot notification" "Manager Method"
        webSocketManagerSubscribeToTierMethod = element "SubscribeToTier()" "Subscribes connection to tier updates" "Manager Method"
        webSocketManagerUnsubscribeFromTierMethod = element "UnsubscribeFromTier()" "Unsubscribes connection from tier updates" "Manager Method"
        webSocketManagerCloseConnectionMethod = element "CloseConnection()" "Closes WebSocket connection" "Manager Method"

        webSocketManagerHandleConnectionMethod -> webSocketManagerSubscribeToTierMethod "calls to subscribe to updates"
            webSocketManagerHandleConnectionMethod -> webSocketManagerCloseConnectionMethod "calls on connection close"
            webSocketManagerBroadcastLotsUpdateMethod -> webSocketManagerSubscribeToTierMethod "uses subscription list"
            webSocketManagerBroadcastWonLotMethod -> webSocketManagerSubscribeToTierMethod "uses subscription list"
        }

        // Code-level diagrams (Level 4 in C4 model) - Operator Service
        operatorService {
            // Network Operator Controller Code Level
            operatorNetworkOperatorController {
        networkOperatorControllerGetStatisticMethod = element "getStatistic()" "Retrieves comprehensive network operator statistics" "Controller Method"
        networkOperatorControllerValidateJwtMethod = element "validateJwt()" "Validates JWT token and extracts role" "Controller Helper Method"
        networkOperatorControllerAggregateStatisticsMethod = element "aggregateStatistics()" "Aggregates statistics from multiple services" "Controller Helper Method"

        networkOperatorControllerGetStatisticMethod -> networkOperatorControllerValidateJwtMethod "calls to validate JWT"
        networkOperatorControllerGetStatisticMethod -> networkOperatorControllerAggregateStatisticsMethod "calls to aggregate statistics"
        networkOperatorControllerValidateJwtMethod -> operatorJwtService "calls getAllClaimsFromToken()"
        networkOperatorControllerAggregateStatisticsMethod -> operatorRequestService "calls getNodeStatistics()"
        networkOperatorControllerAggregateStatisticsMethod -> operatorRequestService "calls getPortalStatistic()"
        networkOperatorControllerAggregateStatisticsMethod -> operatorRequestService "calls getWalletStatistic()"
    }

    // Request Service Code Level
    operatorRequestService {
        requestServiceGetNodeStatisticsMethod = element "getNodeStatistics()" "Retrieves node statistics from Corda5 Bastion" "Service Method"
        requestServiceGetPortalStatisticMethod = element "getPortalStatistic()" "Retrieves portal statistics from User Infrastructure" "Service Method"
        requestServiceGetWalletStatisticMethod = element "getWalletStatistic()" "Retrieves wallet statistics from Auth Gateway" "Service Method"
        requestServiceMakeHttpRequestMethod = element "makeHttpRequest()" "Makes HTTP request to external service" "Service Helper Method"
        requestServiceHandleResponseMethod = element "handleResponse()" "Handles HTTP response and error cases" "Service Helper Method"
        requestServiceBuildAuthHeaderMethod = element "buildAuthHeader()" "Builds Basic Auth header for Corda requests" "Service Helper Method"

        requestServiceGetNodeStatisticsMethod -> requestServiceMakeHttpRequestMethod "calls to make HTTP request"
        requestServiceGetPortalStatisticMethod -> requestServiceMakeHttpRequestMethod "calls to make HTTP request"
        requestServiceGetWalletStatisticMethod -> requestServiceMakeHttpRequestMethod "calls to make HTTP request"
        requestServiceMakeHttpRequestMethod -> requestServiceBuildAuthHeaderMethod "calls to build auth header"
        requestServiceMakeHttpRequestMethod -> requestServiceHandleResponseMethod "calls to handle response"
        requestServiceGetNodeStatisticsMethod -> corda5Bastion "makes HTTP request to get node statistics"
        requestServiceGetPortalStatisticMethod -> userInfrastructure "makes HTTP request to get portal statistics"
        requestServiceGetWalletStatisticMethod -> authGateway "makes HTTP request to get wallet statistics"
    }

    // JWT Service Code Level
    operatorJwtService {
        jwtServiceGetUsernameFromTokenMethod = element "getUsernameFromToken()" "Extracts username from JWT token" "Service Method"
        jwtServiceGetRoleFromTokenMethod = element "getRoleeFromToken()" "Extracts role from JWT token" "Service Method"
        jwtServiceGetExpirationDateFromTokenMethod = element "getExpirationDateFromToken()" "Extracts expiration date from JWT token" "Service Method"
        jwtServiceGetClaimFromTokenMethod = element "getClaimFromToken()" "Extracts specific claim from JWT token" "Service Method"
        jwtServiceGetAllClaimsFromTokenMethod = element "getAllClaimsFromToken()" "Extracts all claims from JWT token" "Service Method"
        jwtServiceIsTokenExpiredMethod = element "isTokenExpired()" "Checks if JWT token is expired" "Service Method"
        jwtServiceValidateTokenMethod = element "validateToken()" "Validates JWT token" "Service Method"

        jwtServiceGetUsernameFromTokenMethod -> jwtServiceGetClaimFromTokenMethod "calls to get username claim"
        jwtServiceGetRoleFromTokenMethod -> jwtServiceGetClaimFromTokenMethod "calls to get role claim"
        jwtServiceGetExpirationDateFromTokenMethod -> jwtServiceGetClaimFromTokenMethod "calls to get expiration claim"
        jwtServiceGetClaimFromTokenMethod -> jwtServiceGetAllClaimsFromTokenMethod "calls to get all claims"
        jwtServiceIsTokenExpiredMethod -> jwtServiceGetExpirationDateFromTokenMethod "calls to get expiration date"
        jwtServiceValidateTokenMethod -> jwtServiceIsTokenExpiredMethod "calls to check expiration"
    }

    // Total Network Operator Statistics DTO Code Level
    operatorTotalNetworkOperatorStatistics {
        totalNetworkOperatorStatisticsInvestorsCountField = element "investorsCount" "Total number of investors in the system" "DTO Field"
        totalNetworkOperatorStatisticsAvatarsCountField = element "avatarsCount" "Total number of avatars in the system" "DTO Field"
        totalNetworkOperatorStatisticsAdviserCountField = element "adviserCount" "Total number of advisers in the system" "DTO Field"
        totalNetworkOperatorStatisticsSignedAgreementsCountField = element "signedAgreementsCount" "Total number of signed agreements" "DTO Field"
        totalNetworkOperatorStatisticsAvatarPerAdviserField = element "avatarPerAdviser" "Map of avatars per adviser" "DTO Field"
        totalNetworkOperatorStatisticsAuaPerAdviserField = element "auaPerAdviser" "Map of AUA (Assets Under Advice) per adviser" "DTO Field"
        totalNetworkOperatorStatisticsWalletStatisticsResultField = element "walletStatisticsResult" "List of wallet statistics by type" "DTO Field"

        totalNetworkOperatorStatisticsWalletStatisticsResultField -> operatorWalletStatisticsResult "contains wallet statistics"
    }

    // Portal Network Operator Statistics DTO Code Level
    operatorPortalNetworkOperatorStatistics {
        portalNetworkOperatorStatisticsInvestorsCountField = element "investorsCount" "Number of investors from portal" "DTO Field"
        portalNetworkOperatorStatisticsAvatarsCountField = element "avatarsCount" "Number of avatars from portal" "DTO Field"
        portalNetworkOperatorStatisticsAdviserCountField = element "adviserCount" "Number of advisers from portal" "DTO Field"
        portalNetworkOperatorStatisticsAvatarsWithNodesField = element "avatarsWithNodes" "Number of avatars with associated nodes" "DTO Field"
        portalNetworkOperatorStatisticsAvatarPerAdviserField = element "avatarPerAdviser" "Map of avatars per adviser from portal" "DTO Field"
    }

    // Node Network Operator Statistic DTO Code Level
    operatorNodeNetworkOperatorStatistic {
        nodeNetworkOperatorStatisticAgreementCountField = element "agreementCount" "Number of agreements from blockchain" "DTO Field"
        nodeNetworkOperatorStatisticAgreementStatisticsField = element "agreementStatistics" "Map of agreement statistics by adviser" "DTO Field"
    }

    // Wallet Statistics Result DTO Code Level
    operatorWalletStatisticsResult {
        walletStatisticsResultWalletTypeField = element "walletType" "Type of wallet (DFNS, SAFEHERON)" "DTO Field"
        walletStatisticsResultCountField = element "count" "Number of wallets of this type" "DTO Field"
    }

    // Role Enum Code Level
    operatorRole {
        roleInvestorValue = element "INVESTOR" "Investor role enum value" "Enum Value"
        roleAvatarValue = element "AVATAR" "Avatar role enum value" "Enum Value"
        roleAdviserValue = element "ADVISER" "Adviser role enum value" "Enum Value"
        roleNetworkOperatorValue = element "NETWORK_OPERATOR" "Network operator role enum value" "Enum Value"
    }

    // Flow Status Enum Code Level
    operatorFlowStatus {
        flowStatusCompletedValue = element "COMPLETED" "Flow completed status" "Enum Value"
        flowStatusRunningValue = element "RUNNING" "Flow running status" "Enum Value"
        flowStatusFailedValue = element "FAILED" "Flow failed status" "Enum Value"
    }

    // Connection Flow Exception Code Level
    operatorConnectionFlowException {
        connectionFlowExceptionConstructor = element "ConnectionFlowException()" "Constructor for connection flow exception" "Exception Constructor"
        connectionFlowExceptionMessageField = element "message" "Exception message field" "Exception Field"

        connectionFlowExceptionConstructor -> connectionFlowExceptionMessageField "sets exception message"
    }

    // Failed Flow Exception Code Level
    operatorFailedFlowException {
        failedFlowExceptionConstructor = element "FailedFlowException()" "Constructor for failed flow exception" "Exception Constructor"
        failedFlowExceptionMessageField = element "message" "Exception message field" "Exception Field"

        failedFlowExceptionConstructor -> failedFlowExceptionMessageField "sets exception message"
    }

    // Security Configuration Code Level
    operatorSecurityConfiguration {
        securityConfigurationConfigureMethod = element "configure()" "Configures HTTP security settings" "Configuration Method"
        securityConfigurationDisableCsrfMethod = element "disableCsrf()" "Disables CSRF protection" "Configuration Helper Method"
        securityConfigurationPermitAllRequestsMethod = element "permitAllRequests()" "Permits all requests without authentication" "Configuration Helper Method"
        securityConfigurationSetSessionPolicyMethod = element "setSessionPolicy()" "Sets session creation policy to stateless" "Configuration Helper Method"

            securityConfigurationConfigureMethod -> securityConfigurationDisableCsrfMethod "calls to disable CSRF"
            securityConfigurationConfigureMethod -> securityConfigurationPermitAllRequestsMethod "calls to permit all requests"
            securityConfigurationConfigureMethod -> securityConfigurationSetSessionPolicyMethod "calls to set session policy"
        }

        // Code-level diagrams (Level 4 in C4 model) - Corda5 Bastion Service
        corda5Bastion {
            // Propose Agreement Flow Code Level
            corda5BastionProposeAgreementFlow {
        proposeAgreementFlowCallMethod = element "call()" "Main flow execution method for proposing agreements" "Flow Method"
        proposeAgreementFlowParseParametersMethod = element "parseParameters()" "Parses JSON parameters from RPC request" "Flow Helper Method"
        proposeAgreementFlowValidateParametersMethod = element "validateParameters()" "Validates agreement parameters" "Flow Helper Method"
        proposeAgreementFlowCreateAgreementStateMethod = element "createAgreementState()" "Creates new AgreementState with PROPOSED status" "Flow Helper Method"
        proposeAgreementFlowBuildTransactionMethod = element "buildTransaction()" "Builds unsigned transaction with agreement state" "Flow Helper Method"
        proposeAgreementFlowSignTransactionMethod = element "signTransaction()" "Signs transaction with initiator's key" "Flow Helper Method"
        proposeAgreementFlowCollectSignaturesMethod = element "collectSignatures()" "Collects signatures from counterparties" "Flow Helper Method"
        proposeAgreementFlowFinalizeTransactionMethod = element "finalizeTransaction()" "Notarizes and records transaction" "Flow Helper Method"
        proposeAgreementFlowSendNotificationMethod = element "sendNotification()" "Sends notification to external services" "Flow Helper Method"

        proposeAgreementFlowCallMethod -> proposeAgreementFlowParseParametersMethod "calls to parse parameters"
        proposeAgreementFlowCallMethod -> proposeAgreementFlowValidateParametersMethod "calls to validate parameters"
        proposeAgreementFlowCallMethod -> proposeAgreementFlowCreateAgreementStateMethod "calls to create agreement state"
        proposeAgreementFlowCallMethod -> proposeAgreementFlowBuildTransactionMethod "calls to build transaction"
        proposeAgreementFlowCallMethod -> proposeAgreementFlowSignTransactionMethod "calls to sign transaction"
        proposeAgreementFlowCallMethod -> proposeAgreementFlowCollectSignaturesMethod "calls to collect signatures"
        proposeAgreementFlowCallMethod -> proposeAgreementFlowFinalizeTransactionMethod "calls to finalize transaction"
        proposeAgreementFlowCallMethod -> proposeAgreementFlowSendNotificationMethod "calls to send notification"
        proposeAgreementFlowSendNotificationMethod -> userInfrastructure "sends HTTP notification about agreement proposal"
    }

    // Sign Agreement Flow Code Level
    corda5BastionSignAgreementFlow {
        signAgreementFlowCallMethod = element "call()" "Main flow execution method for signing agreements" "Flow Method"
        signAgreementFlowParseParametersMethod = element "parseParameters()" "Parses agreement ID from RPC request" "Flow Helper Method"
        signAgreementFlowQueryAgreementStateMethod = element "queryAgreementState()" "Queries existing agreement state from vault" "Flow Helper Method"
        signAgreementFlowValidateStatusMethod = element "validateStatus()" "Validates agreement is in PROPOSED status" "Flow Helper Method"
        signAgreementFlowChangeStatusMethod = element "changeStatus()" "Changes agreement status to SIGNED" "Flow Helper Method"
        signAgreementFlowBuildTransactionMethod = element "buildTransaction()" "Builds transaction with input and output states" "Flow Helper Method"
        signAgreementFlowSignTransactionMethod = element "signTransaction()" "Signs transaction with signer's key" "Flow Helper Method"
        signAgreementFlowCollectSignaturesMethod = element "collectSignatures()" "Collects signatures from counterparties" "Flow Helper Method"
        signAgreementFlowFinalizeTransactionMethod = element "finalizeTransaction()" "Notarizes and records transaction" "Flow Helper Method"
        signAgreementFlowSendNotificationMethod = element "sendNotification()" "Sends notification to external services" "Flow Helper Method"

        signAgreementFlowCallMethod -> signAgreementFlowParseParametersMethod "calls to parse parameters"
        signAgreementFlowCallMethod -> signAgreementFlowQueryAgreementStateMethod "calls to query agreement state"
        signAgreementFlowCallMethod -> signAgreementFlowValidateStatusMethod "calls to validate status"
        signAgreementFlowCallMethod -> signAgreementFlowChangeStatusMethod "calls to change status"
        signAgreementFlowCallMethod -> signAgreementFlowBuildTransactionMethod "calls to build transaction"
        signAgreementFlowCallMethod -> signAgreementFlowSignTransactionMethod "calls to sign transaction"
        signAgreementFlowCallMethod -> signAgreementFlowCollectSignaturesMethod "calls to collect signatures"
        signAgreementFlowCallMethod -> signAgreementFlowFinalizeTransactionMethod "calls to finalize transaction"
        signAgreementFlowCallMethod -> signAgreementFlowSendNotificationMethod "calls to send notification"
        signAgreementFlowSendNotificationMethod -> userInfrastructure "sends HTTP notification about agreement signing"
    }

    // Verify Agreement Flow Code Level
    corda5BastionVerifyAgreementFlow {
        verifyAgreementFlowCallMethod = element "call()" "Main flow execution method for verifying agreements" "Flow Method"
        verifyAgreementFlowParseParametersMethod = element "parseParameters()" "Parses agreement ID and verification result from RPC request" "Flow Helper Method"
        verifyAgreementFlowQueryAgreementStateMethod = element "queryAgreementState()" "Queries existing agreement state from vault" "Flow Helper Method"
        verifyAgreementFlowValidateStatusMethod = element "validateStatus()" "Validates agreement is in SIGNED status" "Flow Helper Method"
        verifyAgreementFlowProcessVerificationMethod = element "processVerification()" "Processes verification result (VERIFIED or DECLINED)" "Flow Helper Method"
        verifyAgreementFlowChangeStatusMethod = element "changeStatus()" "Changes agreement status based on verification result" "Flow Helper Method"
        verifyAgreementFlowBuildTransactionMethod = element "buildTransaction()" "Builds transaction with input and output states" "Flow Helper Method"
        verifyAgreementFlowSignTransactionMethod = element "signTransaction()" "Signs transaction with verifier's key" "Flow Helper Method"
        verifyAgreementFlowCollectSignaturesMethod = element "collectSignatures()" "Collects signatures from counterparties" "Flow Helper Method"
        verifyAgreementFlowFinalizeTransactionMethod = element "finalizeTransaction()" "Notarizes and records transaction" "Flow Helper Method"
        verifyAgreementFlowSendNotificationMethod = element "sendNotification()" "Sends notification to external services" "Flow Helper Method"

        verifyAgreementFlowCallMethod -> verifyAgreementFlowParseParametersMethod "calls to parse parameters"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowQueryAgreementStateMethod "calls to query agreement state"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowValidateStatusMethod "calls to validate status"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowProcessVerificationMethod "calls to process verification"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowChangeStatusMethod "calls to change status"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowBuildTransactionMethod "calls to build transaction"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowSignTransactionMethod "calls to sign transaction"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowCollectSignaturesMethod "calls to collect signatures"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowFinalizeTransactionMethod "calls to finalize transaction"
        verifyAgreementFlowCallMethod -> verifyAgreementFlowSendNotificationMethod "calls to send notification"
        verifyAgreementFlowSendNotificationMethod -> userInfrastructure "sends HTTP notification about agreement verification"
    }

    // Notarize Agreement Flow Code Level
    corda5BastionNotarizeAgreementFlow {
        notarizeAgreementFlowCallMethod = element "call()" "Main flow execution method for notarizing agreements" "Flow Method"
        notarizeAgreementFlowParseParametersMethod = element "parseParameters()" "Parses agreement ID from RPC request" "Flow Helper Method"
        notarizeAgreementFlowQueryAgreementStateMethod = element "queryAgreementState()" "Queries existing agreement state from vault" "Flow Helper Method"
        notarizeAgreementFlowValidateStatusMethod = element "validateStatus()" "Validates agreement is in VERIFIED status" "Flow Helper Method"
        notarizeAgreementFlowChangeStatusMethod = element "changeStatus()" "Changes agreement status to NOTARIZED" "Flow Helper Method"
        notarizeAgreementFlowBuildTransactionMethod = element "buildTransaction()" "Builds transaction with input and output states" "Flow Helper Method"
        notarizeAgreementFlowSignTransactionMethod = element "signTransaction()" "Signs transaction with notarizer's key" "Flow Helper Method"
        notarizeAgreementFlowCollectSignaturesMethod = element "collectSignatures()" "Collects signatures from counterparties" "Flow Helper Method"
        notarizeAgreementFlowFinalizeTransactionMethod = element "finalizeTransaction()" "Notarizes and records transaction" "Flow Helper Method"
        notarizeAgreementFlowSendNotificationMethod = element "sendNotification()" "Sends notification to external services" "Flow Helper Method"

        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowParseParametersMethod "calls to parse parameters"
        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowQueryAgreementStateMethod "calls to query agreement state"
        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowValidateStatusMethod "calls to validate status"
        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowChangeStatusMethod "calls to change status"
        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowBuildTransactionMethod "calls to build transaction"
        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowSignTransactionMethod "calls to sign transaction"
        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowCollectSignaturesMethod "calls to collect signatures"
        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowFinalizeTransactionMethod "calls to finalize transaction"
        notarizeAgreementFlowCallMethod -> notarizeAgreementFlowSendNotificationMethod "calls to send notification"
        notarizeAgreementFlowSendNotificationMethod -> userInfrastructure "sends HTTP notification about agreement notarization"
    }

    // Decline Agreement Flow Code Level
    corda5BastionDeclineAgreementFlow {
        declineAgreementFlowCallMethod = element "call()" "Main flow execution method for declining agreements" "Flow Method"
        declineAgreementFlowParseParametersMethod = element "parseParameters()" "Parses agreement ID and decline reason from RPC request" "Flow Helper Method"
        declineAgreementFlowQueryAgreementStateMethod = element "queryAgreementState()" "Queries existing agreement state from vault" "Flow Helper Method"
        declineAgreementFlowValidateStatusMethod = element "validateStatus()" "Validates agreement can be declined" "Flow Helper Method"
        declineAgreementFlowDeclineAgreementMethod = element "declineAgreement()" "Declines agreement with reason and decliner info" "Flow Helper Method"
        declineAgreementFlowBuildTransactionMethod = element "buildTransaction()" "Builds transaction with input and output states" "Flow Helper Method"
        declineAgreementFlowSignTransactionMethod = element "signTransaction()" "Signs transaction with decliner's key" "Flow Helper Method"
        declineAgreementFlowCollectSignaturesMethod = element "collectSignatures()" "Collects signatures from counterparties" "Flow Helper Method"
        declineAgreementFlowFinalizeTransactionMethod = element "finalizeTransaction()" "Notarizes and records transaction" "Flow Helper Method"
        declineAgreementFlowSendNotificationMethod = element "sendNotification()" "Sends notification to external services" "Flow Helper Method"

        declineAgreementFlowCallMethod -> declineAgreementFlowParseParametersMethod "calls to parse parameters"
        declineAgreementFlowCallMethod -> declineAgreementFlowQueryAgreementStateMethod "calls to query agreement state"
        declineAgreementFlowCallMethod -> declineAgreementFlowValidateStatusMethod "calls to validate status"
        declineAgreementFlowCallMethod -> declineAgreementFlowDeclineAgreementMethod "calls to decline agreement"
        declineAgreementFlowCallMethod -> declineAgreementFlowBuildTransactionMethod "calls to build transaction"
        declineAgreementFlowCallMethod -> declineAgreementFlowSignTransactionMethod "calls to sign transaction"
        declineAgreementFlowCallMethod -> declineAgreementFlowCollectSignaturesMethod "calls to collect signatures"
        declineAgreementFlowCallMethod -> declineAgreementFlowFinalizeTransactionMethod "calls to finalize transaction"
        declineAgreementFlowCallMethod -> declineAgreementFlowSendNotificationMethod "calls to send notification"
        declineAgreementFlowSendNotificationMethod -> userInfrastructure "sends HTTP notification about agreement decline"
    }

    // Complete Agreement Flow Code Level
    corda5BastionCompleteAgreementFlow {
        completeAgreementFlowCallMethod = element "call()" "Main flow execution method for completing agreements" "Flow Method"
        completeAgreementFlowParseParametersMethod = element "parseParameters()" "Parses agreement ID from RPC request" "Flow Helper Method"
        completeAgreementFlowQueryAgreementStateMethod = element "queryAgreementState()" "Queries existing agreement state from vault" "Flow Helper Method"
        completeAgreementFlowValidateStatusMethod = element "validateStatus()" "Validates agreement is in NOTARIZED or DECLINED status" "Flow Helper Method"
        completeAgreementFlowBuildTransactionMethod = element "buildTransaction()" "Builds transaction to consume agreement state" "Flow Helper Method"
        completeAgreementFlowSignTransactionMethod = element "signTransaction()" "Signs transaction with completer's key" "Flow Helper Method"
        completeAgreementFlowCollectSignaturesMethod = element "collectSignatures()" "Collects signatures from counterparties" "Flow Helper Method"
        completeAgreementFlowFinalizeTransactionMethod = element "finalizeTransaction()" "Notarizes and records transaction" "Flow Helper Method"
        completeAgreementFlowSendNotificationMethod = element "sendNotification()" "Sends notification to external services" "Flow Helper Method"

        completeAgreementFlowCallMethod -> completeAgreementFlowParseParametersMethod "calls to parse parameters"
        completeAgreementFlowCallMethod -> completeAgreementFlowQueryAgreementStateMethod "calls to query agreement state"
        completeAgreementFlowCallMethod -> completeAgreementFlowValidateStatusMethod "calls to validate status"
        completeAgreementFlowCallMethod -> completeAgreementFlowBuildTransactionMethod "calls to build transaction"
        completeAgreementFlowCallMethod -> completeAgreementFlowSignTransactionMethod "calls to sign transaction"
        completeAgreementFlowCallMethod -> completeAgreementFlowCollectSignaturesMethod "calls to collect signatures"
        completeAgreementFlowCallMethod -> completeAgreementFlowFinalizeTransactionMethod "calls to finalize transaction"
        completeAgreementFlowCallMethod -> completeAgreementFlowSendNotificationMethod "calls to send notification"
        completeAgreementFlowSendNotificationMethod -> userInfrastructure "sends HTTP notification about agreement completion"
    }

    // Agreement State Code Level
    corda5BastionAgreementState {
        agreementStateAgreementIDField = element "agreementID" "Unique identifier for the agreement" "State Field"
        agreementStateAgreementDateField = element "agreementDate" "Date when agreement was created" "State Field"
        agreementStateAgreementHashField = element "agreementHash" "Hash of the agreement document" "State Field"
        agreementStateAdvisorField = element "advisor" "Advisor party in the agreement" "State Field"
        agreementStateAvatarField = element "avatar" "Avatar party in the agreement" "State Field"
        agreementStateAvatarIDField = element "avatarID" "Unique identifier for the avatar" "State Field"
        agreementStatePlatformField = element "platform" "Platform party in the agreement" "State Field"
        agreementStateInvestmentField = element "investment" "Investment amount in the agreement" "State Field"
        agreementStateFeeField = element "fee" "Fee percentage for the agreement" "State Field"
        agreementStateStatusField = element "status" "Current status of the agreement" "State Field"
        agreementStateDeclinedByField = element "declinedBy" "Party who declined the agreement" "State Field"
        agreementStateDeclineReasonField = element "declineReason" "Reason for declining the agreement" "State Field"
        agreementStateParticipantsField = element "participants" "List of parties participating in the agreement" "State Field"

        agreementStateChangeStatusMethod = element "changeStatus()" "Changes the status of the agreement" "State Method"
        agreementStateDeclineAgreementMethod = element "declineAgreement()" "Declines the agreement with reason and decliner" "State Method"
        agreementStateToJsonMethod = element "toJson()" "Converts agreement state to JSON representation" "State Method"
        agreementStateGenerateMappedObjectMethod = element "generateMappedObject()" "Generates persistent object for database storage" "State Method"
        agreementStateSupportedSchemasMethod = element "supportedSchemas()" "Returns supported database schemas" "State Method"

        agreementStateChangeStatusMethod -> agreementStateStatusField "modifies status field"
        agreementStateDeclineAgreementMethod -> agreementStateStatusField "modifies status field"
        agreementStateDeclineAgreementMethod -> agreementStateDeclinedByField "modifies declinedBy field"
        agreementStateDeclineAgreementMethod -> agreementStateDeclineReasonField "modifies declineReason field"
        agreementStateGenerateMappedObjectMethod -> corda5BastionAgreementSchemaV1 "creates persistent agreement object"
    }

    // Propose Agreement Contract Code Level
    corda5BastionProposeAgreementContract {
        proposeAgreementContractVerifyMethod = element "verify()" "Verifies transaction validity based on command type" "Contract Method"
        proposeAgreementContractVerifyCreateMethod = element "verifyCreate()" "Verifies CREATE command constraints" "Contract Helper Method"
        proposeAgreementContractVerifySignMethod = element "verifySign()" "Verifies SIGN command constraints" "Contract Helper Method"
        proposeAgreementContractVerifyVerifyMethod = element "verifyVerify()" "Verifies VERIFY command constraints" "Contract Helper Method"
        proposeAgreementContractVerifyNotarizeMethod = element "verifyNotarize()" "Verifies NOTARIZE command constraints" "Contract Helper Method"
        proposeAgreementContractVerifyDeclineMethod = element "verifyDecline()" "Verifies DECLINE command constraints" "Contract Helper Method"
        proposeAgreementContractVerifyCompleteMethod = element "verifyComplete()" "Verifies COMPLETE command constraints" "Contract Helper Method"

        proposeAgreementContractVerifyMethod -> proposeAgreementContractVerifyCreateMethod "calls for CREATE command"
        proposeAgreementContractVerifyMethod -> proposeAgreementContractVerifySignMethod "calls for SIGN command"
        proposeAgreementContractVerifyMethod -> proposeAgreementContractVerifyVerifyMethod "calls for VERIFY command"
        proposeAgreementContractVerifyMethod -> proposeAgreementContractVerifyNotarizeMethod "calls for NOTARIZE command"
        proposeAgreementContractVerifyMethod -> proposeAgreementContractVerifyDeclineMethod "calls for DECLINE command"
        proposeAgreementContractVerifyMethod -> proposeAgreementContractVerifyCompleteMethod "calls for COMPLETE command"
    }

    // Status Enum Code Level
    corda5BastionStatus {
        statusProposedValue = element "PROPOSED" "Agreement has been proposed" "Enum Value"
        statusSignedValue = element "SIGNED" "Agreement has been signed by avatar" "Enum Value"
        statusVerifiedValue = element "VERIFIED" "Agreement has been verified by platform" "Enum Value"
        statusNotarizedValue = element "NOTARIZED" "Agreement has been notarized" "Enum Value"
        statusDeclinedValue = element "DECLINED" "Agreement has been declined" "Enum Value"
    }

    // Agreement Schema V1 Code Level
    corda5BastionAgreementSchemaV1 {
        agreementSchemaV1PersistentAgreementClass = element "PersistentAgreement" "JPA entity for storing agreement data" "Entity Class"
        agreementSchemaV1FindAllQueryMethod = element "findAllQuery" "Named query to find all unconsumed or completed agreements" "Named Query"
        agreementSchemaV1FindAllByAvatarIDQueryMethod = element "findAllByAvatarIDQuery" "Named query to find agreements by avatar ID" "Named Query"
        agreementSchemaV1MigrationResourceField = element "migrationResource" "Database migration resource file" "Schema Field"

        agreementSchemaV1PersistentAgreementClass -> agreementSchemaV1FindAllQueryMethod "uses for data retrieval"
        agreementSchemaV1PersistentAgreementClass -> agreementSchemaV1FindAllByAvatarIDQueryMethod "uses for filtered data retrieval"
    }

    // Propose Agreement Flow Acceptor Code Level
    corda5BastionProposeAgreementFlowAcceptor {
        proposeAgreementFlowAcceptorCallMethod = element "call()" "Accepts and processes proposed agreement" "Flow Method"
        proposeAgreementFlowAcceptorIsValidMethod = element "isValid()" "Validates incoming signed transaction" "Flow Helper Method"
        proposeAgreementFlowAcceptorCheckTransactionMethod = element "checkTransaction()" "Checks transaction validity before signing" "Flow Helper Method"
        proposeAgreementFlowAcceptorSignTransactionMethod = element "signTransaction()" "Signs the transaction" "Flow Helper Method"
        proposeAgreementFlowAcceptorReceiveFinalityMethod = element "receiveFinality()" "Receives finalized transaction" "Flow Helper Method"

        proposeAgreementFlowAcceptorCallMethod -> proposeAgreementFlowAcceptorSignTransactionMethod "calls to sign transaction"
        proposeAgreementFlowAcceptorCallMethod -> proposeAgreementFlowAcceptorReceiveFinalityMethod "calls to receive finality"
        proposeAgreementFlowAcceptorSignTransactionMethod -> proposeAgreementFlowAcceptorCheckTransactionMethod "calls to check transaction"
        proposeAgreementFlowAcceptorCheckTransactionMethod -> proposeAgreementFlowAcceptorIsValidMethod "calls to validate transaction"
    }

    // Sign Agreement Flow Responder Code Level
    corda5BastionSignAgreementFlowResponder {
        signAgreementFlowResponderCallMethod = element "call()" "Responds to sign agreement flow" "Flow Method"
        signAgreementFlowResponderCheckTransactionMethod = element "checkTransaction()" "Checks transaction validity before signing" "Flow Helper Method"
        signAgreementFlowResponderSignTransactionMethod = element "signTransaction()" "Signs the transaction" "Flow Helper Method"
        signAgreementFlowResponderReceiveFinalityMethod = element "receiveFinality()" "Receives finalized transaction" "Flow Helper Method"

        signAgreementFlowResponderCallMethod -> signAgreementFlowResponderSignTransactionMethod "calls to sign transaction"
        signAgreementFlowResponderCallMethod -> signAgreementFlowResponderReceiveFinalityMethod "calls to receive finality"
        signAgreementFlowResponderSignTransactionMethod -> signAgreementFlowResponderCheckTransactionMethod "calls to check transaction"
    }

    // Verify Agreement Flow Responder Code Level
    corda5BastionVerifyAgreementFlowResponder {
        verifyAgreementFlowResponderCallMethod = element "call()" "Responds to verify agreement flow" "Flow Method"
        verifyAgreementFlowResponderCheckTransactionMethod = element "checkTransaction()" "Checks transaction validity before signing" "Flow Helper Method"
        verifyAgreementFlowResponderSignTransactionMethod = element "signTransaction()" "Signs the transaction" "Flow Helper Method"
        verifyAgreementFlowResponderReceiveFinalityMethod = element "receiveFinality()" "Receives finalized transaction" "Flow Helper Method"

        verifyAgreementFlowResponderCallMethod -> verifyAgreementFlowResponderSignTransactionMethod "calls to sign transaction"
        verifyAgreementFlowResponderCallMethod -> verifyAgreementFlowResponderReceiveFinalityMethod "calls to receive finality"
        verifyAgreementFlowResponderSignTransactionMethod -> verifyAgreementFlowResponderCheckTransactionMethod "calls to check transaction"
    }

    // Notarize Agreement Flow Responder Code Level
    corda5BastionNotarizeAgreementFlowResponder {
        notarizeAgreementFlowResponderCallMethod = element "call()" "Responds to notarize agreement flow" "Flow Method"
        notarizeAgreementFlowResponderCheckTransactionMethod = element "checkTransaction()" "Checks transaction validity before signing" "Flow Helper Method"
        notarizeAgreementFlowResponderSignTransactionMethod = element "signTransaction()" "Signs the transaction" "Flow Helper Method"
        notarizeAgreementFlowResponderReceiveFinalityMethod = element "receiveFinality()" "Receives finalized transaction" "Flow Helper Method"

        notarizeAgreementFlowResponderCallMethod -> notarizeAgreementFlowResponderSignTransactionMethod "calls to sign transaction"
        notarizeAgreementFlowResponderCallMethod -> notarizeAgreementFlowResponderReceiveFinalityMethod "calls to receive finality"
        notarizeAgreementFlowResponderSignTransactionMethod -> notarizeAgreementFlowResponderCheckTransactionMethod "calls to check transaction"
    }

    // Decline Agreement Flow Responder Code Level
    corda5BastionDeclineAgreementFlowResponder {
        declineAgreementFlowResponderCallMethod = element "call()" "Responds to decline agreement flow" "Flow Method"
        declineAgreementFlowResponderCheckTransactionMethod = element "checkTransaction()" "Checks transaction validity before signing" "Flow Helper Method"
        declineAgreementFlowResponderSignTransactionMethod = element "signTransaction()" "Signs the transaction" "Flow Helper Method"
        declineAgreementFlowResponderReceiveFinalityMethod = element "receiveFinality()" "Receives finalized transaction" "Flow Helper Method"

        declineAgreementFlowResponderCallMethod -> declineAgreementFlowResponderSignTransactionMethod "calls to sign transaction"
        declineAgreementFlowResponderCallMethod -> declineAgreementFlowResponderReceiveFinalityMethod "calls to receive finality"
        declineAgreementFlowResponderSignTransactionMethod -> declineAgreementFlowResponderCheckTransactionMethod "calls to check transaction"
    }

    // Complete Agreement Flow Responder Code Level
    corda5BastionCompleteAgreementFlowResponder {
        completeAgreementFlowResponderCallMethod = element "call()" "Responds to complete agreement flow" "Flow Method"
        completeAgreementFlowResponderCheckTransactionMethod = element "checkTransaction()" "Checks transaction validity before signing" "Flow Helper Method"
        completeAgreementFlowResponderSignTransactionMethod = element "signTransaction()" "Signs the transaction" "Flow Helper Method"
        completeAgreementFlowResponderReceiveFinalityMethod = element "receiveFinality()" "Receives finalized transaction" "Flow Helper Method"

        completeAgreementFlowResponderCallMethod -> completeAgreementFlowResponderSignTransactionMethod "calls to sign transaction"
        completeAgreementFlowResponderCallMethod -> completeAgreementFlowResponderReceiveFinalityMethod "calls to receive finality"
        completeAgreementFlowResponderSignTransactionMethod -> completeAgreementFlowResponderCheckTransactionMethod "calls to check transaction"
    }

    // Contract Commands Code Level
    corda5BastionContractCommands {
        contractCommandsCreateCommand = element "Create" "Command for creating new agreement" "Command Class"
        contractCommandsSignCommand = element "Sign" "Command for signing agreement" "Command Class"
        contractCommandsVerifyCommand = element "Verify" "Command for verifying agreement" "Command Class"
        contractCommandsNotarizeCommand = element "Notarize" "Command for notarizing agreement" "Command Class"
        contractCommandsDeclineCommand = element "Decline" "Command for declining agreement" "Command Class"
        contractCommandsCompleteCommand = element "Complete" "Command for completing agreement" "Command Class"
    }

    // Corda Notification Utility Code Level
    corda5BastionCordaNotification {
        cordaNotificationSendNotificationMethod = element "sendNotification()" "Sends HTTP notification to external service" "Utility Method"
        cordaNotificationBuildRequestMethod = element "buildRequest()" "Builds HTTP request with notification data" "Utility Helper Method"
        cordaNotificationExecuteRequestMethod = element "executeRequest()" "Executes HTTP request to notification endpoint" "Utility Helper Method"
        cordaNotificationHandleResponseMethod = element "handleResponse()" "Handles HTTP response from notification service" "Utility Helper Method"

        cordaNotificationSendNotificationMethod -> cordaNotificationBuildRequestMethod "calls to build request"
        cordaNotificationSendNotificationMethod -> cordaNotificationExecuteRequestMethod "calls to execute request"
        cordaNotificationSendNotificationMethod -> cordaNotificationHandleResponseMethod "calls to handle response"
    }

    // Annex1 Utility Code Level
    corda5BastionAnnex1 {
        annex1ValidateAgreementMethod = element "validateAgreement()" "Validates agreement against Annex1 requirements" "Utility Method"
        annex1CheckComplianceMethod = element "checkCompliance()" "Checks compliance with regulatory requirements" "Utility Helper Method"
        annex1GenerateReportMethod = element "generateReport()" "Generates compliance report" "Utility Helper Method"

            annex1ValidateAgreementMethod -> annex1CheckComplianceMethod "calls to check compliance"
            annex1ValidateAgreementMethod -> annex1GenerateReportMethod "calls to generate report"
        }
    }

    views {
        // Main holistic diagrams
        systemContext polityPlatform "SystemContext" {
            include *
            autoLayout
        }

        container polityPlatform "ContainerView" "All Containers - Complete System View" {
            include *
            autoLayout
        }

        // Individual container-focused diagrams using dynamic relationship inclusion
        container polityPlatform "AuthPortal" "Auth Portal - Focused View" {
            include ->authPortal->
            autoLayout
        }

        container polityPlatform "UserCabinet" "User Cabinet - Focused View" {
            include ->userCabinet->
            autoLayout
        }

        container polityPlatform "OperatorDashboard" "Operator Dashboard - Focused View" {
            include ->operatorDashboard->
            autoLayout
        }

        container polityPlatform "AuthGateway" "Auth Gateway - Focused View" {
            include ->authGateway->
            autoLayout
        }

        container polityPlatform "UserInfrastructure" "User Infrastructure Service - Focused View" {
            include ->userInfrastructure->
            autoLayout
        }

        container polityPlatform "WalletInfrastructure" "Wallet Infrastructure Service - Focused View" {
            include ->walletInfrastructure->
            autoLayout
        }

        container polityPlatform "PolityVault" "Polity Vault - Focused View" {
            include ->polityVault->
            autoLayout
        }

        container polityPlatform "StoreAuction" "Store & Auction Service - Focused View" {
            include ->storeAuction->
            autoLayout
        }

        container polityPlatform "OperatorService" "Operator Service - Focused View" {
            include ->operatorService->
            autoLayout
        }

        container polityPlatform "Corda5Bastion" "Corda5 Bastion - Focused View" {
            include ->corda5Bastion->
            autoLayout
        }

        container polityPlatform "FrontendServer" "Frontend Server - Focused View" {
            include ->frontendServer->
            autoLayout
        }

        // Component-level diagrams (Level 3)
        component authGateway "AuthGatewayComponents" "Auth Gateway - Component View" {
            include *
            autoLayout
        }

        component userInfrastructure "UserInfrastructureComponents" "User Infrastructure Service - Component View" {
            include *
            autoLayout
        }

        component walletInfrastructure "WalletInfrastructureComponents" "Wallet Infrastructure Service - Component View" {
            include *
            autoLayout
        }

        component polityVault "PolityVaultComponents" "Polity Vault - Component View" {
            include *
            autoLayout
        }

        component storeAuction "StoreAuctionComponents" "Store & Auction Service - Component View" {
            include *
            autoLayout
        }

        component operatorService "OperatorServiceComponents" "Operator Service - Component View" {
            include *
            autoLayout
        }

        component corda5Bastion "Corda5BastionComponents" "Corda5 Bastion - Component View" {
            include *
            autoLayout
        }

        // Code Level Views for all components (Level 4)
        // Auth Gateway Service Code Level Views
        systemContext authGatewayCodeLevel "Auth Gateway Service - Code Level" {
            include ->authGateway->
            autoLayout
        }

        // User Infrastructure Service Code Level Views
        systemContext userInfraCodeLevel "User Infrastructure Service - Code Level" {
            include ->userInfrastructure->
            autoLayout
        }

        // Wallet Infrastructure Service Code Level Views
        systemContext walletInfraCodeLevel "Wallet Infrastructure Service - Code Level" {
            include ->walletInfrastructure->
            autoLayout
        }

        // Polity Vault Service Code Level Views
        systemContext polityVaultCodeLevel "Polity Vault Service - Code Level" {
            include ->polityVault->
            autoLayout
        }

        // Store & Auction Service Code Level Views
        systemContext storeAuctionCodeLevel "Store & Auction Service - Code Level" {
            include ->storeAuction->
            autoLayout
        }

        // Operator Service Code Level Views
        systemContext operatorCodeLevel "Operator Service - Code Level" {
            include ->operatorService->
            autoLayout
        }

        // Corda5 Bastion Service Code Level Views
        systemContext corda5BastionCodeLevel "Corda5 Bastion Service - Code Level" {
            include ->corda5Bastion->
            autoLayout
        }

        styles {
            element "Person" {
                color #ffffff
                fontSize 22
                shape Person
            }
            element "External System" {
                background #999999
                color #ffffff
            }
            element "Web Browser" {
                shape WebBrowser
            }
            element "Microservice" {
                shape Hexagon
            }
            element "Database" {
                shape Cylinder
            }
            element "Cache" {
                shape Cylinder
                background #ff6b6b
            }
            element "Blockchain Node" {
                shape Component
                background #4ecdc4
            }
            element "Web Server" {
                shape Box
                background #45b7d1
            }
        }
    }
}
