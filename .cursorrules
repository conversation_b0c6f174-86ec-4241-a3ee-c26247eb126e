
# main-overview

## Development Guidelines

- Only modify code directly relevant to the specific request. Avoid changing unrelated functionality.
- Never replace code with placeholders like `# ... rest of the processing ...`. Always include complete code.
- Break problems into smaller steps. Think through each step separately before implementing.
- Always provide a complete PLAN with REASONING based on evidence from code and logs before making changes.
- Explain your OBSERVATIONS clearly, then provide REASONING to identify the exact issue. Add console logs when needed to gather more information.


The system implements a comprehensive digital asset management platform with several key business domains:

## Wallet Management Infrastructure
Core implementation in `wallet-infrastructure-service/internal/service/wallets/`:
- Multi-provider wallet system supporting DFNS and Safeheron
- Hierarchical wallet relationships with parent-child structures 
- Provider-specific network initialization and asset support
- Custom transaction validation workflows
- Multi-signature approval process

## Financial Operations
Located in `wallet-infrastructure-service/internal/service/defi/`:
- DeFi protocol integration across multiple chains
- Position management for lending/borrowing
- Total value locked (TVL) calculations
- Cross-chain asset conversion and rate management
- Custom fee structures per transaction type

## Authentication & Identity 
Implemented in `auth-gateway/service/`:
- Role-based system (Investor, Adviser, Avatar, Network Operator)
- Custom routing based on user type
- Multi-factor authentication flows
- Role-specific access policies

## Agreement Processing
Core logic in `corda5-bastion/contracts/`:
- Multi-stage agreement lifecycle: PROPOSED → SIGNED → VERIFIED → NOTARIZED
- Investment validation with minimum thresholds:
  - Initial: ≥ 100 EUR
  - Subsequent: ≥ 10 EUR
- Party separation enforcement between advisors, avatars, and platform

## Auction System
Implemented in `store-auction/auction/`:
- Custom bidding rules with countdown extensions
- Automated lot management
- Tier-based product access control
- Integration with wallet infrastructure for payments

## Notification Infrastructure
Located in `polity_vault/wallet_credentials/`:
- Event-driven updates for wallet operations
- Status tracking across transaction lifecycle
- Multi-channel delivery system
- Custom payload handling per notification type

The system architecture emphasizes secure asset management, strict role separation, and automated workflow processing across interconnected financial services components.

$END$
