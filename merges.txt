a74e346441cbb508201e0a2e7ded9a69e4db2bfa|2025-03-12 17:42:00 +0200|Merge pull request #126 from bastion-coop/dev
ca3e6bf07d34aa123fec88aaec0cfc96723cd24e|2025-03-12 17:39:18 +0200|pull master
6cf37d914029eab05dea39ac1d94c97d393303c2|2024-12-24 16:51:47 +0200|Merge pull request #123 from bastion-coop/26595_hashicorp_free_tier
a8dd3a5efa8e63ba6d0312d8254724149cf29382|2024-12-23 22:50:47 +0200|Merge branch 'dev' of https://github.com/bastion-coop/Polity_MVP into 26595_hashicorp_free_tier
6369a052d8c33cac1aff8650c852e362e3b8489b|2024-12-06 00:46:10 +0200|Merge pull request #122 from bastion-coop/PLT-689
1ff37553d1be4195cb5e8394177e50807de0f1f6|2024-11-28 14:18:05 +0200|Merge pull request #121 from bastion-coop/PLT-689
c4a32c68673004ecd43d065f0565c5fb67bce079|2024-11-06 19:31:44 +0200|Merge pull request #119 from bastion-coop/PLT-690
e2ca21de614a910144fd79436763f6b7d9caa2d7|2024-11-06 15:43:38 +0200|Merge pull request #120 from bastion-coop/PLT-688
92c7078c60327681de383a4b7f459c980539161b|2024-10-29 17:23:03 +0100|Merge pull request #118 from bastion-coop/PLT-680/fe/chain_change
4ec3e98ef2e52e3e9cddae270135cb4bcea3418b|2024-10-29 12:47:12 +0200|Merge pull request #117 from bastion-coop/PLT-687/single_cosigner
38b70212768672a40a51dd0845e296b315cceca3|2024-07-30 13:29:06 +0200|Merge pull request #116 from bastion-coop/fix-co-signer-shutdown
1fc9ed6804be4e5e1652c0d961a4f7b4819f48a1|2024-07-30 11:54:02 +0200|Merge pull request #115 from bastion-coop/fix-co-signer-shutdown
7c9e93e136bb0608ef378711a678bfdd69aa68b1|2024-07-01 14:07:28 +0300|Merge pull request #113 from bastion-coop/hide-avalanche-asset
4415d31bf794820067c3c1319480922f520c625c|2024-06-27 12:45:58 +0300|Merge remote-tracking branch 'origin/master'
d707879cf9105ec73b72d4fbed9835bd9e3d0d5e|2024-06-19 17:33:12 +0300|Merge pull request #109 from bastion-coop/btc-transaction-bugfix2
d79de01002901e016b9f1b1e2f140c3ac81ed4bb|2024-06-18 19:59:32 +0300|Merge pull request #108 from bastion-coop/btc-transaction-bugfix2
3c7cb42b4b1f9ca4cbd0eb5438d67df555baa72f|2024-06-14 00:45:11 +0300|Merge pull request #106 from bastion-coop/dfns-btc-history-bugfix
582b9cd4b6f9365d85e7ced94d0076270e0e9865|2024-06-12 18:57:23 +0300|Merge branch 'master' of https://github.com/bastion-coop/Polity_MVP into dfns-btc-history-bugfix
a5c79bfe4e986f08b9476838d0ab1a66d0226fa3|2024-06-07 14:49:11 +0300|Merge pull request #104 from bastion-coop/fe-wc-unavailable-session-request-fallback
04157ac40906f53def39fd18b300a934db8da2bb|2024-06-03 18:24:01 +0300|Merge pull request #102 from bastion-coop/dfns-prod-migration
a00a588fffb849f2dcc870fc37c73d8f1aaaa310|2024-05-31 14:54:28 +0200|Merge pull request #101 from bastion-coop/PLT-653-automate-contract-signature-flow
02137f73776ccdf866ddf7bc42d868c3abccd9a9|2024-05-31 14:19:31 +0200|Merge pull request #100 from bastion-coop/PLT-653-automate-contract-signature-flow
bf4273fb47b8f771a0b4937fe30ac6f447b78f7f|2024-05-31 13:51:12 +0200|Merge pull request #99 from bastion-coop/PLT-653-automate-contract-signature-flow
4456f44ace587b62a64657ac2ee4c21141a08a77|2024-05-31 13:31:04 +0200|Merge pull request #98 from bastion-coop/PLT-653-automate-contract-signature-flow
59f01d3c91bcec96b1c001ed88510fe681ae1633|2024-05-29 16:48:31 +0300|Merge branch 'PLT-672-fe-install-wallet-fixes'
87dbb8e4e110c9e34ab64c5df6fea36a69308cd5|2024-05-29 15:44:08 +0200|Merge pull request #97 from bastion-coop/PLT-653-automate-contract-signature-flow
dc3f27e83896bb27b773abc6c1e11e7aaa3edd6a|2024-05-28 16:53:05 +0300|Merge branch 'master' of github.com:bastion-coop/Polity_MVP into PLT-672-fe-install-wallet-fixes
eb5c7e0fe9f44c35784c517066ae0e09b322a350|2024-05-28 15:52:34 +0200|Merge pull request #95 from bastion-coop/PLT-653-automate-contract-signature-flow
717e99423d0e3e72dba92c4de16d01eec2327a64|2024-05-28 12:50:19 +0300|Merge pull request #92 from bastion-coop/safeheron-walletId-bug
8ac023c04f255ab33b37741550d9737859c0f844|2024-05-28 11:43:21 +0300|Merge pull request #91 from bastion-coop/rate-limit-fix
8abe2e128a4c3dab87c1697391ac6afa16c3150a|2024-05-27 14:28:38 +0300|Merge pull request #90 from bastion-coop/safeheron-walletId-bug
8d8456b1b64a61da584422fb0e9df77f1dbbef73|2024-05-24 12:42:59 +0200|Merge pull request #75 from bastion-coop/PLT-653-automate-contract-signature-flow
a6242439ace9f5d6566fc61e252c3763c433d752|2024-05-22 01:09:46 +0300|Merge pull request #87 from bastion-coop/safeheron-walletId-bug
d25c6c21dc32544a6912928935745f6156876b07|2024-05-21 23:57:26 +0300|Merge pull request #86 from bastion-coop/safeheron-walletId-bug
583a234fb14b265aec5a2ec6ff8cf92681b137d9|2024-05-21 17:27:35 +0300|Merge pull request #85 from bastion-coop/safeheron-walletId-bug
31e294baf02d0ffb1ea55c008dd00c8aa1e448ce|2024-05-18 00:57:48 +0300|Merge pull request #84 from bastion-coop/dfns-personal-sign
0cb6c01a9592a02abc6add8d2dbf3ee9cccc301a|2024-05-16 23:16:48 +0300|Merge pull request #83 from bastion-coop/dfns-transaction-history
00154f339e58f2e36ab598ebd7c1e21e48644b79|2024-05-15 17:50:23 +0300|Merge pull request #80 from bastion-coop/PLT-660
236bf6120967db31b15bab6270503f4d4dac70c8|2024-05-14 21:54:44 +0300|Merge pull request #79 from bastion-coop/PLT-660
f77c6d4a40febc89de0038b415e0f61d027b7d91|2024-05-14 21:00:02 +0300|Merge pull request #78 from bastion-coop/dfns-scan-fix
3ef84ca9706c99090e0b9aa9cf86c67255031c4a|2024-05-14 15:48:43 +0300|Merge pull request #77 from bastion-coop/dfns-scan-fix
9a5592a06ce342572fc2ef90f2a3b887eaa18774|2024-05-09 13:00:37 +0200|Merge remote-tracking branch 'origin/master'
e9c42bfab457ba99faa7a8307e9452b7950db7ad|2024-05-01 16:38:50 +0300|Merge pull request #70 from bastion-coop/fix-bwi-dockerfile
7fbfddc7b26c6dcc8074b71079b38484a7b25169|2024-05-01 12:39:05 +0200|Merge pull request #69 from bastion-coop/fix-avatar-querying
2a7717cb8a0c0e2b5cd3c0d0eacdae953420fb0b|2024-05-01 12:14:59 +0200|Merge pull request #68 from bastion-coop/fix-avatar-querying
4182d96ae30362e1ad9a51a013f9ba2465794086|2024-05-01 11:57:16 +0200|Merge remote-tracking branch 'origin/master'
6ec8eb8b6627707b63ca0d7647c5456e66db737c|2024-04-29 16:24:13 +0300|Merge pull request #65 from bastion-coop/devops
e4487488c725fdb1b682a242e9e56498ee5b212e|2024-04-29 12:02:21 +0200|Merge pull request #64 from bastion-coop/fix-pdf-path
a805b4dbd0d0bfd476f938d90917db3ead91ec9a|2024-04-29 10:39:31 +0200|Merge pull request #63 from bastion-coop/fix-pdf-path
86e92782fa3d43ca25738c5f43fcc052854dae7b|2024-04-26 13:26:30 +0200|Merge pull request #62 from bastion-coop/fix-images-path
46b5e3e40b127aca60c4b6d35c690db52c20c626|2024-04-25 15:05:29 +0200|Merge pull request #61 from bastion-coop/fix-cors-problem
0d94b8ba94e5b232acdb765e2b26eab7d420558d|2024-04-25 14:59:04 +0200|Merge pull request #60 from bastion-coop/fix-cors-problem
eee46031c0b59ea549de4fd1604ee3a5dfff6434|2024-04-25 14:44:48 +0200|Merge pull request #59 from bastion-coop/fix-cors-problem
67495adfa800c532436d5b3404ca876fbdf4a66e|2024-04-25 14:36:14 +0200|Merge pull request #58 from bastion-coop/fix-cors-problem
9f4a5dcd2569b3f7b75426e9e5bc00e33bc86026|2024-04-25 14:14:30 +0200|Merge pull request #57 from bastion-coop/fix-cors-problem
ab429d3bc64a9d70a02c3b26b0bda23435669cf2|2024-04-25 13:28:22 +0200|Merge pull request #56 from bastion-coop/fix-cors-problem
1f66325a8e2db8cedd62eeddf1e548fec1ef09d5|2024-04-25 12:37:38 +0200|Merge pull request #55 from bastion-coop/fix-cors-problem
3f14ea0938a11cff3e88e3e46715604f624f5b12|2024-04-24 16:52:33 +0300|Merge branch 'master' of github.com:bastion-coop/Polity_MVP
7d75c89abdb0f22660ca93ab1809e829a71f14f3|2024-04-24 14:40:02 +0300|Merge pull request #53 from bastion-coop/create-transaction-bug
4dfe8d35001811f20b1b79fae89e1c7b78cdf262|2024-04-23 16:17:03 +0300|Merge pull request #52 from bastion-coop/btc-transaction-bugfix
b3b5c42c9af0098940b04888e7a655ab34532344|2024-04-23 00:15:54 +0300|Merge pull request #49 from bastion-coop/btc-transaction-bugfix
aefb4c9614897147afd7222aacf46be622f1cca0|2024-04-22 18:17:33 +0300|Merge pull request #48 from bastion-coop/btc-transaction-bugfix
9abc8f1a19aa57b33eef9faf34a6a1f3d79737d8|2024-04-22 17:52:53 +0300|Merge pull request #47 from bastion-coop/PLT-647-fe-refresh-token
e6a4e85925ce3a262729bbbf6f43685b853373c2|2024-04-22 12:13:11 +0300|Merge pull request #46 from bastion-coop/PLT-647-fe-refresh-token
6f0189568218957c8bc8c867e32ea0e3f37f85a2|2024-04-20 01:48:26 +0300|Merge pull request #43 from bastion-coop/btc-transaction-bugfix
b8d862d166dbb5e28c772f6468df9c6139d30f62|2024-04-20 01:46:52 +0300|Merge branch 'master' of https://github.com/bastion-coop/Polity_MVP into btc-transaction-bugfix
d901cc9b1d4ee1b5d55a13e4c6f2bdc1b9f7116a|2024-04-19 14:02:45 +0300|Merge pull request #41 from bastion-coop/PLT-637-wc-zapper-logo
0c961fcc0d4bd2e5dc4a76e6b6e34fff61d303fa|2024-04-19 13:55:15 +0300|Merge pull request #40 from bastion-coop/btc-transaction-bugfix
e4dd38dd821631d0188953ceb13f01d80c794200|2024-04-18 18:34:29 +0300|Merge pull request #39 from bastion-coop/dApps-fix
4a010d6db5f380280347d62ba762dd9631d5a9a0|2024-04-18 18:09:01 +0300|Merge pull request #38 from bastion-coop/dApps-fix
e1bf316b024440dd37aebf3a67e887acf808f9cb|2024-04-18 17:33:10 +0300|Merge pull request #37 from bastion-coop/dApps-fix
325ebb032dd008b47117094800a7c6673ed1e88d|2024-04-15 14:51:54 +0300|Merge pull request #36 from bastion-coop/markets-bug-fix
7a009a424636f49c3f877a1e15697d35ba97bc74|2024-04-12 12:21:28 +0300|Merge pull request #33 from bastion-coop/personal_sign_fix
a9d559a9febecb35421a3098d6914637a321c2be|2024-04-05 15:53:41 +0300|Merge pull request #27 from bastion-coop/fe_fractal_login_issues_hint
2cd069f459e99a45e126377caaec3d8ca7c531cf|2024-04-02 13:23:33 +0300|Merge pull request #26 from bastion-coop/auth-polity-vault-fix
8d461c8edcae4e8a5d68c8cf48b6fb939466b148|2024-03-27 11:20:28 +0200|Merge pull request #11 from bastion-coop/PLT-647-jwt-flow-updates
77412f7f71756cd52a6a4723f6356e464a75bc14|2024-03-25 12:54:48 +0200|Merge pull request #8 from bastion-coop/PLT-635,636
a307535e7cbc31fe4f624adca10e6c3251a20205|2024-03-25 12:42:45 +0200|Merge pull request #7 from bastion-coop/PLT-617
93dc5cab8cac9dd773ef9a81c084d73614908673|2024-03-15 12:20:37 +0200|Merge pull request #6 from bastion-coop/fix
73c82cd3f5add735eb3a1b09aea64dbd22a168c1|2024-03-14 13:06:37 +0200|Merge pull request #2 from bastion-coop/PLT-646
4f00ff8a9654186922d74391ec49b3133bb15dbd|2024-03-14 12:37:10 +0200|Merge pull request #4 from bastion-coop/PLT-631
4c011551f1bbb13cced0d8f95b41112363b989d5|2024-03-12 14:10:06 +0200|Merge pull request #3 from bastion-coop/PLT-648
5fe60d3fb4305719928aa133b764795d1d8a66ce|2024-03-12 13:44:13 +0200|Merge pull request #1 from bastion-coop/PLT-644
21dc1c4be80be99b3cb1c7be8940e1044de1fca6|2023-10-27 15:47:36 +0300|Merge pull request #692 from QuantuMobileSoftware/PLT-626
0d2cbca39b478c747850319638bc8c0b40667512|2023-10-26 15:14:46 +0300|Merge pull request #699 from QuantuMobileSoftware/PLT-600
faca2b70af299cdc24e08cac64dc10375a8d5a3a|2023-10-26 14:27:21 +0300|Merge pull request #698 from QuantuMobileSoftware/PLT-600
0dc30d7eeef903e8dccac1bd0ac00c96d8ba6bbc|2023-10-24 12:48:33 +0300|Merge pull request #688 from QuantuMobileSoftware/PLT-0-add-feature-flags
a26b44492f6c0c53c3eeabdb04ebfa6492555fa9|2023-10-23 09:50:43 +0300|Merge branch 'master' of github.com:QuantuMobileSoftware/Polity_MVP into master
65ce4e530ab7e8cf971d88232083c58773b6734c|2023-10-20 14:41:21 +0200|Merge pull request #686 from QuantuMobileSoftware/PTL-410-Rate-limiting
bccbd6bb9922773007450b1ed7ede5776547bfe7|2023-10-20 14:04:01 +0200|Merge pull request #527 from QuantuMobileSoftware/PTL-410-Rate-limiting
445c3667a9a11b59e2f425e9b1ee327d407d9303|2023-10-18 14:17:52 +0300|Merge pull request #680 from QuantuMobileSoftware/PLT-475
6313b9d56e756e586d45eb44a84aa39b65c75d33|2023-10-17 16:00:41 +0300|Merge pull request #678 from QuantuMobileSoftware/PLT-475
9df630554bdd1940f4d8aea5eb04fb41109ab66c|2023-10-17 15:18:20 +0300|Merge pull request #677 from QuantuMobileSoftware/PLT-475
0c97035533c5ab4bebf020a29a145b96e779493e|2023-10-13 15:44:40 +0200|Merge pull request #676 from QuantuMobileSoftware/PLT-601-store-auction-admin-devops
97231c4a2500ef7f96b468ca33954c16d6f45d00|2023-10-13 12:29:53 +0200|Merge pull request #675 from QuantuMobileSoftware/PLT-601-store-auction-admin-devops
1a4d839d877f79562281d3f8e05489b03bd38acc|2023-10-13 10:56:05 +0200|Merge pull request #672 from QuantuMobileSoftware/PLT-601-store-auction-admin-devops
99d5959c752615b48cb386a76ecc0add1a8f0815|2023-10-09 16:09:16 +0300|Merge pull request #669 from QuantuMobileSoftware/PLT-475
9650a5396d23df608bc2b47a533fa6144fe1d997|2023-10-09 15:24:39 +0300|Merge pull request #668 from QuantuMobileSoftware/PLT-475
dc57ad2615d75d0e8dd59a249b5fc080ac54cdaa|2023-10-09 13:15:27 +0300|Merge pull request #667 from QuantuMobileSoftware/PLT-475
476d7329b83f7f1d0c5f9c2953c552e68c4754ec|2023-10-06 11:57:56 +0300|Merge pull request #663 from QuantuMobileSoftware/PLT-555
05b3652e272f1afbe3b8386dcd2f8b9af070dac0|2023-09-21 17:37:53 +0300|Merge pull request #644 from QuantuMobileSoftware/PLT-0-modify-docker-compose-staging
9611efa36346f3ffa5eb2b4c7cb13a524f8a6cc1|2023-09-21 17:29:18 +0300|Merge pull request #643 from QuantuMobileSoftware/PLT-0-modify-docker-compose-staging
48774d7a1453646eb6d28e111197875c383df6bb|2023-09-21 15:45:01 +0300|Merge pull request #639 from QuantuMobileSoftware/fe_cleanup
4cb6458119cd0f7abca6b76c4b3d707c57bb6792|2023-09-21 14:26:37 +0300|Merge pull request #634 from QuantuMobileSoftware/PLT-0-modify-docker-compose-staging
9d06c47899b1190008bd1fd7b2dad91844f44f11|2023-09-21 14:25:25 +0300|Merge pull request #636 from QuantuMobileSoftware/PLT-0-change-method
d80e80075ef75546a9181d396a12225d5b864e7d|2023-09-21 12:18:12 +0300|Merge pull request #632 from QuantuMobileSoftware/PLT-579
0f1c39064ae5317699a794521f32731bef7ce295|2023-09-21 11:33:03 +0300|Merge pull request #631 from QuantuMobileSoftware/PLT-579
55089bf4e34f3203b9a0c4d045f4b80504d94c60|2023-09-20 14:08:31 +0300|Merge pull request #627 from QuantuMobileSoftware/PLT-579
a6d2908c3500647b7c1cd0f1e3a6427f6b4cbc93|2023-09-20 13:34:42 +0300|Merge pull request #626 from QuantuMobileSoftware/PLT-579
735edd38e5fbae1d2c3f9fde44c81b9db308186b|2023-09-20 10:15:05 +0300|Merge pull request #624 from QuantuMobileSoftware/PLT-566
5b9346bf0fc51f45023ac0216e902f8039724bef|2023-09-19 12:48:00 +0300|Merge pull request #623 from QuantuMobileSoftware/PLT-573
ed7f62db8bec0b20cf7e830d44c9f3daed002a8d|2023-09-18 14:00:34 +0300|Merge pull request #621 from QuantuMobileSoftware/PLT-573
2c009217d7bcfa28f386949c045073c14d4e561e|2023-09-18 13:50:34 +0300|Merge pull request #620 from QuantuMobileSoftware/PLT-535-remove-dummy-texts
47b36226735530aa5e293b5c721843a29cf8706f|2023-09-18 13:25:39 +0300|Merge pull request #619 from QuantuMobileSoftware/PLT-574-fix-styles-pdf-preview-btn
c03ba37b9d2a31c833b8e8a2c5c534cb83002a15|2023-09-12 18:08:32 +0300|Merge pull request #610 from QuantuMobileSoftware/PLT-0-version-postgres
c298b4f421228c51ecbb6df6135da2eda98bfe6b|2023-09-12 15:07:28 +0300|Merge pull request #608 from QuantuMobileSoftware/PLT-0-fix-main
3c7f5496bb24e3f58aa52e1ecdc005a63afb12dd|2023-09-12 13:12:50 +0300|Merge pull request #604 from QuantuMobileSoftware/PLT-0-add-logs
b15262241c86d447197c392dad632562eaff8fc0|2023-09-12 12:37:04 +0300|Merge pull request #602 from QuantuMobileSoftware/quick_fix_a_typo
17f4042dfa1a71f949ee99629c00ec0daedfb40a|2023-09-12 12:03:30 +0300|Merge pull request #603 from QuantuMobileSoftware/PLT-0-add-logs
0fd16c4ce3ccb6f0bdc69838cc557baffb6c9aca|2023-09-08 13:57:44 +0300|Merge pull request #590 from vakulyk/PLT-465
cae34b9de0879c603a7c457715c88ced86797d95|2023-09-08 12:05:13 +0300|Merge pull request #595 from QuantuMobileSoftware/PLT-560
4d31cebc2e5b7a11fad54ce76f87a1a9189a72e4|2023-09-08 12:04:12 +0300|Merge pull request #589 from QuantuMobileSoftware/PLT-488
04ec58da73d5c704e8a8fa55ffdb11206951ce99|2023-09-08 01:48:52 +0300|Merge branch 'master' into PLT-465
576bf413d2ae0319ebc59a757c933139cca748de|2023-09-07 22:29:18 +0300|Merge pull request #593 from QuantuMobileSoftware/PLT-569
51c6f3cb906acc2cdf798144697b5e4a0ca78850|2023-09-07 17:00:49 +0300|Merge pull request #592 from QuantuMobileSoftware/PLT-569
432e10243f74c1e4c6713a9db7af6cfba189b31a|2023-09-06 17:27:57 +0300|Merge pull request #588 from QuantuMobileSoftware/fe_cleanup
50fcb491f2c2a4a4b4b489833377272f1c921845|2023-09-06 14:43:30 +0300|Merge pull request #587 from QuantuMobileSoftware/PLT-488
1ca9d8533850d346ac4881809d52da13ddbbdca4|2023-09-06 11:46:34 +0300|Merge pull request #586 from QuantuMobileSoftware/PLT-490
05f08beb7cff2fea6553f0229164d71b69bc0a99|2023-09-04 16:34:33 +0300|Merge pull request #583 from QuantuMobileSoftware/PLT-488
80941d755d75cd1d06973ed69f90ed0a296042bf|2023-09-01 13:22:35 +0300|Merge pull request #576 from QuantuMobileSoftware/PLT-481
fc2892d973b2adad17c84f0e23b5be014f327f28|2023-09-01 13:13:06 +0300|PLT-481: merge master
1ce32b0567482b4d41e0a51c091615b54b4e8e0d|2023-09-01 13:00:19 +0300|Merge pull request #578 from QuantuMobileSoftware/PLT-553
067fdd88ea2ee194b16551e8d3c35cce24cacb82|2023-08-31 14:59:16 +0300|Merge remote-tracking branch 'upstream/master'
570f8f6731fdbf28d5ae9790c45768dbff638d60|2023-08-31 10:28:52 +0300|Merge pull request #574 from QuantuMobileSoftware/PLT-460
482b36e8fa7e74fdcc1f1c52d72d025c00141c4e|2023-08-30 11:57:20 +0300|Merge pull request #572 from QuantuMobileSoftware/PLT-469-fix-response
2461faf5b53c80c8e20d4c73da8308a0b7c26f56|2023-08-29 15:10:47 +0300|Merge remote-tracking branch 'upstream/master'
e5e87d3b33d09bb80a13ebd2e2274f9cb2f224d5|2023-08-29 12:48:25 +0300|Merge pull request #567 from QuantuMobileSoftware/PLT-459
5186bd42f588eb13856ad21a977dd2cfb0d2d481|2023-08-23 13:03:29 +0300|Merge pull request #565 from QuantuMobileSoftware/PLT-486-fix
c395d53c9197550e946f76d3546f98df1ceb99bc|2023-08-23 12:19:59 +0300|Merge pull request #564 from QuantuMobileSoftware/PLT-486-fix
a5c750e0a4645f79087cf53d349365fb7212b29c|2023-08-23 11:39:31 +0300|Merge pull request #563 from QuantuMobileSoftware/PLT-486
822e5a3b1578735e7a033642e13eb83d76c7ee8a|2023-08-23 11:24:56 +0300|Merge pull request #562 from QuantuMobileSoftware/PLT-551
dfe0e8f52654a409f467f8bf788cf39b1922ba5b|2023-08-23 10:58:17 +0300|Merge remote-tracking branch 'upstream/master'
507cc4038c5813a9b3638b622d010d677f0101a6|2023-08-23 10:31:27 +0300|Merge pull request #561 from QuantuMobileSoftware/PLT-485
13dee628cca2308cceafeaf32f6f5a1c9b94b483|2023-08-22 16:48:10 +0300|Merge pull request #560 from QuantuMobileSoftware/PLT-485
c56630bdf1f820829a385ad027dd5b6333c15002|2023-08-22 13:33:55 +0300|Merge pull request #557 from QuantuMobileSoftware/PLT-485
95226a5cc92feb5f5e237507c49739259de84470|2023-08-22 12:52:05 +0300|Merge remote-tracking branch 'upstream/master'
2c1f2d6683f5e1f6da6b5695199530d6cc95a724|2023-08-22 12:49:06 +0300|Merge pull request #556 from QuantuMobileSoftware/PLT-465
d230599b6464fd3e6684f538098d60d8a0e0fbd5|2023-08-22 12:26:21 +0300|Merge pull request #555 from QuantuMobileSoftware/fe_cleanup
c8d7ea309840c8a740354c7a73d2efc9da641e4e|2023-08-22 12:12:58 +0300|Merge remote-tracking branch 'upstream/master'
d30866b8eda72b2a7d3a23ee8239e7115fdf3b11|2023-08-22 11:53:09 +0300|Merge pull request #513 from QuantuMobileSoftware/PLT-508
ce32268c57602b3f81c0d321f639eea265ad429a|2023-08-22 11:27:45 +0300|Merge pull request #554 from QuantuMobileSoftware/fe_cleanup
a020fc86ce1c2e18ff14bb1c8af837f477a5b3d2|2023-08-22 10:30:55 +0300|PLT-508: merge master
e9ff3f7a1e291c7b2743c3fc6962318651249677|2023-08-21 16:46:05 +0300|Merge remote-tracking branch 'upstream/master'
3cbd85bc0bc5dceea252aaa4a683933d3cfef593|2023-08-21 16:06:22 +0300|Merge pull request #552 from QuantuMobileSoftware/PLT-469
be96fab97294d6d78376ddcdbec8f50959ba6cc2|2023-08-21 15:46:24 +0300|Merge pull request #551 from QuantuMobileSoftware/PLT-469
5f81469d423fd8df79f665e09c240f7e1df029e6|2023-08-21 14:04:34 +0300|Merge remote-tracking branch 'upstream/master'
249199a11716826ee5ae7ef08f413645a20598bb|2023-08-21 13:41:14 +0300|Merge pull request #548 from QuantuMobileSoftware/PLT-469
033d941e3ba24d1d03fdea53097181c22a84215f|2023-08-21 12:01:31 +0300|Merge remote-tracking branch 'upstream/master'
1f63717f69335531c842879826e7ce7e1efaf341|2023-08-18 14:22:25 +0300|Merge pull request #539 from QuantuMobileSoftware/PTL-511-admin-ui-domain
93f23b9734f545a33ca37d28cdca7575f305254e|2023-08-18 10:59:06 +0300|Merge pull request #541 from QuantuMobileSoftware/PLT-539-fe-error-and-loading-handlers
1d7fadf8274ef507dfae568f21cbd862bb71e060|2023-08-17 11:33:39 +0300|Merge pull request #538 from QuantuMobileSoftware/PLT-469
8e06b16d4407b27e799c47c0c3e9231696dbc1f5|2023-08-17 09:52:33 +0300|PLT-469: add lots connection endpoint
a1d3d666803715fdb156afe512f4811b207cb9cf|2023-08-16 13:47:17 +0300|Merge pull request #535 from QuantuMobileSoftware/PTL-511-admin-ui-domain
f83ba96c6c55e5a2f77f6257d5e42faae4f93cae|2023-08-11 13:56:37 +0300|Merge pull request #530 from QuantuMobileSoftware/PLT-485
21a1db5a119d2348374c19a5c1feb9a9329a517e|2023-08-10 13:34:57 +0300|Merge pull request #529 from QuantuMobileSoftware/PLT-530-fix-redis
986194a47bcb81106702192c87dab46d3adf930b|2023-08-09 21:55:02 +0300|Merge pull request #528 from QuantuMobileSoftware/PLT-530-fix-redis
4e13fa6b5dfa796127959fcd8060c3fad3bb0e99|2023-08-08 13:01:39 +0300|Merge pull request #526 from QuantuMobileSoftware/PLT-461-add-skeleton
be15d88111425fb85a8856b956d3e42ce6e349a7|2023-08-03 16:01:36 +0300|Merge pull request #521 from QuantuMobileSoftware/PTL-511-admin-ui-domain
758cacf93518ad43051dd9f7b2514e1b689b5c2b|2023-08-03 15:31:53 +0300|Merge pull request #520 from QuantuMobileSoftware/PTL-511-admin-ui-domain
a161d5c6c55069608fada3c7fff0a4b1746d6325|2023-08-03 12:02:48 +0300|PLT-508: merge master
034a33db5a77fd10c01a074bd30a25e6e93aa7fa|2023-08-03 11:50:21 +0300|Merge remote-tracking branch 'origin/master' into master
a90b89ee5dd99b0f2c0ea3e2d5c5f28e2ec1f794|2023-08-02 12:33:00 +0300|Merge pull request #515 from QuantuMobileSoftware/PLT-509
dfeef24ab45c26997bcef27eeee61e18bdfd8da5|2023-07-31 15:45:23 +0300|Merge branch 'fe_cleanup' into master
10e403e4ce0da1499ab2d7a4a3f180388b5a6e7e|2023-07-31 11:14:53 +0300|Merge branch 'master' of github.com:QuantuMobileSoftware/Polity_MVP into master
32231458763ff9324614377fc96c695b2ec3ab0b|2023-07-27 19:06:04 +0300|Merge pull request #508 from QuantuMobileSoftware/23064-add-balances
77f13db16b09f371528bbecf27ccb4cc42858261|2023-07-27 18:31:28 +0300|Merge branch 'master' of github.com:QuantuMobileSoftware/Polity_MVP into master
c877689fc305a5a361ebcfa005c9db2744119b63|2023-07-27 16:33:16 +0300|Merge branch 'master' into fe_cleanup
4f81a7ed46294b2f6f454aefe8bc3db605286256|2023-07-27 16:21:20 +0300|Merge pull request #504 from QuantuMobileSoftware/PTL-500-Create-Prod
2d5eb8f9cc6f866eda6c53e0d82d2a5640d6dc28|2023-07-27 15:56:35 +0300|Merge pull request #503 from QuantuMobileSoftware/23064-add-balances
b80bd402970996ae5ec5968ad09516ee4cdb9031|2023-07-27 13:25:24 +0300|Merge pull request #501 from QuantuMobileSoftware/fe_cleanup
0532049d31323c7b8f21a87b3edd1545dc5637cf|2023-07-26 16:33:16 +0300|Merge pull request #490 from QuantuMobileSoftware/23064-add-balances
8159c33f2af38a71ade76711a2c9d32a5dfa6c09|2023-07-26 12:53:37 +0300|Merge pull request #488 from QuantuMobileSoftware/PTL-500-Create-Prod
56c2057f174532fc3f7a8fe720590aee6df60445|2023-07-24 15:39:47 +0300|Merge pull request #485 from QuantuMobileSoftware/PLT-502-fix-migrations
8c8e631ccdaa5f6117a1b796d6a60618c9fa7093|2023-07-21 17:25:03 +0300|Merge pull request #482 from QuantuMobileSoftware/PLT-502
79c76328d79640c5c573b32bbc5ce16a77e9db68|2023-07-21 16:30:45 +0300|Merge pull request #481 from QuantuMobileSoftware/PLT-502
64b4573198983c2d892e85e102b3dd674a19682e|2023-07-21 15:45:29 +0300|Merge pull request #480 from QuantuMobileSoftware/PLT-502
16227a3fea7fcdecd6008507301a1d38a5e88ff3|2023-07-20 18:48:17 +0300|Merge branch 'master' of github.com:QuantuMobileSoftware/Polity_MVP into master
1f3946b1c08162c8183e37ee919e9a62b593534c|2023-07-20 18:16:24 +0300|Merge pull request #477 from QuantuMobileSoftware/PTL-500-Create-Prod
37980a2b8377f4822ecda91b5bfcd82bed2f3256|2023-07-20 12:17:43 +0300|Merge pull request #476 from QuantuMobileSoftware/PTL-500-Create-Prod
f32703a5bf8e80957c3e96e58c68699c0ae7b65f|2023-07-20 11:01:56 +0300|Merge pull request #475 from QuantuMobileSoftware/PTL-500-Create-Prod
9f381c1dde4c1821b00bdceafba62aec1b62d47b|2023-07-13 14:10:54 +0300|Merge pull request #470 from QuantuMobileSoftware/PLT-406-return-list
52bc467f5b6c6f5b8c5654042d941e79cc47e080|2023-07-13 13:52:30 +0300|Merge pull request #469 from QuantuMobileSoftware/PLT-495
6f815a636c58acb780f9e7305aaea561a172eb53|2023-07-12 12:57:12 +0300|Merge pull request #466 from QuantuMobileSoftware/PLT-406-fix
6dbf0ca94bb1ca95535c39f49e4766b9d3f16a13|2023-07-12 11:20:41 +0300|Merge pull request #465 from QuantuMobileSoftware/PLT-406-fix
15efc20ff7893354fa0734329c5ee56ba1d9bd2c|2023-07-12 11:03:21 +0300|Merge pull request #464 from QuantuMobileSoftware/PLT-406-fix
2c5d3f5e7e69d1b842563c691017e2fdc066e24e|2023-07-10 13:08:15 +0300|Merge pull request #462 from QuantuMobileSoftware/PLT-453
ec6d5eafd9237642e3dc752b8c5b320128aff28e|2023-07-05 14:32:11 +0300|Merge pull request #455 from QuantuMobileSoftware/PTL-410-Rate-limiting
ebf016ec7c3405a636fb83197b3d24a278868f3a|2023-07-04 12:24:22 +0300|Merge pull request #452 from QuantuMobileSoftware/PLT-400-refactor-pagination
0eaf6bb4ccbb06dc7af2ffae6cbb8303c4e42dad|2023-06-30 16:44:50 +0300|Merge pull request #449 from QuantuMobileSoftware/PTL-499-provision-in-lan
ca6b8b5ea0c121ff8379448cee3abff569fa5400|2023-06-30 14:58:45 +0300|Merge pull request #448 from QuantuMobileSoftware/PTL-499-provision-in-lan
7c40af231e28ba72593ea11946110bb4557fd72a|2023-06-30 14:13:47 +0300|Merge pull request #447 from QuantuMobileSoftware/PLT-399-fis-swagger
023ff77bf738f40aa90121a1b5109793306e97a2|2023-06-30 10:47:31 +0300|Merge pull request #445 from QuantuMobileSoftware/PTL-499-provision-in-lan
75c084ad395b7f88e2daac65767be95a278536ed|2023-06-27 12:54:37 +0300|Merge pull request #443 from QuantuMobileSoftware/PLT-433-status-validation
24161cc889d51693df42db94eb015e97178d87d1|2023-06-26 14:48:52 +0300|Merge pull request #441 from QuantuMobileSoftware/PLT-398-fix-header
303790b6239891ce1eac6188571ff5463dacfaa2|2023-06-23 12:52:56 +0300|Merge pull request #439 from QuantuMobileSoftware/PLT-447
7f91a4b2df0ea1177a78d2be29a287ef7ce39aa0|2023-06-23 11:50:01 +0300|Merge pull request #438 from QuantuMobileSoftware/PLT-447
5aa1eb5df3704f3a321fb9c93dc17586f648e529|2023-06-22 16:32:08 +0300|Merge pull request #437 from QuantuMobileSoftware/PLT-433
dc19e77650e248e6a14fa1b6cd37c5623695c6b5|2023-06-22 15:32:42 +0300|Merge pull request #436 from QuantuMobileSoftware/PLT-433
4767cce564e2b456ba40d4d2b1782111b8e12606|2023-06-21 16:16:33 +0300|Merge pull request #434 from QuantuMobileSoftware/PLT-433
06fd5e969715df8aa72207fa4f0ff23a46808f5f|2023-06-21 16:00:41 +0300|Merge pull request #433 from QuantuMobileSoftware/PLT-433
1a5a6c80be06719d71ef230d3a75ee359f703682|2023-06-21 14:23:55 +0300|Merge pull request #432 from QuantuMobileSoftware/PLT-408
99d19c6e203c894dd2d2388313b88cb99c591565|2023-06-21 11:29:11 +0300|Merge pull request #431 from QuantuMobileSoftware/PLT-444
8117c4e5eea89dc37bae01558718c198521caa84|2023-06-21 11:16:35 +0300|Merge pull request #429 from QuantuMobileSoftware/PLT-406
bf7dcf010f1107636eac23a68c0e98a4390d4e68|2023-06-21 11:14:47 +0300|PLT-444: merge master
e0b741d78b39cb0b2d552e237d2e544523f7ac08|2023-06-21 10:18:17 +0300|Merge pull request #430 from QuantuMobileSoftware/PLT-444
7e45a23f35efd818a642de25c5b0ac9948e0f91d|2023-06-19 14:45:50 +0300|Merge pull request #426 from QuantuMobileSoftware/fe_cleanup
bee0d971f8c1fc950dde9276127582e8456ef8a4|2023-06-16 16:01:20 +0300|Merge pull request #424 from QuantuMobileSoftware/PLT-437
979ede5240bb3898935335ba7e052b9097f90da7|2023-06-16 13:21:22 +0300|Merge pull request #422 from QuantuMobileSoftware/PLT-439
637bf4b784a38102c539846771d9da31cec4d7d4|2023-06-16 13:18:33 +0300|Merge pull request #416 from QuantuMobileSoftware/PLT-433
a09737914f4cc4c3811061a914758afa53302963|2023-06-15 17:17:31 +0300|Merge pull request #421 from QuantuMobileSoftware/PLT-398
7886cbf3033e267d4986313e5c8734922baa3e8c|2023-06-15 14:37:19 +0300|Merge pull request #420 from QuantuMobileSoftware/PLT-437
6c1223515eda1133d23876d9788cd02f8224def9|2023-06-15 12:33:24 +0300|Merge pull request #419 from QuantuMobileSoftware/PLT-398
cad0e0d065fc2a6ab964cb1b98c906c2a95b4046|2023-06-15 12:29:50 +0300|PLT-433: merge master
5470bd729a0d24655e0cb369607f53b639da8005|2023-06-15 12:21:24 +0300|Merge pull request #418 from QuantuMobileSoftware/PLT-437
6d8aceae466ebd5e990c909ec549d50deb49fe3f|2023-06-15 10:06:27 +0300|Merge pull request #417 from QuantuMobileSoftware/PLT-423
f1612a0d075d1b35db3b39fed631aeff87577634|2023-06-14 14:24:09 +0300|PLT-189: merge master
392ecef18b48153c40bc1cfa169a0e164ac0522c|2023-06-14 12:40:30 +0300|Merge pull request #409 from QuantuMobileSoftware/PLT-399
59325fcd8c9a4ae3df4b72a8aa56254d4b930cd7|2023-06-12 14:14:04 +0300|Merge pull request #412 from QuantuMobileSoftware/PLT-423
db514011457c9ccb1a95cc181d21424a1fb9574e|2023-06-12 11:15:42 +0300|Merge branch 'master' into PLT-399
f00d24a32daa6a8bc4ff7ed1e2d50c73c536042b|2023-06-09 11:38:18 +0300|Merge pull request #410 from QuantuMobileSoftware/PLT-423
8e5f5318d17aa66cf5f8ef67a1369e6963310267|2023-06-02 10:31:46 +0300|Merge branch 'master' of github.com:QuantuMobileSoftware/Polity_MVP into master
d095283a3784cea1ecfa95d23afb6b2c472ad4cf|2023-05-30 11:23:02 +0300|Merge pull request #379 from QuantuMobileSoftware/PLT-381
4c370769a773d55020eaa33e3a3124a78faba403|2023-05-30 11:13:28 +0300|Merge branch 'master' into PLT-381
2c4895900319c1e00a701557b75e91044a02bb7f|2023-05-30 11:05:45 +0300|Merge branch 'master' into PLT-381
ca4338e7f89aee7a4e5d1dac403965d6d0d8dd34|2023-05-29 10:13:37 +0300|Merge pull request #366 from QuantuMobileSoftware/PLT-378
5783e675163f47c2b55d45f3f8b7369ea81ef2dc|2023-05-12 12:21:19 +0300|Merge pull request #378 from QuantuMobileSoftware/PLT-383-rate-limiting-and-api-gateway-additions
f5600cc45641b7b443a2b8a91a1c34bdfb1d3d67|2023-05-10 16:32:16 +0300|Merge pull request #374 from QuantuMobileSoftware/PLT-383-rate-limiting-and-api-gateway-additions
bb55274cdba292d6eb6da27acd962fa7727b5e91|2023-05-10 15:19:28 +0300|Merge pull request #373 from QuantuMobileSoftware/PLT-383-rate-limiting-and-api-gateway-additions
0c5ea9621a63adea869bbd332ccb2cf1391596db|2023-05-10 15:01:13 +0300|Merge pull request #372 from QuantuMobileSoftware/PLT-383-rate-limiting-and-api-gateway-additions
bd4457223e3ccc7e9ee85dcb4bd6c3fd0c216281|2023-05-10 14:23:55 +0300|Merge pull request #371 from QuantuMobileSoftware/PLT-383-rate-limiting-and-api-gateway-additions
2e471c9feae53e25d6ebdf675887549ddedb5172|2023-05-05 11:24:09 +0300|Merge remote-tracking branch 'origin/master' into master
53544efe1341dfcf0ea0fa82c6e04312aeb48194|2023-05-04 16:23:11 +0300|Merge pull request #367 from QuantuMobileSoftware/PLT-382
9ec83144ec30311d564e14004d86218ca2b2bf36|2023-05-03 14:46:00 +0300|Merge pull request #365 from QuantuMobileSoftware/fe-pre-demo-patches
b3bc73f7931aacde012a2109aa713b5703e41a78|2023-05-01 16:54:36 +0300|Merge pull request #343 from QuantuMobileSoftware/PLT-269
6785b69999de09875a65584c5ebd5090f1123359|2023-05-01 13:28:14 +0300|Merge pull request #341 from QuantuMobileSoftware/PLT-373
db54210d5e13caf6362b0378dab81cf421f6dea6|2023-05-01 13:23:18 +0300|Merge pull request #335 from QuantuMobileSoftware/PLT-346
b530cad5dbebf58939df3f8964aa1169597f5858|2023-04-28 14:15:14 +0300|Merge pull request #330 from QuantuMobileSoftware/PLT-352
a8f668d302898efbf22682824061ee0f66f21b33|2023-04-25 15:11:55 +0300|Merge pull request #328 from QuantuMobileSoftware/PLT-350
3b6149f23e1a8befc4ae1012dbd00911e50ec029|2023-04-25 13:51:17 +0300|Merge pull request #327 from QuantuMobileSoftware/PLT-350
07768879ec739e6b01e1a550eaf73179b8ec79f6|2023-04-25 12:23:05 +0300|Merge pull request #326 from QuantuMobileSoftware/PLT-265
30f1d62dba70e85ea2cadb852d2017f4e6e1546c|2023-04-24 17:10:18 +0300|Merge pull request #325 from QuantuMobileSoftware/PLT-278
821d4d283a0996ba210a7c790d15b2e8161b78c0|2023-04-21 16:56:34 +0300|Merge pull request #322 from QuantuMobileSoftware/PLT-351
ce7e9d7738388080e8e71fdfad45c91f06e4135c|2023-04-21 14:28:35 +0200|Merge pull request #323 from QuantuMobileSoftware/PLT-347-Bug-avatar-images
a67625839b6ec72fd49cec7ffea5a99e3e4746cf|2023-04-21 13:21:35 +0200|Merge pull request #321 from QuantuMobileSoftware/PLT-347-Bug-avatar-images
a978f7f1aea2120e5ff5f3d5bae0e8db911efd9e|2023-04-21 14:20:32 +0300|Merge pull request #320 from QuantuMobileSoftware/PLT-301
545aa2750c5ef86c28a55ff9c78dc6ac14975710|2023-04-20 18:25:22 +0300|Merge remote-tracking branch 'origin/master'
6f0fe2a36767636ba2f6818624d5998ab2d191ef|2023-04-20 18:05:18 +0300|Merge pull request #317 from QuantuMobileSoftware/PLT-273-fe-handle-proxy-redirects
58be9ae378286db6a77249fb404d77db98c94278|2023-04-19 19:40:41 +0300|Merge branch 'master' into PLT-301
cbf9c8417690b38d42bae5d226e5ebb394d5763c|2023-04-19 19:06:33 +0300|Merge pull request #312 from QuantuMobileSoftware/PLT-363
c128461fa399d3295b74c770f63123bb4d8995ae|2023-04-19 17:33:49 +0300|Merge branch 'master' into PLT-301
61061c9e5e09302d60cf438e58f64769eda72a63|2023-04-19 14:27:55 +0300|Merge pull request #305 from QuantuMobileSoftware/PLT-272
559e5680e6a92b1fbdde506bba2bc6befd7fd7ab|2023-04-19 13:53:31 +0300|Merge pull request #309 from QuantuMobileSoftware/fe_fix_bc_address_validation
b78e92ec40065ffc7672b7c9272277a4447fc70b|2023-04-18 14:07:04 +0300|Merge pull request #306 from QuantuMobileSoftware/PLT-360-fe-privacy-policy-link
0299531376ce27b36ddd3b457564d1aeff436b54|2023-04-14 12:52:19 +0200|Merge pull request #303 from QuantuMobileSoftware/PLT-355-Fix-Co-Signer
dcaacd8781861a848d19fec1e7f4e9546ac4e5e1|2023-04-14 12:02:13 +0300|Merge branch 'master' into PLT-272
e73aaa133a56de3e8b1582519b4c19ff9b44d65a|2023-04-14 10:50:36 +0200|Merge pull request #302 from QuantuMobileSoftware/PLT-0-fix-redis
5af41d72d927949c7358c9986f3bca226432281f|2023-04-14 10:47:36 +0200|Merge branch 'master' into PLT-0-fix-redis
9302677fc29d50a4a6a493a6ac46f00aa384787f|2023-04-14 10:44:07 +0200|Merge remote-tracking branch 'origin/PLT-0-fix-redis' into PLT-0-fix-redis
318f0a6686fd43a4cdf0f88f851114b432340460|2023-04-14 10:55:11 +0300|Merge pull request #300 from QuantuMobileSoftware/PLT-356
c1f7e9606bccca9ad23f259001b8a39e35334240|2023-04-13 14:54:23 +0300|Merge pull request #292 from QuantuMobileSoftware/PLT-351
580d022a5a5d036e1e2b8c3ccf96d76a0f935cc7|2023-04-13 10:31:36 +0200|Merge pull request #288 from QuantuMobileSoftware/PLT-344-Badges
5e76975240925fc807a7c1ccd70d5b04dd94a73c|2023-04-13 11:11:42 +0300|Merge pull request #294 from QuantuMobileSoftware/fe_cleanup
fdd1c46ac14023d2360958707be90f454408589b|2023-04-12 13:37:46 +0300|Merge pull request #285 from QuantuMobileSoftware/PLT-338
b688e964a21ce9ba7c1cbc5b5a8f3e19c805c2ef|2023-04-12 13:36:54 +0300|Merge pull request #287 from QuantuMobileSoftware/PLT-346
73b03919cc52ab429dbff6325b3e6b00e98e933e|2023-04-11 10:49:55 +0300|Merge pull request #280 from QuantuMobileSoftware/PLT-342
8a8ae6432bb67faf8df17ae70ae8e1c1996f2671|2023-04-10 15:21:31 +0300|Merge pull request #277 from QuantuMobileSoftware/PLT-345-fe-invalid-signup-error-notification
eb9a4f42fc69511466048f727df1a394d0b43ef6|2023-04-10 14:36:02 +0300|Merge pull request #276 from QuantuMobileSoftware/PLT-245-fe-safeheron-assets
5383293e677e7778cffd728701d4ad6fa867bfc6|2023-04-10 13:25:08 +0300|Merge pull request #274 from QuantuMobileSoftware/PLT-277
813cfa987dc5a74f84dfbfbdecbb9e6017c565da|2023-04-07 17:44:53 +0300|Merge pull request #271 from QuantuMobileSoftware/PLT-341_fe_fix_trailing_slash_normalizer
d85245f670ff52116458e0e750e5e47ca87de15e|2023-04-07 16:14:33 +0300|Merge pull request #270 from QuantuMobileSoftware/PLT-277
359048e5867359eafc5da585ec86659d7d2407ae|2023-04-06 15:23:41 +0300|Merge pull request #257 from QuantuMobileSoftware/PLT-333
e85b72ae8fc93ca3290832c6f55687a1d3944cc1|2023-04-06 15:23:25 +0300|Merge pull request #250 from QuantuMobileSoftware/PLT-329
002979a3c3bb04828340ffa63149d70d03ef3147|2023-04-06 15:23:07 +0300|Merge pull request #258 from QuantuMobileSoftware/PLT-294
952bb4eb8856390daead0f03170dfbede744a209|2023-04-06 13:35:40 +0300|Merge pull request #260 from QuantuMobileSoftware/PLT-335
7d7cb52056b41fab119b0eddadf0ea5ec3495347|2023-04-03 16:33:35 +0300|Merge pull request #256 from QuantuMobileSoftware/PLT-238-tests_for_500_error
366e36d3ba7ae8f10a5a44a0980a4522c4126508|2023-04-03 16:31:51 +0300|Merge branch 'master' into PLT-238-tests_for_500_error
beda5af6ea62bf96849a7551b1f3f1b022348bb1|2023-04-03 16:26:58 +0300|Merge pull request #255 from QuantuMobileSoftware/PLT-238-500_error_InvalidPath_if_record_is_missed_on_the_vault
b8574e3dad5f3a25447abcb4d731060674c88ead|2023-04-03 16:23:37 +0300|Merge branch 'PLT-238-500_error_InvalidPath_if_record_is_missed_on_the_vault' into PLT-238-tests_for_500_error
e7d4fd88998554baa8d3dce16439e76d2121b42d|2023-04-03 15:34:41 +0300|Merge pull request #254 from QuantuMobileSoftware/PLT-332
fbaf43d6b364559b0bb6701f232a054d9e11a99b|2023-03-31 11:47:19 +0300|Merge pull request #249 from QuantuMobileSoftware/polity_vault/fix_jwt_uuid
cd3657bbe69a0e0c15f9b6395e7bd5b724f4c755|2023-03-30 18:23:58 +0300|Merge pull request #246 from QuantuMobileSoftware/PLT-277
b4bbe9b2697d592ae9da7ed7fd98ab087b6172e8|2023-03-30 18:04:49 +0300|Merge pull request #247 from QuantuMobileSoftware/polity-tests/fix_json_decode_error
806e857f814b2c866b1a11633ea4f78bc0fc5c99|2023-03-30 17:51:56 +0300|Merge pull request #245 from QuantuMobileSoftware/feature/fix_test_login_tests
fabace92aa2f74b22a9f34689d631128e35071a4|2023-03-29 17:04:00 +0300|Merge pull request #239 from QuantuMobileSoftware/PLT-318
86ba5698e9d12cc1fe9918562d59b573172842db|2023-03-29 13:23:20 +0300|Merge pull request #244 from QuantuMobileSoftware/feature/fix_test_login_tests
79d3938e13ad1f6f51b0ecfd7244e0ba25a06f92|2023-03-29 12:57:43 +0300|Merge branch 'master' into PLT-318
5fb3e063df662e87362eabf23c72abf862fe9af2|2023-03-29 12:04:11 +0300|Merge pull request #242 from QuantuMobileSoftware/PLT-323
260d81eebd0ea6bfcc829275957dc99356d0a01d|2023-03-29 11:29:20 +0300|Merge pull request #243 from QuantuMobileSoftware/PLT-311_500_error_if_JWT_doesn't_contain_uuid_property
6fd8ea64bf1beaee32a0fc71e9cb5d166c064919|2023-03-29 11:16:18 +0300|Merge pull request #241 from QuantuMobileSoftware/feature/polity_vault-remove_username_from_filters
743d0cb10be5378c463ebfba106fad1ae02dd3ce|2023-03-29 11:16:01 +0300|Merge pull request #240 from QuantuMobileSoftware/tests/debug-test-login
b195d856582381341314aef44dceeeedfcc7ebba|2023-03-28 14:22:36 +0300|Merge pull request #238 from QuantuMobileSoftware/feature/fix-auth-tests
c05f1d2a4721b3a81c524aa64a83dc066b4da8eb|2023-03-28 13:41:42 +0300|Merge pull request #237 from QuantuMobileSoftware/PLT-294
bfacd86b406aaaf69aaaec2a347c451183f3c16e|2023-03-28 13:37:56 +0300|Merge branch 'master' into PLT-294
1b9f89bc3f6e62bfbc4837f41e1f481505d38889|2023-03-27 19:18:45 +0300|Merge pull request #233 from QuantuMobileSoftware/PLT-320-add-db-waits
e0c9c86a57bacf3630fb779dad834a229c68c5ab|2023-03-27 18:34:47 +0300|Merge pull request #232 from QuantuMobileSoftware/PLT-294
8d4a2573a05027fc062e07118f3ebee965edb6a3|2023-03-27 18:10:00 +0300|Merge branch 'master' into PLT-294
6ac8b48198c6d792149d9d9fcd8bfcbb848a6f28|2023-03-27 16:27:31 +0300|Merge pull request #229 from QuantuMobileSoftware/PLT-308
4a07c006dad290e5e18a6190835b1e04aae0232b|2023-03-27 10:47:44 +0300|Merge pull request #228 from QuantuMobileSoftware/PLT-304-table-view-for-wallet-requests
0a68f441b52558bb52d3fb399bdc328dab2c60fc|2023-03-27 10:47:30 +0300|Merge pull request #227 from QuantuMobileSoftware/PLT-307-tests_for_key_exchange
9de741dcd88722fca35785801f174dfc07288fb6|2023-03-24 17:39:26 +0200|Merge branch 'master' into PLT-307-tests_for_key_exchange
472a507f7c6317dc61a7a3054d373b49e13e658c|2023-03-24 12:46:03 +0200|Merge pull request #226 from QuantuMobileSoftware/PLT-310-tests_tokens_refactoring
0d17f3b1e3cfe6ab1e19b63f8687130562a54481|2023-03-17 16:35:42 +0200|Merge pull request #216 from QuantuMobileSoftware/PLT-296
d4b1a27b21e3ca6b65fbd1db9afe9400a806694a|2023-03-15 16:19:04 +0200|Merge pull request #213 from QuantuMobileSoftware/PLT-282_tests_for_wallet_details
9639178e4aafa7f141078cb7d5d19b079fa95b91|2023-03-15 10:25:45 +0200|Merge pull request #212 from QuantuMobileSoftware/PLT-279_tests_notifications_payment_received
43a1366c9a53e2e02ba19ce13133fe89e0a9a8c0|2023-03-14 11:00:59 +0200|Merge pull request #210 from QuantuMobileSoftware/PLT-103-fix-get-asset-address
95995c856e4fe120a18d68c6749a089695fa4cb1|2023-03-13 17:57:44 +0200|Merge pull request #208 from QuantuMobileSoftware/tests/fix-test-rurn
47a17d934198c6657664a9e6d61d898b4ed65b35|2023-03-13 16:05:25 +0200|Merge pull request #207 from QuantuMobileSoftware/PLT-195
aff1cafc66d19a0182e069745c1fe4508f3c02f3|2023-03-13 14:52:27 +0200|Merge pull request #206 from QuantuMobileSoftware/PLT-195
921dfe4045fb39c2f715e8a2a477cef21b0c09a0|2023-03-13 13:17:32 +0200|Merge pull request #205 from QuantuMobileSoftware/PLT-103-fix
39117e9f43de8ef44d6d38a24187e3fd33709c95|2023-03-10 16:58:13 +0200|Merge pull request #204 from QuantuMobileSoftware/PLT-144
98ae5e5521b2bcf3661e9a19db0cfc5de807a72f|2023-03-10 16:16:51 +0200|Merge pull request #203 from QuantuMobileSoftware/feature/errors_fiixes
1476c9fc3ab11a76686aa45a1b4174022ea78c86|2023-03-10 16:03:30 +0200|Merge pull request #202 from QuantuMobileSoftware/PLT-283
ab3e54927f39d04abb87eddb3de58824ef670317|2023-03-10 13:23:05 +0200|Merge pull request #196 from QuantuMobileSoftware/PLT-11-fix-wc
db10997962bee65ba69f36092cd0b9cc4f9d2bba|2023-03-10 10:58:24 +0200|Merge pull request #195 from QuantuMobileSoftware/PLT-180-fix-handlers
d8dea9b25e8874dca2e65379cb9c72651cc20769|2023-03-10 10:24:50 +0200|Merge pull request #192 from QuantuMobileSoftware/PLT-166
d244c4f9cdb60f8ae7d71a12fc948794353d9849|2023-03-09 19:24:42 +0200|Merge pull request #193 from QuantuMobileSoftware/PLT-180-fix-handlers
7ee7bd49dc335ba23dc91c3d4e4082c9abbab9c4|2023-03-09 18:42:59 +0200|Merge pull request #191 from QuantuMobileSoftware/PLT-180-fix-handlers
35e896e3fcc9099395f9669b00b2580e3929ac2f|2023-03-09 17:48:06 +0200|Merge pull request #189 from QuantuMobileSoftware/PLT-124
0493bbb12d820ae51c7f40d484c6590423314ec6|2023-03-09 17:37:04 +0200|Merge pull request #190 from QuantuMobileSoftware/PLT-180-fix-queries
09847903773cd1f5e3e4167aa1e39fe51294d257|2023-03-09 16:44:43 +0200|Merge remote-tracking branch 'origin/PLT-124' into PLT-124
3fe3b666ae0e569894b6643792b0303dd5f2cf17|2023-03-09 16:04:25 +0200|Merge pull request #188 from QuantuMobileSoftware/PLT-180-add-logging
0478039c05b072bf017d83f33d06e5b45f3bb4ec|2023-03-09 15:07:58 +0200|Merge pull request #187 from QuantuMobileSoftware/PLT-180-add-logging
216ceb9eafc996f4dfa5b8d582c1ad1e67d977e9|2023-03-09 12:14:52 +0200|Merge pull request #186 from QuantuMobileSoftware/PLT-124
e39bf47f95e4e280525503605a80ac1e9c6e7870|2023-03-09 11:52:19 +0200|Merge branch 'master' into PLT-124
9317b3e89660620654b497ae702a7d878d2bc521|2023-03-09 11:46:58 +0200|Merge pull request #185 from QuantuMobileSoftware/PLT-189-add-data-register
9db4760b9d460a72aa2c4944666134dda4032cc9|2023-03-08 14:05:59 +0200|Merge pull request #184 from QuantuMobileSoftware/PLT-124
2a9d8e2a6d484a9bab51ffc0955ee71cc6d71334|2023-03-08 13:20:32 +0200|Merge pull request #183 from QuantuMobileSoftware/PLT-179-add-logs
cfa1e405c5511ca5c2c2cb0f2961a188f754f6a7|2023-03-08 12:54:00 +0200|Merge pull request #180 from QuantuMobileSoftware/PLT-194-CloudWatch-logs
1127f95883dbe926f2978eb01f11756b38ebbcd6|2023-03-08 10:21:43 +0200|Merge pull request #182 from QuantuMobileSoftware/tests/fix-asset-contracts
366ef856f434fb5693e72680f0756e9714b8d9fe|2023-03-08 10:10:35 +0200|Merge pull request #181 from QuantuMobileSoftware/feature/fix-notifications-url
431c9ab4fb14d7679e714fd190df8785556f8068|2023-03-07 16:53:48 +0200|Merge pull request #178 from QuantuMobileSoftware/PLT-125
20a4d768886ab3180ff3aceebc3f28600b177715|2023-03-07 16:24:28 +0200|Merge pull request #179 from QuantuMobileSoftware/PLT-190-fix-notification-contracts
e25d907288b1489f321dc59af11a57cfeba75bb5|2023-03-07 13:42:05 +0200|Merge pull request #174 from QuantuMobileSoftware/PLT-103
6fdd977f322f5d252ef470217aaa61f6cd28c285|2023-03-07 12:29:16 +0200|Merge pull request #173 from QuantuMobileSoftware/PLT-190-sync-notifications
09fea5763082362496f6e938e9666ead306a2787|2023-03-07 11:32:47 +0200|Merge pull request #172 from QuantuMobileSoftware/PLT-191
4462dca0b8e77c0af572caee83d9a2148722b12e|2023-03-07 10:47:43 +0200|Merge pull request #171 from QuantuMobileSoftware/PLT-164
c13d04962be22881220f7fb2cd7313017e35cb36|2023-03-07 10:29:43 +0200|Merge pull request #168 from QuantuMobileSoftware/PLT-189-tests-update-dto-notifications-platform-key
f54075e2eff81082dc5c6c5f53c907761bc23a5c|2023-03-07 10:24:28 +0200|Merge pull request #167 from QuantuMobileSoftware/PLT-164
36ced2575e08307ec842c8904dba487f555de61f|2023-03-06 17:30:58 +0200|Merge pull request #166 from QuantuMobileSoftware/PLT-179
3b8febb606ee3e22d629ba2210c86c156a0a154f|2023-03-06 16:35:00 +0200|Merge pull request #165 from QuantuMobileSoftware/PLT-189
c660ae7c025d40c04a65f278aae542ebe5185f74|2023-03-06 15:39:27 +0200|PLT-189: merge master
f5a58931b4e470391487ffd8fa48021d1701a314|2023-03-06 13:48:10 +0200|Merge pull request #164 from QuantuMobileSoftware/PLT-179
eb9614f3cee494fbaa66100add226c7140d314ad|2023-03-06 13:33:19 +0200|Merge branch 'master' into PLT-179
130238fe04108909039315260f53577b64c39915|2023-03-06 13:23:48 +0200|Merge pull request #155 from vakulyk/upd-env-var
4dc4719424ce27c6492a189fb2c97b79bf5eb32f|2023-03-06 13:23:20 +0200|Merge pull request #156 from QuantuMobileSoftware/featue/update-tests-safeheron-structure
adb19311a9e7f2354add562f98ea47324a033a58|2023-03-03 14:26:26 +0200|Merge pull request #163 from QuantuMobileSoftware/PLT-87-fix-cosigner
4ec6c1598c98009b1e8714d18ba93f70514aa799|2023-03-03 10:55:00 +0100|Merge pull request #162 from QuantuMobileSoftware/PLT-180-fix-send-notification
d0a03fcd0448903e69cda5cac4d9a4701b700bd2|2023-03-02 18:10:30 +0200|Merge branch 'master' into PLT-179
5355ff78bc29ae15a761b0f2746c127d77c8cbbf|2023-03-02 17:58:27 +0200|Merge pull request #161 from QuantuMobileSoftware/PLT-180-fix-status-change
e1f3d1b55e683fa9a79d8f3e780ac98893851615|2023-03-02 16:29:00 +0200|Merge pull request #160 from QuantuMobileSoftware/PLT-180-fix-quering-argument
7430824ecfb917b6646aef480e3aed9e9ef74c8e|2023-03-02 16:21:21 +0200|Merge pull request #159 from QuantuMobileSoftware/PLT-180-fix-quering-argument
b630eef94a60cbc7ff672d72c42c5a43e1642c8a|2023-03-02 15:14:25 +0200|Merge pull request #158 from QuantuMobileSoftware/PLT-180-fix-quering-argument
3aa1faf01e978bf1afe8641b1028bb376023ad9e|2023-03-02 09:39:13 +0100|Merge pull request #154 from QuantuMobileSoftware/PLT-180-fix-signature
48a9cce6aaf21f1694b410553cac1431513cd853|2023-03-02 09:38:41 +0100|Merge pull request #153 from vakulyk/fix-service
f060389690828899e71885035244f93144fe0afb|2023-03-01 18:11:51 +0200|Merge remote-tracking branch 'upstream/master'
ee93346aa473363b7ab8e6ffbfbb36abb33e1186|2023-03-01 17:10:09 +0200|Merge pull request #152 from QuantuMobileSoftware/PLT-183-Volumes-in-docker-compose
eea2bcb19ee18cd92574ea2d14f1c85380b371e2|2023-03-01 15:20:37 +0100|Merge pull request #151 from QuantuMobileSoftware/PLT-129-platform-public-key
81804289290ef2b2a0482d0f887687e7cdca8aaa|2023-03-01 15:57:23 +0200|Merge pull request #150 from QuantuMobileSoftware/PLT-180-fix-keys
7cc4c5f0e3224c0319277a8240f2aeb21542e5ef|2023-03-01 15:53:30 +0200|PLT-129: merge master
f90a9aff9853b36ad2db6c0f3fc3feec63cdff2a|2023-03-01 13:57:12 +0100|Merge pull request #133 from QuantuMobileSoftware/PLT-68-asset-balance-usd
c9cbe936bf79f1d0706c4bb0e7b1b5a630076218|2023-03-01 14:56:01 +0200|Merge pull request #149 from QuantuMobileSoftware/PLT-183-Volumes-in-docker-compose
042c76190f585e2627f204555376438c229001fd|2023-03-01 13:52:47 +0100|Merge branch 'master' into PLT-68-asset-balance-usd
e6b8cbdb3ab983d67d2a00e6ccb463998533b9bf|2023-03-01 14:45:25 +0200|Merge remote-tracking branch 'upstream/master'
4a4f6e09bec4544d0d5654759487e24b771fa8a0|2023-03-01 13:27:05 +0100|Merge branch 'master' into PLT-68-asset-balance-usd
d4715ef14652e523241891dced4d4d107dca0ee1|2023-03-01 14:26:28 +0200|Merge pull request #147 from QuantuMobileSoftware/PLT-180
76bc8ec2f35406844cd28f4c66e9cb12b5fefa61|2023-03-01 13:24:41 +0100|Merge pull request #132 from QuantuMobileSoftware/PLT-10-wallet-connect-bridge
5086f14649a94f6e0ee86606cece0ed693aaf608|2023-03-01 14:20:21 +0200|fix
2d3dda31760a6f1da6216eb6cbc8d7fa9e1fe104|2023-03-01 13:20:56 +0200|Merge pull request #146 from QuantuMobileSoftware/PLT-183-Volumes-in-docker-compose
5276d852cf5421c1a4ebbf4be48b2cc87b074577|2023-03-01 10:57:35 +0200|Merge pull request #144 from QuantuMobileSoftware/PLT-188-bwi-notifications
c51ec979861792e17af92cffc86dd663e2af1a56|2023-03-01 03:12:15 +0200|Merge remote-tracking branch 'upstream/master'
9b3eada1cc24a41ca7211ce785e39d1997390970|2023-02-28 17:03:22 +0200|Merge pull request #142 from QuantuMobileSoftware/PLT-111-make-payment-tests
942a42f43faeafeb9bed4d8a91b129aa5f1529fc|2023-02-28 14:08:37 +0200|Merge pull request #141 from QuantuMobileSoftware/PLT-129-co-signer-provision
6a12fa859a8241f874db76d8a19adf26058faebb|2023-02-28 12:42:39 +0100|Merge branch 'master' into PLT-68-asset-balance-usd
21111bd2649c838dcffe76581146584d332f5a8a|2023-02-28 13:12:20 +0200|Merge pull request #140 from QuantuMobileSoftware/PLT-129-co-signer-provision
92d8f0653170950cf5a434442a8882e84cbc67c5|2023-02-28 12:58:18 +0200|Merge pull request #137 from QuantuMobileSoftware/PLT-177
5b425e3a835de863ae4ef8b372bb4dbbede28827|2023-02-28 12:55:08 +0200|Merge branch 'master' into PLT-177
9604d408ac77ba1f258fd2812d32efb77bbef73d|2023-02-28 12:52:08 +0200|Merge pull request #139 from QuantuMobileSoftware/PLT-45-env
d6f113de6d10a3d902907afcbf95a06832ad108e|2023-02-28 12:37:07 +0200|Merge branch 'master' into PLT-111-make-payment-tests
9e89f4485c6529f05230b22aa5da4c7154de6173|2023-02-28 11:52:54 +0200|Merge branch 'master' into PLT-177
7e901ac82c1b84541e06cd2b8a132c3e012afc64|2023-02-28 11:07:53 +0200|Merge pull request #136 from QuantuMobileSoftware/PLT-178
09f2292bea35ffc0ac38ce0d4ec75c845e827996|2023-02-28 01:51:48 +0200|Merge branch 'master' into PLT-51
4a66b23ed7ea4d12d1b5c0a0c281a1a5d632b4fa|2023-02-27 18:01:56 +0200|PLT-178: merge master
3d4a57f6667e72cb295ea5593a2b7948e288e43b|2023-02-27 16:18:19 +0200|Merge pull request #134 from QuantuMobileSoftware/PLT-129-co-signer-provision
41e3a4537efdd94d37f506f645f0d749795078a9|2023-02-27 16:13:29 +0200|Merge remote-tracking branch 'origin/master' into PLT-129-co-signer-provision
ffbe380cb0d2a7ddf797e14330665087391bd181|2023-02-27 16:05:30 +0200|Merge pull request #130 from QuantuMobileSoftware/PLT-45
74662688f1fe35977a7e19d7d79dca8dfd95276b|2023-02-27 15:59:20 +0200|PLT-45: merge master
472c069dea5ec7e2cf02ae1c3cf008f42eccdbb1|2023-02-27 13:10:44 +0200|Merge pull request #129 from QuantuMobileSoftware/PLT-41
622a28491a3b62ccbdc78cb217794ff50ffc3b2f|2023-02-24 16:17:09 +0200|Merge pull request #128 from QuantuMobileSoftware/PLT-168-removed-xfails
98bbcabc125abea0350d80d9783ee652c06dcd6a|2023-02-24 13:08:29 +0200|Merge pull request #125 from QuantuMobileSoftware/PLT-153-notifications-list-tests
5d0116101c01e8fdd32395ba45a401267409ec9c|2023-02-24 12:30:24 +0200|Merge branch 'master' into PLT-153-notifications-list-tests
effe4195b63dcf2ab25921c568ff48ebcf2541f2|2023-02-23 17:05:01 +0200|Merge branch 'master' into PLT-41
bf45ebeefd1790e04a59baa5716008d165b3d4a1|2023-02-23 16:54:12 +0200|Merge pull request #121 from QuantuMobileSoftware/PLT-171
92715e2afb6778e507e1f65fc1261751116b45a8|2023-02-23 16:50:16 +0200|Merge pull request #119 from QuantuMobileSoftware/PLT-12
e313264179eafe32926300b712acca19b11c1a2e|2023-02-23 16:48:08 +0200|PLT-171: merge master
b454dbcf8e533323ebf5ffe90d905f1f61c05eaa|2023-02-23 15:27:42 +0200|Merge pull request #120 from QuantuMobileSoftware/PLT-168
6bb6ec8801613d416a64bdea81479202bc92528d|2023-02-23 14:51:04 +0200|Merge branch 'master' into PLT-12
20f73d1b9223bffa685484cb6dc55ca12b935ba2|2023-02-23 12:39:46 +0100|Merge pull request #118 from QuantuMobileSoftware/PLT-129-add-instance-info
61178c72e5e72ba4f5e3d003a184907665f80bf6|2023-02-23 12:17:40 +0200|Merge pull request #114 from QuantuMobileSoftware/PLT-11
f23a4aa50fc2bfe39b91869d76ab316c75d55f93|2023-02-23 11:40:38 +0200|Merge pull request #105 from QuantuMobileSoftware/PLT-129-co-signer-provision
9bc884a1ee7d8700a67376fe290e45b4eb2a6ca8|2023-02-22 15:31:34 +0200|Merge pull request #115 from QuantuMobileSoftware/set_different_port_for_statge
d3e85a7828b19ed4ee9dd319553fcf57c4c675d7|2023-02-22 15:12:16 +0200|PLT-11: merge master
ffe6639fa0acc9c1bceffd881513d90bd2f1bf9b|2023-02-22 14:09:17 +0200|Merge pull request #113 from QuantuMobileSoftware/feature/fixed-parallel-run
75acbf272077e3f7ae66a9682557e18a073f7db5|2023-02-22 10:55:56 +0200|Merge pull request #107 from QuantuMobileSoftware/PLT-141-tests-enable-disable-assets
f8b4c9ef7a95ef9ad994a0464520e7441ffcc060|2023-02-21 19:10:50 +0200|Merge branch 'master' into PLT-141-tests-enable-disable-assets
5e91c66f347eee8e4466ff3e4248bcb8037f961c|2023-02-20 19:20:21 +0200|Merge pull request #109 from QuantuMobileSoftware/PLT-163
ba67ebb01d065b553f0b377da79e79fcf06ae43d|2023-02-20 18:35:01 +0200|Merge pull request #108 from QuantuMobileSoftware/PLT-163
7e3b58b3e15af215e42067a0f8fd5a3502101745|2023-02-20 17:26:14 +0200|Merge pull request #96 from QuantuMobileSoftware/PLT-72
2d99356632a4af19b3d0600d5d220654f0f9f588|2023-02-20 12:29:13 +0100|Merge pull request #106 from QuantuMobileSoftware/PLT-0-remove-unused-func
fee9394515872b75bafa39b6d019dd72dbf3548b|2023-02-20 11:11:34 +0200|Merge pull request #101 from QuantuMobileSoftware/PLT-127_test-fixing-after-resolved-bugs
8505552593dd75274737622d379a9c575685483e|2023-02-17 12:01:01 +0100|Merge pull request #90 from QuantuMobileSoftware/PLT-129-django-part
46fedacdd2074018fc9557b2366585a7d4e3d678|2023-02-17 11:19:33 +0200|Merge pull request #104 from QuantuMobileSoftware/PLT-18
1852ea90a4bc28afa784e9bd835743edaf6afc86|2023-02-17 10:29:01 +0200|Merge pull request #103 from QuantuMobileSoftware/PLT-140
27cef43788007567074106450794c93245c1308a|2023-02-16 19:35:57 +0200|Merge pull request #102 from QuantuMobileSoftware/PLT-140
df62f3ccc67707a78116dbfc6267f12bca7d269a|2023-02-16 15:36:43 +0200|Merge branch 'master' into PLT-72
c068f34892a81b06bb3fa01a132a9a2160419c08|2023-02-16 15:31:19 +0200|Merge pull request #99 from QuantuMobileSoftware/PLT-148
af69fcd3a9de2569b1fc9660c772b3145d30c65e|2023-02-16 15:10:19 +0200|Merge branch 'master' into PLT-72
09ed503a0999a41ba3287edda4ececc90a20ce28|2023-02-16 15:02:01 +0200|Merge pull request #98 from QuantuMobileSoftware/PLT-26
7d7f4e13e65fecaa947ca688858c29cfadff23b9|2023-02-16 15:00:36 +0200|PLT-27: merge master
dc21a8114c1c525374665b488e6914df84ab6705|2023-02-15 18:14:02 +0200|Merge pull request #92 from QuantuMobileSoftware/PLT-26
fe6a0015e95932356ae5703bbaf05e1cab17017c|2023-02-15 13:41:29 +0200|Merge pull request #95 from QuantuMobileSoftware/PLT-143
c938cfc45830fb988ae33f50f8b05fdd2c511b37|2023-02-13 10:56:45 +0200|Merge pull request #91 from QuantuMobileSoftware/PLT-141-assets-default-address
038e4a97ee4bb703645dc2472a7c666df7be85aa|2023-02-09 15:34:16 +0200|Merge pull request #89 from QuantuMobileSoftware/PLT-37-fix
e609e137277a7b9c17c841e24dccb41c2ad275ac|2023-02-09 14:45:43 +0200|Merge pull request #88 from QuantuMobileSoftware/PLT-37-fix
1013cd6aa95d48edeead7258935aa3c005494e9d|2023-02-09 14:30:59 +0200|Merge pull request #87 from QuantuMobileSoftware/PLT-141-assets-default-address
b7d68eb90f74af397420298744ed3311d65427d8|2023-02-09 13:27:03 +0200|Merge pull request #85 from QuantuMobileSoftware/feature/change-env-sample
395034fefb9a1fe61ed1869b5997a826013b2ca8|2023-02-08 18:49:54 +0200|Merge pull request #84 from QuantuMobileSoftware/PLT-136-asset-address-tests
36415bafa082281ff839b622f03e6e42be9d818d|2023-02-08 16:24:33 +0200|Merge pull request #82 from QuantuMobileSoftware/PLT-137
93d8cfbe68a3283d7e3fa3507f2b7cf18be5ee07|2023-02-08 14:56:34 +0200|Merge pull request #81 from QuantuMobileSoftware/PLT-19
9d85c59ff18570e8f7b883e9182d5b7d94f63518|2023-02-08 13:59:59 +0200|Merge pull request #80 from QuantuMobileSoftware/PLT-27
e946d93765ca82a4116ea44f80daf010c11524a4|2023-02-08 12:40:31 +0200|Merge pull request #78 from QuantuMobileSoftware/PLT-41
3ff35cef8f3ac4f1c6d89d6f82750700b769a8a0|2023-02-08 09:28:53 +0200|PLT-27: merge master
d3a1e4e024fdb3939e61930ed2ceb71fedd6365f|2023-02-07 16:11:15 +0200|Merge pull request #77 from QuantuMobileSoftware/PLT-132-polity-vault-refactoring-using-vault
9e8755960a19e187cdbbfc47fdb9b6a2d9147a26|2023-02-06 16:58:02 +0200|Merge pull request #76 from QuantuMobileSoftware/PLT-66
1a32245625b94526fc9058ca6dc823e1cd2c6c00|2023-02-06 16:25:35 +0200|Merge branch 'master' into PLT-66
45cfe55f1c3053725f1a84f22c11f850d87fc548|2023-02-06 14:15:49 +0200|Merge pull request #74 from QuantuMobileSoftware/PLT-38
bce5cb085c6268187013b0d80da34853996083ec|2023-02-06 13:12:54 +0100|Merge pull request #66 from QuantuMobileSoftware/PLT-64-secrets-backend
b58f09ed1fa045cf37bdfae72174415d70307e05|2023-02-06 14:01:12 +0200|Merge pull request #75 from QuantuMobileSoftware/aqa_updating_dfns_assets_assertation
f7ecb82bec0d3ec2cfef147c6328460b956d64ca|2023-02-06 11:36:34 +0200|Merge pull request #71 from QuantuMobileSoftware/PLT-105-asssets-list
8ecfd32142ee2439205d8b03cb8f2e140ea3c789|2023-02-06 11:36:13 +0200|Merge pull request #73 from QuantuMobileSoftware/PLT-104-safeheron-callback-negative-tests
2018d39f35aee214c355457051a75f48efb6fb8b|2023-02-03 14:14:24 +0200|Merge pull request #70 from QuantuMobileSoftware/PLT-38
1981d6f7c03b2ce9a0c9216fa8538d3146b36843|2023-02-03 13:03:56 +0200|Merge branch 'master' into PLT-105-asssets-list
8c0d13b6a8b6eb8dc499d16556638e7aeda37d6f|2023-02-02 18:49:51 +0200|Merge pull request #68 from QuantuMobileSoftware/PLT-104-safeheron-callback-tests
76e7ac3ba78e59614c5d1045d5404cf5c62a9324|2023-02-02 17:17:20 +0200|Merge branch 'master' into PLT-104-safeheron-callback-tests
78150359093c2e2e0e1696d2025c56560bfe2ca7|2023-02-02 14:59:32 +0200|Merge pull request #67 from QuantuMobileSoftware/PLT-109
96843f9bcb36aeb90bbac7e1ba2f4c0493aae59f|2023-02-02 14:59:01 +0200|Merge pull request #64 from QuantuMobileSoftware/PLT-18
282977349fc552599b225853366503ad456bc66b|2023-02-02 14:26:27 +0200|Merge branch 'master' into PLT-66
a9c3df0a94296b0f69189c68a3151c838edd0f07|2023-02-02 14:21:11 +0200|Merge pull request #65 from QuantuMobileSoftware/PLT-120
b4350cd745609d553bd9c9cd139a575e983a9e01|2023-02-02 12:59:33 +0100|Merge branch 'master' into PLT-64-secrets-backend
421010c08de443aa729e2ac757ba54fd9a8a3c79|2023-02-02 11:07:00 +0200|Merge pull request #52 from QuantuMobileSoftware/PLT-92_adding_assets_tupe_creation_tests
031c57a05e9ed81fea18c9835cb50816acb4292c|2023-02-01 18:21:42 +0200|PLT-18: merge main and refactor
693c62e1f1fa6b9be9f3aa2f61dc33913a55078d|2023-02-01 16:55:18 +0200|Merge pull request #63 from QuantuMobileSoftware/PLT-120
49fbc868ee816246e695d2579bac131cef118555|2023-02-01 16:02:41 +0200|Merge pull request #61 from QuantuMobileSoftware/PLT-94
fb6ab2bc862e75fb465f897bf99a690ace6458e6|2023-02-01 15:56:07 +0200|Merge branch 'master' into PLT-94
84b7a0b177a7e7fa22206b9f1b20c1bc49bbbd92|2023-02-01 15:41:29 +0200|Merge pull request #62 from QuantuMobileSoftware/PLT-119
be528d30b8b6f4120897ca6cdf186400557c41d3|2023-02-01 14:50:20 +0200|Merge pull request #60 from QuantuMobileSoftware/PLT-94
63d2b017ac3c1abf0d8dbff9a11e0ae8690aac2a|2023-02-01 12:06:46 +0200|Merge pull request #48 from QuantuMobileSoftware/PLT-94
fc0f7ea9faec8f3756fe93e0e9a58266cbfd5683|2023-02-01 12:05:32 +0200|Merge pull request #56 from QuantuMobileSoftware/feature/user-notification-docker-compose-fixes
e943b3204babb4423bd997e5cf9772403da93d0d|2023-01-31 14:13:34 +0200|Merge pull request #54 from QuantuMobileSoftware/PLT-116-wallet-infrasctructure-ci-cd
9977844f7e0f376f9e79567117b812aa56450f14|2023-01-31 13:29:28 +0200|Merge branch 'master' into PLT-94
0cbcd4795b04aebcc7cf606d393a4a458b060b41|2023-01-31 12:05:24 +0200|Merge pull request #51 from QuantuMobileSoftware/PLT-37
30fee453ff234075a786daa22615248aedaa3c7f|2023-01-30 14:38:29 +0200|Merge pull request #50 from QuantuMobileSoftware/PLT-107
714344bf99db338353811d1c51f032994d4ffd67|2023-01-30 14:10:39 +0200|Merge branch 'master' into PLT-92_adding_assets_tupe_creation_tests
fc2bfd20357a25d5ff3dfc1be14bbe4266bb1191|2023-01-30 14:00:35 +0200|Merge pull request #49 from QuantuMobileSoftware/PLT-107
3fa66488e8c44654504d70f2138e4721a97232b6|2023-01-30 11:25:38 +0200|Merge pull request #47 from QuantuMobileSoftware/PLT-99-remove-xfail
2d6124d74555ce6b0620c4787d7b290c78fa14a3|2023-01-27 17:39:24 +0200|Merge pull request #45 from QuantuMobileSoftware/PLT-37
8b54211fa9cdbe5f8e42f64c5012880b150cbde1|2023-01-27 17:33:51 +0200|PLT-37: fix conflicts
ac6d04e571c7b14bcfdf3f6a22083d21f98a52bc|2023-01-27 17:30:58 +0200|Merge pull request #44 from QuantuMobileSoftware/PLT-15-fix
623bd937726602a3cd0c1e1c6e06e3d7e110c19d|2023-01-26 18:44:24 +0200|Merge pull request #28 from QuantuMobileSoftware/PLT-15
0e6821093ab5f1a8fd6696d27b566417fe4e2475|2023-01-26 18:39:16 +0200|Merge branch 'master' into PLT-15
e4743a808d5e1e5fbbe7f733760f30962cc70e48|2023-01-26 18:11:28 +0200|Merge pull request #42 from QuantuMobileSoftware/PLT-102
8a87436728c7f3b677a9658a7322eab7e9bd2f46|2023-01-26 15:19:17 +0200|Merge pull request #39 from QuantuMobileSoftware/PLT-65
50d749ac41d207f9b528450c994755447dbd4e0e|2023-01-26 13:27:43 +0100|Merge pull request #40 from QuantuMobileSoftware/PLT-0-fix-status
8f71e568115a1dea3f6b840e650bc947effac307|2023-01-26 14:11:09 +0200|Merge pull request #35 from QuantuMobileSoftware/PLT-93-assertions-for-resolved-issues
c3fcf51fb439097349b644154b2f6a37913326d9|2023-01-26 13:53:51 +0200|Merge pull request #38 from QuantuMobileSoftware/PLT-102
8e5266068bca96b26fc0b48bb40d3c42a9f1cbff|2023-01-26 12:18:12 +0200|Merge pull request #37 from QuantuMobileSoftware/PLT-16
664cdc04240ca5325a4ad411545228eddb6959b3|2023-01-26 09:54:17 +0200|Merge pull request #36 from QuantuMobileSoftware/PLT-87-fix
c3ba903201be209cad6c38eeb05be2721e2fbb51|2023-01-25 19:13:24 +0200|Merge pull request #17 from QuantuMobileSoftware/PLT-25
182171d10307495ff0b3c8086be4278bb34de479|2023-01-25 18:39:23 +0200|Merge branch 'PLT-25' into PLT-15
762f16cb034798c0434c7932bdc1a8fc9aec567a|2023-01-25 17:38:17 +0200|Merge remote-tracking branch 'origin/PLT-25' into PLT-25
c375f574c2b02faa7b27f9ad2f7e4c9c9b98dd65|2023-01-25 16:42:45 +0200|Merge pull request #33 from QuantuMobileSoftware/PLT-93-create-wallet-inegration-tests
3d99d1b7646fe99599bf8c168dc3db89400bbac4|2023-01-25 12:37:06 +0200|Merge branch 'PLT-25' into PLT-15
****************************************|2023-01-25 00:55:25 +0200|Merge branch 'master' into PLT-25
d8973c151edfc73606c38ceb13a6c2c3abfb97cc|2023-01-24 21:05:09 +0200|Merge branch 'PLT-25' into PLT-15
7ee29a25aa0eaae0305f7806c48724499b2e1e61|2023-01-24 19:22:40 +0200|Merge branch 'master' into PLT-25
ae6e3e8b9e4df691b6201fb3def0776b5ec158ef|2023-01-24 17:52:15 +0200|Merge branch 'master' into PLT-15
03e5eab7bf846bfae973e3e7e58fb07b2d071493|2023-01-24 14:51:30 +0200|Merge pull request #31 from QuantuMobileSoftware/PLT-87
ee35c6b34721172e9409c4377356eb5cda255241|2023-01-24 14:14:39 +0200|Merge pull request #32 from QuantuMobileSoftware/feature/root-make-file-changes
c17468284a199ecbb10781567e3a7d710ccd43ab|2023-01-24 12:33:34 +0200|Merge pull request #29 from QuantuMobileSoftware/PLT-71-refactor
0d3e63617bd2a8e1570d17a29d7d597352634ebe|2023-01-24 12:05:32 +0200|Merge pull request #30 from QuantuMobileSoftware/PLT-93-bwi-tests
b4602058f4788367b45e42380ffdb8573013da88|2023-01-23 13:32:14 +0200|Merge pull request #27 from QuantuMobileSoftware/PLT-91_aqa_framework_makefile
bad97a49f5f591f69c964c331b9134c18770fb98|2023-01-19 17:56:33 +0200|Merge branch 'master' into PLT-25
dbe69128d5dcd604c7e46b0bdb3853877984fd22|2023-01-19 16:58:27 +0200|Merge pull request #21 from QuantuMobileSoftware/PLT-71
d5936ed859d95706cea45d145d5ee373d74253de|2023-01-19 16:31:24 +0200|PLT-71: resolve conflicts
e89ac5aa696be26172f3aadfe9d121b2d6988127|2023-01-19 16:27:39 +0200|Merge pull request #24 from QuantuMobileSoftware/PLT-6-refactor
b25d1d66a1c5fcfc20ebf901182cc5fc7767468d|2023-01-19 15:53:10 +0200|Merge remote-tracking branch 'origin/PLT-25' into PLT-25
8d48370e8a8c4e4441e726007db37a46ea584db4|2023-01-19 15:50:07 +0200|Merge pull request #26 from QuantuMobileSoftware/PLT-71-delete-idea
e8136e9f51b22ed742f52fc93be0617e13862b0b|2023-01-19 14:35:51 +0100|Merge pull request #25 from QuantuMobileSoftware/PLT-21-status-update-endpoint-call
19cce377e5acf30e4cfe9a6b6f82dd4e09800703|2023-01-18 12:57:48 +0200|Merge pull request #22 from QuantuMobileSoftware/PLT-90_configuration-for-stage-pipeline
d665965cfcf0475547ce4e4a7bf161c22b8eb407|2023-01-18 11:41:12 +0200|Merge pull request #20 from QuantuMobileSoftware/polity_vault/using_postgres
e2c2d32e1bb3d58d8d01bba72111c537e84431da|2023-01-17 18:07:32 +0200|Merge branch 'master' into PLT-25
29f992f796130bd0d9143150e863ae5310a423eb|2023-01-17 14:48:16 +0200|Merge pull request #19 from QuantuMobileSoftware/PLT-71
a02bb4355de1209b11c9225cbce4598825ea8c53|2023-01-17 11:43:25 +0200|Merge pull request #18 from QuantuMobileSoftware/polity_vault/regressoin_tests
640119ed80468914d13c81ca9237525014481c0d|2023-01-16 20:59:14 +0200|Merge branch 'master' into polity_vault/regressoin_tests
bc906e23c4c0a043bdf2b530600e42dd38fc0f8e|2023-01-16 18:19:01 +0200|Merge branch 'master' into PLT-25
67d00adfcd4a8d277bd208e55c0ef1ea0e015048|2023-01-16 11:06:48 +0200|Merge pull request #16 from QuantuMobileSoftware/PLT-6
7e8e2998128f296f62703ad6af830a0ef3ee0b14|2023-01-11 17:37:35 +0200|Merge branch 'master' into polity_vault/regressoin_tests
ea3a467135490af71e07d9ef3a227ed4d91d8aa0|2023-01-11 17:34:11 +0200|Merge pull request #15 from QuantuMobileSoftware/PLT-85-polity-swagger-cleanup
5b5652324158099a69c2b02ee24da0abb5a540f2|2023-01-11 16:57:00 +0200|Merge pull request #14 from QuantuMobileSoftware/PLT-85-polity-swagger-cleanup
78518d5c84cbfd9d658fa3871c5213e35f22e7f5|2023-01-11 16:52:43 +0200|Merge pull request #10 from QuantuMobileSoftware/PLT-14
fa708c257aae32ac9e9fceb64a377a84dd1d708a|2023-01-11 10:20:10 +0200|PLT-14: fix imports
fe5bfc438bad6fe9e4de452089278e8c479c58c5|2023-01-10 17:50:25 +0200|Merge branch 'master' into polity_vault/regressoin_tests
19d4c1582114c02bfff4e7c3bc32b14c0b886f91|2023-01-10 17:42:28 +0200|Merge pull request #8 from QuantuMobileSoftware/PLT-8
7d3a8ca827a49df35fad9fc49f12b946cc2cc64b|2023-01-10 13:06:30 +0100|Merge pull request #11 from QuantuMobileSoftware/PLT-59-add-uuid
d448d49ba7f588e1d68561b9bd40ea8ee14d842a|2023-01-06 20:50:00 +0200|Merge branch 'master' into polity_vault/regressoin_tests
1e2ed527d4ffa02ac5e7142d747f19df98d233c3|2023-01-06 19:49:45 +0200|Merge pull request #9 from QuantuMobileSoftware/PLT-78-79-84-fix-bugs-add-endpoints
d7f7759fad2e990bbc29044485ba5772a82f5f91|2023-01-05 20:44:31 +0200|Merge branch 'master' into PLT-8
73b5035d55cb20136bad62f3c3a617b86d0cfe6a|2022-12-28 15:53:54 +0200|Merge pull request #7 from QuantuMobileSoftware/refactor-safeheron-wallet
b6866faade2a40b9fc0a24696eaa9d78aab6d60c|2022-12-23 13:50:45 +0200|Merge pull request #2 from QuantuMobileSoftware/create-wallet-infrastructure
65bd6ca66569b26e91076b158e72c25f9448393d|2022-12-23 13:36:40 +0200|resolve conflict
86451ac8a9f4e47596f41e985dced7fb9df9bd26|2022-12-23 13:06:33 +0200|Merge pull request #4 from QuantuMobileSoftware/dfns
d2c144699d056d7893898e907cb1505214a94969|2022-12-23 12:53:27 +0200|Merge remote-tracking branch 'origin/create-wallet-infrastructure' into dfns
c951b88e975184463d48585fe62a629ea9d902c9|2022-12-22 14:05:40 +0100|Merge pull request #3 from QuantuMobileSoftware/setup-polity-vault
f93ddf60dc4912a04ad32d4b5fc3df6e779ae977|2022-06-30 14:25:30 +0100|Fixed merge
0336b58ba77290159f67bc3cc71703450a3d5ce8|2022-06-29 17:11:28 +0300|Merge pull request #109 from QuantuMobileSoftware/DCS-nd_deploy_10_avatar_nodes
854d47a26878ab2591f68d30283960d6689855e5|2022-06-29 13:32:59 +0300|Merge remote-tracking branch 'origin/master' into master
261173181d4f93d81fb30a98ce9b3d0f473cc745|2022-06-29 11:31:33 +0100|Merge pull request #108 from QuantuMobileSoftware/DCS-102-be-aggregate-information
8251002602649004d132439d0153b7ae33fdc290|2022-06-27 12:07:43 +0300|Merge pull request #106 from QuantuMobileSoftware/DCS-nd_verify_flow_update
1309d86bf33e39b553688140e87d39952be347c9|2022-06-22 21:11:22 +0300|Merge branch 'master' of github.com:QuantuMobileSoftware/DCS_POC into master
85c84ff90e8f06407e94c96794c4bd5e3e207b88|2022-06-22 18:34:24 +0100|Merge pull request #104 from QuantuMobileSoftware/DCS-102-be-aggregate-information
4bde560342343b498744a9f27fde92b33eb87f7f|2022-06-22 18:04:28 +0300|Merge remote-tracking branch 'origin/master' into master
7d76dc9f268e8c161a8320f3c06986b652fcdb68|2022-06-22 15:30:27 +0100|Merge pull request #103 from QuantuMobileSoftware/DCS-102-be-aggregate-information
34c1b0148b49fda77cca379c5dd9778633730153|2022-06-22 12:15:18 +0100|Merge pull request #101 from QuantuMobileSoftware/DCS-102-be-aggregate-information
7d3650a8e0cbf4087665abaa4f41fce712cc9d06|2022-06-22 11:32:54 +0100|Merge pull request #100 from QuantuMobileSoftware/DCS-102-be-aggregate-information
a4792f8df32dfeb53ac217483277c526cc39871a|2022-06-22 10:50:37 +0100|Merge pull request #99 from QuantuMobileSoftware/DCS-102-be-aggregate-information
6850b64088d7dfbfaf983da362b159cbae271d79|2022-06-20 12:56:35 +0300|Merge pull request #94 from QuantuMobileSoftware/DCS-251_Empty_value_is_displayed_in_the_Declined_by
4ed15ce413b310e4a5cd08141c98062a10a52519|2022-06-17 19:39:12 +0100|Merge pull request #93 from QuantuMobileSoftware/DCS-240-advisor-infrastructure-view-investor-details-agreement-lists-is-not-displayed
438a70aeeacfe4558b518c2f735e7b8ab7b93e4e|2022-06-17 19:32:49 +0100|Merge pull request #92 from QuantuMobileSoftware/DCS-240-advisor-infrastructure-view-investor-details-agreement-lists-is-not-displayed
dc84134f2c7ae25961307eab29541cb0b1e67c0a|2022-06-17 12:51:06 +0100|Merge pull request #91 from QuantuMobileSoftware/DCS-240-advisor-infrastructure-view-investor-details-agreement-lists-is-not-displayed
8baaef154e1a0c15e30519ad3bcec7ea98600f5a|2022-06-16 12:26:45 +0300|Merge pull request #89 from QuantuMobileSoftware/fe_fixes
ffeac92bda695ac55bcf908ef56c384206be0f40|2022-06-15 18:53:02 +0100|Merge pull request #88 from QuantuMobileSoftware/DCS-236-avatar-infrastructure-notifications-are-not-displayed
05480953c2a2cba2a8d3d7e40f65453865f28e8e|2022-06-15 18:11:16 +0300|Merge pull request #86 from QuantuMobileSoftware/fe_fixes
5bfd487bc400f28381c3ffadff6ef3d8bf117551|2022-06-15 14:55:08 +0100|Merge pull request #87 from QuantuMobileSoftware/DCS-236-avatar-infrastructure-notifications-are-not-displayed
07e10f9d8c2f76b07d33bce3b16f34a02458d151|2022-06-15 14:20:35 +0100|Merge pull request #85 from QuantuMobileSoftware/DCS-235-avatar-cant-view-agreements
d8fc06f04cdaacc03018c1a95b76919ae63e1eed|2022-06-15 13:00:51 +0300|Merge pull request #84 from QuantuMobileSoftware/DCS-231-acquisition-form-fixes
cdc8c1ef48dcffb1b07637bded7091c92fc6a04f|2022-06-15 10:02:57 +0100|Merge pull request #83 from QuantuMobileSoftware/DCS-230-advisor-infrastructure-avatar-images-are-not-displayed-in-the-list-of-avatars
19f718d2e770459c497687a637e09aacd68f8ed2|2022-06-14 15:11:31 +0300|Merge pull request #81 from QuantuMobileSoftware/fe_fixes
762663f45c92de87fd0efd6886f671db78f0efca|2022-06-10 15:33:41 +0100|Merge branch 'master' of github.com:QuantuMobileSoftware/DCS_POC
9a9504f93e9c7fe3ab20e103fdc261b6f0f73ad8|2022-06-07 16:32:30 +0300|Merge pull request #77 from QuantuMobileSoftware/DCS-224-hide-time-in-datetime-formatters
6cee654a4b93e37b114b2eeb38dee69487c906fc|2022-06-07 16:13:29 +0300|Merge pull request #71 from QuantuMobileSoftware/DCS-136_change_ledger_state
07f06d963283316a02bf036ca800473d52056ce9|2022-06-07 15:43:20 +0300|Merge pull request #76 from QuantuMobileSoftware/fe_cleanup
d71432b0da9ac71efd5f40b2ff6a9602da7c4939|2022-06-01 16:33:29 +0300|Merge pull request #65 from QuantuMobileSoftware/DCS-200_Platform_Validates_an_Agreement
12225e9eccdb78d77bfe3091778702c45e87af76|2022-05-26 14:03:33 +0100|Merge pull request #62 from QuantuMobileSoftware/DCS-135-be-push-notification-to-the-fe
b8e2e1536f2951d4d8f4ddaf6744291d14b34ec6|2022-05-25 15:51:43 +0300|Merge pull request #59 from QuantuMobileSoftware/fe_demo_updates
d84be951e2fde5e96810720f4d6e1dfc3b1868be|2022-05-19 14:11:18 +0100|Merge pull request #54 from QuantuMobileSoftware/integration
5c7110399383b2d92fcf7407b54bea37de119003|2022-05-18 15:05:53 +0100|Merge branch 'master' of github.com:QuantuMobileSoftware/DCS_POC
f0c1c3226ec9b0aaa5980f4fe54f33c0cc958463|2022-05-18 16:33:58 +0300|Merge remote-tracking branch 'origin/master' into master
52336e7a65f54c56400f3739c07b15d948ccfe70|2022-05-18 13:22:36 +0100|Merge branch 'master' of github.com:QuantuMobileSoftware/DCS_POC
fd524ab7ea52387764a2a7343ca58c965077c025|2022-05-18 11:15:13 +0100|Merge pull request #48 from QuantuMobileSoftware/DCS-127-be-query-the-corda-rpc-for-the-notifications
db029d43edc13c300d52d4a7ed1623d3e6d4964e|2022-05-16 12:18:09 +0100|Merge pull request #46 from QuantuMobileSoftware/DCS-41-be-agreement
e13dcc7e7d56791720522363bddec7a0ba0aaca7|2022-05-16 12:47:04 +0300|Merge pull request #45 from QuantuMobileSoftware/DCS-54_notarize_flow
0f476e842dbd8e467c144ee1f661a626bdb602f9|2022-05-12 18:15:58 +0100|Merge pull request #44 from QuantuMobileSoftware/DCS-202-implement-agreement-query-flow
c483ebadee7fc9c8aecfb66479c88e0273a22181|2022-05-12 10:08:10 +0100|Merged with master
040bf712a55ac85a1e14ee3c603aaf3bf3b6198b|2022-05-11 12:29:22 +0300|Merge pull request #42 from QuantuMobileSoftware/DCS-56_decline_flow
2a8af38f910d1add57f9927b45449b604203cfc3|2022-05-09 16:18:38 +0300|Merge pull request #39 from QuantuMobileSoftware/DCS-53_verify_flow
2f1555f5a3c43d810bf6c4ca9fc4e87b892612f4|2022-05-04 21:43:47 +0100|Merge pull request #36 from QuantuMobileSoftware/DCS-47-refactoring-be
c9dfa0c1e1cd8f027f787eed6eae8df73ceedf71|2022-05-04 17:59:06 +0300|Merge pull request #37 from QuantuMobileSoftware/DCS-51_add_all_corda's_5_flows_for_poc
59c0bd6079b20c4e07091ede8b12b8cc320022ed|2022-05-04 17:23:16 +0300|Merge branch 'DCS-47-refactoring-be' of github.com:QuantuMobileSoftware/DCS_POC into DCS-47-refactoring-be
90278bec208b59e4f4914131bed7ac9384f968fa|2022-05-04 17:10:37 +0300|Merge branch 'master' of github.com:QuantuMobileSoftware/DCS_POC into DCS-47-refactoring-be
9e44283b591a00fd27553ca76b10a84ceccb08f6|2022-05-02 23:20:51 +0100|Merge branch 'DCS-47-refactoring-be' of github.com:QuantuMobileSoftware/DCS_POC into DCS-47-refactoring-be
38724190a3f9b87aced192c374cb184d42f63850|2022-04-22 17:29:19 +0300|Merge pull request #29 from QuantuMobileSoftware/DCS-51_add_all_corda's_5_flows_for_poc
c5d3117bc35a2d5e0ed0df585aadff7bf81147be|2022-04-22 13:20:25 +0300|Merge pull request #21 from QuantuMobileSoftware/DCS-51_add_all_corda's_5_flows_for_poc
638419bcf62b62a2edd3e679dfba6c728c855453|2022-04-21 15:58:14 +0100|Merge pull request #26 from QuantuMobileSoftware/DCS-45-fix-integration-tests
222bf29f0e03341b65638e525567fdd118e12339|2022-04-21 15:15:40 +0100|Merge branch 'master' into DCS-45-fix-integration-tests
7bcc95d822037c2f68d955209c2138b3410dc6c1|2022-04-21 15:48:16 +0300|Merge pull request #23 from QuantuMobileSoftware/DCS-34_deploy_backend_and_frontend_application
aa3d31425ec73436cb0f6a39e30e43b5b127ded3|2022-04-20 15:07:22 +0100|Merge pull request #18 from QuantuMobileSoftware/DCS-48-refresh-token
46169e14fae0bb450c233fccf667f8acc8b17a10|2022-04-19 19:47:17 +0300|Merge pull request #15 from QuantuMobileSoftware/DCS-2_Blockchain_prepare_states&flows_T&C_adviser/avatar
9226f15009f1c853da150783dbac81096c06706b|2022-04-15 17:25:36 +0100|Merge pull request #12 from QuantuMobileSoftware/DCS-18-be-add-versioning-of-db-and-routes-for-user
df83103aa14d47751ffa155e02e432065b1c0a2c|2022-04-13 14:32:08 +0100|Merge pull request #8 from QuantuMobileSoftware/DCS-16-be-implement-login-logout-registration
65dbb078211dd54a418a46dde871576754ea2239|2022-04-12 15:39:37 +0300|Merge pull request #5 from QuantuMobileSoftware/DCS-12_integrate_prototype_components
6f2ec84a9dd67fb2bc8a2955c45fdc985ceab93c|2022-04-12 13:48:49 +0300|Merge branch 'DCS-12_integrate_prototype_components' into DCS-12-review
22f1fa1898ea47f5a650c879dd794ad336395c3e|2022-04-12 10:40:37 +0300|Merge branch 'master' into DCS-12-review
47fbf389071de07fa330e1243a5541952ee52d68|2022-04-11 15:16:52 +0300|Merge pull request #6 from QuantuMobileSoftware/fe_setup
0e111e78789b29320a5573c8862171895f0c0e61|2022-04-11 13:39:52 +0300|Merge pull request #4 from QuantuMobileSoftware/DCS-8-jwt-auth
71e90570aa6346909e75d9c9d7e698db101d6026|2022-04-09 21:06:42 +0100|Merge branch 'master' into DCS-16-be-implement-login-logout-registration
caae1b25ed57ac1f6f25746f9477bbbef9fcc44d|2022-04-08 12:53:06 +0100|Merge pull request #3 from QuantuMobileSoftware/DCS-20-back-docker-setup-for-multi-entry-frontend
f1d42885b6bba1711b25eeb41d0c2dcdd97a0b8c|2022-04-06 20:22:03 +0300|Merge branch 'master' into DCS-8-jwt-auth
0bd08bef7799e66e9a7c47e7b8640f6ab3ee0bb6|2022-04-06 20:14:18 +0300|Merge branch 'DCS-7_multy_entry_app' into master
e6697d3900c908dd18d5cc11b012a05eb46947b9|2022-04-06 12:23:52 +0100|Merge pull request #1 from QuantuMobileSoftware/DCS-5-back-setup-local-dev-environment
07412b9808056b424e873d560e13ab4e15b411e4|2022-04-05 09:57:14 +0300|Merge pull request #2 from QuantuMobileSoftware/DCS-7_multy_entry_app