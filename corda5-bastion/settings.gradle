pluginManagement {

    repositories {
        gradlePluginPortal()
    }

    plugins {
        id 'net.corda.plugins.cordapp-cpk' version gradlePluginsVersion
        id 'net.corda.plugins.cordapp-cpb' version gradlePluginsVersion
        id 'net.corda.cordapp.cordapp-configuration' version cordaAPIVersion
        id 'org.jetbrains.kotlin.jvm' version kotlinVersion
        id 'org.jetbrains.kotlin.plugin.allopen' version kotlinVersion
        id 'org.jetbrains.kotlin.plugin.jpa' version kotlinVersion
    }
}

dependencyResolutionManagement {
    repositories {
        mavenCentral()
        mavenLocal()
    }
}

rootProject.name = 'Corda5-Cordapp-Template'

include('contracts')
include('workflows')
