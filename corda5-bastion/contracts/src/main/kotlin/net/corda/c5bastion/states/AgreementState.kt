package net.corda.c5bastion.states

import net.corda.c5bastion.statuses.Status
import com.google.gson.Gson
import net.corda.c5bastion.contracts.ProposeAgreementContract
import net.corda.c5bastion.schema.AgreementSchemaV1
import net.corda.v5.application.identity.AbstractParty
import net.corda.v5.application.identity.Party
import net.corda.v5.application.utilities.JsonRepresentable
import net.corda.v5.ledger.contracts.BelongsToContract
import net.corda.v5.ledger.contracts.ContractState
import net.corda.v5.ledger.schemas.PersistentState
import net.corda.v5.ledger.schemas.QueryableState
import net.corda.v5.persistence.MappedSchema
import java.time.LocalDate
import java.util.*
import kotlin.time.hours


@BelongsToContract(ProposeAgreementContract::class)
data class AgreementState(
    val agreementID: String,
    val agreementDate: LocalDate,
    val agreementHash: String,
    val advisor: Party,
    val avatar: Party,
    val avatarID: String,
    val platform: Party,
    val investment: String,
    val fee: String,
    val status: String,
    val declinedBy: String = "",
    val declineReason: String = "",
) : ContractState, QueryableState, JsonRepresentable {


    fun changeStatus(newStatus: String): AgreementState {
        return AgreementState(
            agreementID,
            agreementDate,
            agreementHash,
            advisor,
            avatar,
            avatarID,
            platform,
            investment,
            fee,
            newStatus,
            declinedBy,
            declineReason
        )
    }

    fun declineAgreement(newDeclinedBy: String, newDeclineReason: String): AgreementState {
        return AgreementState(
            agreementID,
            agreementDate,
            agreementHash,
            advisor,
            avatar,
            avatarID,
            platform,
            investment,
            fee,
            Status.DECLINED.status,
            newDeclinedBy,
            newDeclineReason
        )
    }

    override val participants: List<AbstractParty> get() = listOf(advisor, avatar, platform)

    override fun supportedSchemas(): Iterable<MappedSchema> = listOf(AgreementSchemaV1)
    override fun generateMappedObject(schema: MappedSchema): PersistentState {
        return when (schema) {
            is AgreementSchemaV1 -> AgreementSchemaV1.PersistentAgreement(
                this.avatarID,
                this.agreementID,
                this.investment,
                this.fee,
                this.status,
                this.declinedBy,
                this.declineReason,
                this.agreementDate
            )
            else -> throw IllegalArgumentException("Unrecognised schema $schema")
        }
    }

    fun toDto(): AgreementStateDto {
        return AgreementStateDto(
            agreementID,
            agreementDate.toString(),
            agreementHash,
            advisor.name.toString(),
            avatar.name.toString(),
            avatarID,
            platform.name.toString(),
            investment,
            fee,
            status,
            declinedBy,
            declineReason
        )
    }

    override fun toJsonString(): String {
        return Gson().toJson(this.toDto())
    }

}

data class AgreementStateDto(
    val agreementID: String,
    val agreementDate: String,
    val agreementHash: String,
    val adviser: String,
    val avatar: String,
    val avatarID: String,
    val platform: String,
    val investment: String,
    val fee: String,
    val status: String,
    val declinedBy: String,
    val declineReason: String
)
