package net.corda.c5bastion.util

import com.fasterxml.jackson.databind.ObjectMapper
import java.io.BufferedReader
import java.io.DataOutputStream
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.util.*

fun sendNotification(
    notificationType: String,
    payload: CordaNotification
) {
    val POLITY_HOST: String = System.getenv("POLITY_HOST")
    val SEND_TO = "https://$POLITY_HOST/api/v1/notifications/webhook"
    val url = URL(SEND_TO)

    val uuid = UUID.randomUUID()

    val objectMapper = ObjectMapper()
    val requestBody = mutableMapOf<String, Any?>()
    requestBody["notificationType"] = notificationType
    requestBody["uuid"] = uuid
    requestBody["payload"] = objectMapper.convertValue(payload, MutableMap::class.java)
    requestBody["meta"] = emptyMap<String, Any?>()
    val postData = objectMapper.writeValueAsString(requestBody);
    val conn = url.openConnection() as HttpURLConnection
    conn.requestMethod = "POST"
    conn.doOutput = true
    conn.setRequestProperty("Content-Type", "application/json");
    conn.setRequestProperty("Content-Length", postData.length.toString())
    conn.useCaches = false
    DataOutputStream(conn.outputStream).use { it.writeBytes(postData) }
    BufferedReader(InputStreamReader(conn.inputStream)).use { br ->
        var line: String?
        while (br.readLine().also { line = it } != null) {
            println(line)
        }
    }
}
