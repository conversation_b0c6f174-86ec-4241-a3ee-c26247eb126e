package net.corda.c5bastion.contracts

import net.corda.c5bastion.states.AgreementState
import net.corda.c5bastion.statuses.Status
import net.corda.v5.ledger.contracts.CommandData
import net.corda.v5.ledger.contracts.Contract
import net.corda.v5.ledger.contracts.requireSingleCommand
import net.corda.v5.ledger.contracts.requireThat
import net.corda.v5.ledger.transactions.LedgerTransaction
import net.corda.v5.ledger.transactions.outputsOfType

class ProposeAgreementContract : Contract {
    companion object {
        // Used to identify our contract when building a transaction.
        @JvmStatic
        val ID = "net.corda.c5bastion.contracts.ProposeAgreementContract"
    }

    // A transaction is valid if the verify() function of the contract of all the transaction's input and output states
    // does not throw an exception.
    override fun verify(tx: LedgerTransaction) {
        // Verification logic goes here.
        val command = tx.commands.requireSingleCommand<Commands>()
        when (command.value) {
            is Commands.Create -> requireThat {
                val output = tx.outputsOfType<AgreementState>().single()
                "No inputs should be consumed when sending the Agreement proposal.".using(tx.inputStates.isEmpty())
                "Advisor, Avatar, Platform should be different parties".using(output.advisor != output.avatar)
                "Advisor, Avatar, Platform should be different parties".using(output.avatar != output.platform)
                "Advisor, Avatar, Platform should be different parties".using(output.platform != output.advisor)
                "The 'agreementID' shouldn't be an empty string".using(output.agreementID != "")
                "The 'agreementHash' shouldn't be an empty string".using(output.agreementHash != "")
                "The 'avatarID' shouldn't be an empty string".using(output.avatarID != "")
                "The 'fee' field should contain '%'".using(output.fee.contains("%"))
                "The 'investment' should be a digit".using(output.investment.toIntOrNull() != null)
            }
            is Commands.Sign -> requireThat {
                val output = tx.outputsOfType<AgreementState>().single()
                val inputStatus = Status.PROPOSED
                val outputStatus = Status.SIGNED
                val input = tx.inputsOfType(AgreementState::class.java)[0]
                "You can sign the Agreement that have $inputStatus status only".using(input.status == inputStatus.status)
                "This transaction should only output one AgreementState".using(tx.outputs.size == 1)
                "The output AgreementState should have $outputStatus status".using(output.status == outputStatus.status)
                null
            }
            is Commands.Verify -> requireThat {
                val output = tx.outputsOfType<AgreementState>().single()
                val inputStatus = Status.SIGNED
                val outputStatus = Status.VERIFIED
                val outputStatus2 = Status.DECLINED
                val input = tx.inputsOfType(AgreementState::class.java)[0]
                "You can verify the Agreement that have $inputStatus status only".using(input.status == inputStatus.status)
                "This transaction should only output one AgreementState".using(tx.outputs.size == 1)
                "The output AgreementState should have $outputStatus status".using(output.status == outputStatus.status || output.status == outputStatus2.status)
                null
            }
            is Commands.Notarize -> requireThat {
                val output = tx.outputsOfType<AgreementState>().single()
                val inputStatus = Status.VERIFIED
                val outputStatus = Status.NOTARIZED
                val input = tx.inputsOfType(AgreementState::class.java)[0]
                "You can notarize the Agreement that have $inputStatus status only".using(input.status == inputStatus.status)
                "This transaction should only output one AgreementState".using(tx.outputs.size == 1)
                "The output AgreementState should have $outputStatus status".using(output.status == outputStatus.status)
                null
            }
            is Commands.Decline -> requireThat {
                val output = tx.outputsOfType<AgreementState>().single()
                val outputStatus = Status.DECLINED
                "This transaction should only output one AgreementState".using(tx.outputs.size == 1)
                "The output AgreementState should have $outputStatus status".using(output.status == outputStatus.status)
                null
            }
            is Commands.Complete -> requireThat {
                val inputStatus1 = Status.NOTARIZED
                val inputStatus2 = Status.DECLINED
                val validInputStatuses = listOf(inputStatus1.status, inputStatus2.status)
                val input = tx.inputsOfType(AgreementState::class.java)[0]
                "You can complete the Agreement that have $inputStatus1 or $inputStatus2 statuses".using(validInputStatuses.contains(input.status))
                "This transaction mustn't have any output".using(tx.outputs.isEmpty())
                null
            }
        }
    }

    // Used to indicate the transaction's intent.
    interface Commands : CommandData {
        class Create : Commands
        class Sign : Commands
        class Verify : Commands
        class Notarize : Commands
        class Decline : Commands
        class Complete : Commands
    }
}