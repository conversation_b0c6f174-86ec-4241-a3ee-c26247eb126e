package net.corda.c5bastion.schema

import net.corda.v5.application.identity.Party
import net.corda.v5.base.annotations.CordaSerializable
import net.corda.v5.ledger.schemas.PersistentState
import net.corda.v5.persistence.MappedSchema
import net.corda.v5.persistence.UUIDConverter
import java.time.LocalDate
import java.util.UUID
import javax.persistence.*

object AgreementSchema

@CordaSerializable
object AgreementSchemaV1 : MappedSchema(
    schemaFamily = AgreementSchema.javaClass,
    version = 1,
    mappedTypes = listOf(PersistentAgreement::class.java)
) {

    override val migrationResource: String
        get() = "agreement.changelog-master"
    const val findAllQuery = "SELECT agreement" +
            " FROM net.corda.c5bastion.schema.AgreementSchemaV1\$PersistentAgreement agreement," +
            " net.corda.v5.ledger.schemas.vault.VaultSchemaV1\$VaultState state" +
            " WHERE (state.stateStatus = 0 OR agreement.status='NOTARIZED' OR agreement.status='DECLINED')" +
            " AND state.stateRef.txId = agreement.stateRef.txId" +
            " AND state.stateRef.index = agreement.stateRef.index"
    @Entity
    @NamedQueries(
        NamedQuery(
            name = "AgreementSchemaV1.PersistentAgreement.FindAllUnconsumedOrCompleted",
            query = findAllQuery

        ),
        NamedQuery(
            name = "AgreementSchemaV1.PersistentAgreement.FindAllUnconsumedOrCompletedByAvatarID",
            query = findAllQuery +
                    " AND agreement.avatar_id = :avatarID"

        )
    )
    @Table(name = "agreement_states")
    class PersistentAgreement(
        @Column(name = "avatar_id")
        var avatar_id: String,

        @Column(name = "agreement_id")
        val agreementID: String,

        @Column(name = "investment")
        val investment: String,

        @Column(name = "fee")
        val fee: String,

        @Column(name = "status")
        val status: String,

        @Column(name = "declined_by")
        val declinedBy: String,

        @Column(name = "decline_reason")
        val declineReason: String,

        @Column(name = "agreement_date", nullable = true)
        val agreementDate: LocalDate?,

        ) : PersistentState() {
        // Default constructor required by hibernate.
        constructor() : this("", "", "", "", "", "", "", null)
    }
}