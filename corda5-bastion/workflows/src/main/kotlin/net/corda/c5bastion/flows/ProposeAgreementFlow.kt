package net.corda.c5bastion.flows

import net.corda.c5bastion.contracts.ProposeAgreementContract
import net.corda.c5bastion.states.AgreementState
import net.corda.c5bastion.statuses.Status
import net.corda.c5bastion.util.CordaNotification
import net.corda.c5bastion.util.sendNotification
import net.corda.systemflows.CollectSignaturesFlow
import net.corda.systemflows.FinalityFlow
import net.corda.systemflows.ReceiveFinalityFlow
import net.corda.systemflows.SignTransactionFlow
import net.corda.v5.application.flows.*
import net.corda.v5.application.flows.flowservices.FlowEngine
import net.corda.v5.application.flows.flowservices.FlowIdentity
import net.corda.v5.application.flows.flowservices.FlowMessaging
import net.corda.v5.application.identity.CordaX500Name
import net.corda.v5.application.injection.CordaInject
import net.corda.v5.application.services.IdentityService
import net.corda.v5.application.services.json.JsonMarshallingService
import net.corda.v5.application.services.json.parseJson
import net.corda.v5.application.services.persistence.PersistenceService
import net.corda.v5.base.annotations.Suspendable
import net.corda.v5.ledger.contracts.Command
import net.corda.v5.ledger.contracts.StateAndRef
import net.corda.v5.ledger.contracts.requireThat
import net.corda.v5.ledger.services.NotaryLookupService
import net.corda.v5.ledger.services.vault.StateStatus
import net.corda.v5.ledger.transactions.SignedTransaction
import net.corda.v5.ledger.transactions.SignedTransactionDigest
import net.corda.v5.ledger.transactions.TransactionBuilderFactory
import java.time.LocalDate
import java.util.*
import kotlin.NoSuchElementException
import net.corda.v5.base.util.seconds
import net.corda.v5.ledger.services.vault.SetBasedVaultQueryFilter

@InitiatingFlow
@StartableByRPC
class ProposeAgreementFlow @JsonConstructor constructor(private val params: RpcStartFlowRequestParameters) : Flow<SignedTransactionDigest> {

    @CordaInject
    lateinit var flowEngine: FlowEngine
    @CordaInject
    lateinit var flowIdentity: FlowIdentity
    @CordaInject
    lateinit var flowMessaging: FlowMessaging
    @CordaInject
    lateinit var transactionBuilderFactory: TransactionBuilderFactory
    @CordaInject
    lateinit var identityService: IdentityService
    @CordaInject
    lateinit var notaryLookup: NotaryLookupService
    @CordaInject
    lateinit var jsonMarshallingService: JsonMarshallingService
    @CordaInject
    lateinit var persistenceService: PersistenceService

    @Suspendable
    override fun call(): SignedTransactionDigest {

        // parse parameters
        val mapOfParams: Map<String, String> = jsonMarshallingService.parseJson(params.parametersInJson)

        val agreementID = with(mapOfParams["agreementID"] ?: throw BadRpcStartFlowRequestException("Agreement State Parameter \"agreementID\" missing.")) {
            this
        }

        val agreementDate = with(mapOfParams["agreementDate"] ?: throw BadRpcStartFlowRequestException("BoardingTicket State Parameter \"agreementDate\" missing.")) {
            LocalDate.parse(this)
        }

        val agreementHash = with(mapOfParams["agreementHash"] ?: throw BadRpcStartFlowRequestException("Agreement State Parameter \"agreementHash\" missing.")) {
            this
        }

        val target = with(mapOfParams["avatar"] ?: throw BadRpcStartFlowRequestException("Agreement State Parameter \"avatar\" missing.")) {
            CordaX500Name.parse(this)
        }

        val avatarID = with(mapOfParams["avatarID"] ?: throw BadRpcStartFlowRequestException("Agreement State Parameter \"avatarID\" missing.")) {
            this
        }

        val investment = with(mapOfParams["investment"] ?: throw BadRpcStartFlowRequestException("Agreement State Parameter \"investment\" missing.")) {
            this
        }

        val fee = with(mapOfParams["fee"] ?: throw BadRpcStartFlowRequestException("Agreement State Parameter \"fee\" missing.")) {
            this
        }

        //Check the AgreementID to be unic in CONSUMED and UNCONSUMED states
        val cursor = persistenceService.query<StateAndRef<AgreementState>>(
            queryName = "VaultState.findByStateStatus",
            namedParameters = mapOf("stateStatus" to StateStatus.CONSUMED),
            postFilter = SetBasedVaultQueryFilter.Builder()
                .withContractStateClassNames(setOf(AgreementState::class.java.name))
                .build(),
            postProcessorName = "Corda.IdentityStateAndRefPostProcessor"
        )
        val consumedAgreementStateAndRefList = cursor.poll(100, 20.seconds).values
        val consumedAgreementStateAndRefIndex =
            consumedAgreementStateAndRefList.indexOfLast { it.state.data.agreementID == agreementID }
        requireThat {
            "There agreementID $agreementID should be unic." using (consumedAgreementStateAndRefIndex == -1)
        }
        val cursor1 = persistenceService.query<StateAndRef<AgreementState>>(
            queryName = "VaultState.findByStateStatus",
            namedParameters = mapOf("stateStatus" to StateStatus.UNCONSUMED),
            postFilter = SetBasedVaultQueryFilter.Builder()
                .withContractStateClassNames(setOf(AgreementState::class.java.name))
                .build(),
            postProcessorName = "Corda.IdentityStateAndRefPostProcessor"
        )
        val unconsumedAgreementStateAndRefList = cursor1.poll(100, 20.seconds).values
        val unconsumedAgreementStateAndRefIndex =
            unconsumedAgreementStateAndRefList.indexOfLast { it.state.data.agreementID == agreementID }
        requireThat {
            "There agreementID $agreementID should be unic." using (unconsumedAgreementStateAndRefIndex == -1)
        }

        //val status = "PROPOSED"
        val status = Status.PROPOSED

        val platformName = CordaX500Name.parse("C=US, L=New York, O=Platform, OU=LLC")

        val platformParty = identityService.partyFromName(platformName) ?: throw NoSuchElementException("No party found for X500 name $platformName")

        val recipientParty = identityService.partyFromName(target) ?: throw NoSuchElementException("No party found for X500 name $target")

        val notary = notaryLookup.getNotary(CordaX500Name.parse("O=notary, L=London, C=GB"))!!

        // Stage 1.
        // Generate an unsigned transaction.
        val sender = flowIdentity.ourIdentity
        var agreementState = AgreementState(agreementID, agreementDate, agreementHash, sender, recipientParty, avatarID, platformParty, investment, fee, status.status)
        val signers = agreementState.participants.map { it.owningKey }
        val txCommand = Command(ProposeAgreementContract.Commands.Create(), signers)
        val txBuilder = transactionBuilderFactory.create()
                .setNotary(notary)
                .addOutputState(agreementState, ProposeAgreementContract.ID)
                .addCommand(txCommand)


        // Stage 2.
        // Verify that the transaction is valid.
        txBuilder.verify()

        // Stage 3.
        // Sign the transaction.
        val partSignedTx = txBuilder.sign()

        // Stage 4.
        // Send the state to the counterparty, and receive it back with their signature.
        val sessions = (agreementState.participants - flowIdentity.ourIdentity).map { flowMessaging.initiateFlow(it) }.toSet()
        val fullySignedTx = flowEngine.subFlow(
            CollectSignaturesFlow(
                partSignedTx, sessions,
            )
        )

        // Stage 5.
        // Notarise and record the transaction in all parties' vaults.
        val notarisedTx = flowEngine.subFlow(
            FinalityFlow(
                fullySignedTx, sessions,
            )
        )

        val payload = CordaNotification(
            issuer = sender.name.toString(),
            agreementId = agreementID,
            status = status.status,
            statusPrev = null,
            recipients = listOf(avatarID),
            declinedBy = null,
            declineReason = null
        )

        // Send notification
        sendNotification("SAFEHERON_ACQUISITION", payload)

        return SignedTransactionDigest(
                notarisedTx.id,
                notarisedTx.tx.outputStates.map { output -> jsonMarshallingService.formatJson(output) },
                notarisedTx.sigs
        )
    }

}

@InitiatedBy(ProposeAgreementFlow::class)
class ProposeAgreementFlowAcceptor(val otherPartySession: FlowSession) : Flow<SignedTransaction> {
    @CordaInject
    lateinit var flowEngine: FlowEngine

    // instead, for now, doing this so it can be unit tested separately:
    fun isValid(stx: SignedTransaction) {
        requireThat {
            val output = stx.tx.outputs.single().data
            "This must be an AgreementState transaction." using (output is AgreementState)
        }
    }

    @Suspendable
    override fun call(): SignedTransaction {
        val signTransactionFlow = object : SignTransactionFlow(otherPartySession) {
            override fun checkTransaction(stx: SignedTransaction) = isValid(stx)
        }
        val txId = flowEngine.subFlow(signTransactionFlow).id
        return flowEngine.subFlow(ReceiveFinalityFlow(otherPartySession, expectedTxId = txId))
    }
}