package net.corda.c5bastion.flows

import net.corda.c5bastion.contracts.ProposeAgreementContract
import net.corda.c5bastion.states.AgreementState
import net.corda.c5bastion.statuses.Status
import net.corda.c5bastion.util.CordaNotification
import net.corda.c5bastion.util.sendNotification
import net.corda.systemflows.CollectSignaturesFlow
import net.corda.systemflows.FinalityFlow
import net.corda.systemflows.ReceiveFinalityFlow
import net.corda.systemflows.SignTransactionFlow
import net.corda.v5.application.flows.*
import net.corda.v5.application.flows.flowservices.FlowEngine
import net.corda.v5.application.flows.flowservices.FlowIdentity
import net.corda.v5.application.flows.flowservices.FlowMessaging
import net.corda.v5.application.identity.CordaX500Name
import net.corda.v5.application.injection.CordaInject
import net.corda.v5.application.services.IdentityService
import net.corda.v5.application.services.json.JsonMarshallingService
import net.corda.v5.application.services.json.parseJson
import net.corda.v5.application.services.persistence.PersistenceService
import net.corda.v5.base.annotations.Suspendable
import net.corda.v5.ledger.contracts.Command
import net.corda.v5.ledger.contracts.StateAndRef
import net.corda.v5.ledger.contracts.requireThat
import net.corda.v5.ledger.services.NotaryLookupService
import net.corda.v5.ledger.services.vault.StateStatus
import net.corda.v5.ledger.transactions.SignedTransaction
import net.corda.v5.ledger.transactions.SignedTransactionDigest
import net.corda.v5.ledger.transactions.TransactionBuilderFactory
import java.util.*
import kotlin.NoSuchElementException
import net.corda.v5.base.util.seconds
import net.corda.v5.ledger.services.vault.IdentityContractStatePostProcessor
import net.corda.v5.ledger.services.vault.SetBasedVaultQueryFilter

@InitiatingFlow
@StartableByRPC
class SignAgreementFlow @JsonConstructor constructor(private val params: RpcStartFlowRequestParameters) :
    Flow<SignedTransactionDigest> {

    @CordaInject
    lateinit var flowEngine: FlowEngine
    @CordaInject
    lateinit var flowIdentity: FlowIdentity
    @CordaInject
    lateinit var flowMessaging: FlowMessaging
    @CordaInject
    lateinit var transactionBuilderFactory: TransactionBuilderFactory
    @CordaInject
    lateinit var identityService: IdentityService
    @CordaInject
    lateinit var notaryLookup: NotaryLookupService
    @CordaInject
    lateinit var jsonMarshallingService: JsonMarshallingService
    @CordaInject
    lateinit var persistenceService: PersistenceService

    @Suspendable
    override fun call(): SignedTransactionDigest {

        // parse parameters
        val mapOfParams: Map<String, String> = jsonMarshallingService.parseJson(params.parametersInJson)
        val agreementID = with(
            mapOfParams["agreementID"]
                ?: throw BadRpcStartFlowRequestException("Agreement State Parameter \"agreementID\" missing.")
        ) {
            this
        }

        //Query the agreementState
        val cursor = persistenceService.query<StateAndRef<AgreementState>>(
            queryName = "VaultState.findByStateStatus",
            namedParameters = mapOf("stateStatus" to StateStatus.UNCONSUMED),
            postFilter = SetBasedVaultQueryFilter.Builder()
                .withContractStateClassNames(setOf(AgreementState::class.java.name))
                .build(),
            postProcessorName = "Corda.IdentityStateAndRefPostProcessor"
        )
        val agreementStateAndRefList = cursor.poll(100, 20.seconds).values
        val agreementStateAndRefIndex = agreementStateAndRefList.indexOfLast { it.state.data.agreementID == agreementID }
        requireThat {
            "There is not the $agreementID Agreement in the UNCONSUMED vault." using (agreementStateAndRefIndex != -1)
        }
        val agreementStateAndRef = agreementStateAndRefList.get(agreementStateAndRefIndex)
        val originalAgreementState = agreementStateAndRef.state.data

        //Make sure the initiator is valid
        val initiator = flowIdentity.ourIdentity
        val avatar = originalAgreementState.avatar
        val avatarName = avatar.name.toString()
        val initiatorName = initiator.name.toString()
        requireThat {
            "The initiator - $initiatorName of the transaction must be an $avatarName only." using (initiator == avatar)
        }

        //Building the output
        val status = Status.SIGNED
        val outputAgreementState = originalAgreementState.changeStatus(status.status)

        //Find notary
        val notary = notaryLookup.getNotary(CordaX500Name.parse("O=notary, L=London, C=GB"))!!

        //Building the transaction
        val signers = originalAgreementState.participants.map { it.owningKey }
        val txCommand = Command(ProposeAgreementContract.Commands.Sign(), signers)
        val txBuilder = transactionBuilderFactory.create()
            .setNotary(notary)
            .addInputState(agreementStateAndRef)
            .addOutputState(outputAgreementState, ProposeAgreementContract.ID)
            .addCommand(txCommand)

        // Verify that the transaction is valid.
        txBuilder.verify()

        // Sign the transaction.
        val partSignedTx = txBuilder.sign()

        // Send the state to the counterparty, and receive it back with their signature.
        val sessions =
            (originalAgreementState.participants - flowIdentity.ourIdentity).map { flowMessaging.initiateFlow(it) }
                .toSet()
        val fullySignedTx = flowEngine.subFlow(
            CollectSignaturesFlow(
                partSignedTx, sessions,
            )
        )

        // Notarise and record the transaction in all parties' vaults.
        val notarisedTx = flowEngine.subFlow(
            FinalityFlow(
                fullySignedTx, sessions,
            )
        )

        val payload = CordaNotification(
            issuer = initiatorName,
            agreementId = agreementID,
            status = status.status,
            statusPrev = originalAgreementState.status,
            recipients = listOf(originalAgreementState.advisor.name.toString()),
            declinedBy = null,
            declineReason = null
        )


        // Send notification
        sendNotification("SAFEHERON_ACQUISITION", payload)

        return SignedTransactionDigest(
            notarisedTx.id,
            notarisedTx.tx.outputStates.map { it -> jsonMarshallingService.formatJson(it) },
            notarisedTx.sigs
        )
    }
}


@InitiatedBy(SignAgreementFlow::class)
class SignAgreementFlowResponder(val otherPartySession: FlowSession) : Flow<SignedTransaction> {
    @CordaInject
    lateinit var flowEngine: FlowEngine

    @Suspendable
    override fun call(): SignedTransaction {
        val signTransactionFlow = object : SignTransactionFlow(otherPartySession) {
            override fun checkTransaction(stx: SignedTransaction) {

            }
        }
        val txId = flowEngine.subFlow(signTransactionFlow).id
        return flowEngine.subFlow(ReceiveFinalityFlow(otherPartySession, expectedTxId = txId))
    }
}