//package net.corda.c5bastion.flows
//
//import com.nhaarman.mockito_kotlin.*
//import net.corda.c5bastion.contracts.ProposeAgreementContract
//import net.corda.c5bastion.states.AgreementState
//import net.corda.systemflows.CollectSignaturesFlow
//import net.corda.systemflows.FinalityFlow
//import net.corda.testing.flow.utils.flowTest
//import net.corda.v5.application.flows.RpcStartFlowRequestParameters
//import net.corda.v5.application.identity.CordaX500Name
//import net.corda.v5.application.services.json.parseJson
//import net.corda.v5.ledger.contracts.Command
//import net.corda.v5.ledger.contracts.CommandData
//import org.assertj.core.api.Assertions.assertThat
//import org.assertj.core.api.SoftAssertions.assertSoftly
//import org.junit.jupiter.api.Test
//import java.util.*
//import java.text.DateFormat
//import java.time.LocalDate
//import java.time.format.DateTimeFormatter
//
//class ProposeAgreementUnitTest {
//
//    @Test
//    fun `flow signs state`() {
//        flowTest<ProposeAgreementFlow> {
//
//            // NOTE: this probably should be set up in flowTest
//            val mockNode = CordaX500Name.parse("O=MockNode, L=London, C=GB, OU=Template")
//
//            /*The inputParams does not carry actually purpose when running the test.
//             *It is there only to help the flow execute.
//             *All of the returned value is populated with the doReturn() methods */
//            val inputParams = "{\"agreementID\": \"A1235813\", \"agreementDate\": \"2023-11-02\", \"avatar\": \"${mockNode}\", \"investment\": \"$200000\", \"fee\": \"10%\"}"
//            createFlow { ProposeAgreementFlow(RpcStartFlowRequestParameters(inputParams)) }
//
//            //Set the return value of the flow
//            doReturn(notary)
//                    .whenever(flow.notaryLookup)
//                    .getNotary(CordaX500Name.parse("O=notary, L=London, C=GB"))
//            doReturn(mockNode)
//                .whenever(otherSide)
//                .name
//            doReturn(otherSide)
//                .whenever(flow.identityService)
//                .partyFromName(mockNode)
//
//            doReturn(signedTransactionMock)
//                .whenever(flow.flowEngine)
//                .subFlow(any<CollectSignaturesFlow>())
//
//            doReturn(signedTransactionMock)
//                .whenever(flow.flowEngine)
//                .subFlow(any<FinalityFlow>())
//
//            doReturn(
//                    mapOf(
//                        "agreementID" to "A1235813",
//                        "agreementDate" to "2023-11-02",
//                        "avatar" to otherSide.name.toString(),
//                        "investment" to "$200000",
//                        "fee" to "10%"
//                    )
//            )
//                .whenever(flow.jsonMarshallingService)
//                .parseJson<Map<String, String>>(inputParams)
//
//            flow.call()
//
//            // verify notary is set
//            verify(transactionBuilderMock).setNotary(notary)
//
//            // verify the correct output state is created
//            argumentCaptor<AgreementState>().apply {
//                verify(transactionBuilderMock).addOutputState(capture(), eq(ProposeAgreementContract.ID))
//                assertSoftly {
//                    it.assertThat(firstValue.advisor).isEqualTo(ourIdentity)
//                    it.assertThat(firstValue.avatar).isEqualTo(otherSide)
//                    it.assertThat(firstValue.agreementID).isEqualTo("A1235813")
//                    it.assertThat(firstValue.agreementDate).isEqualTo("2023-11-02")
//                    it.assertThat(firstValue.investment).isEqualTo("$200000")
//                    it.assertThat(firstValue.fee).isEqualTo("10%")
//                }
//            }
//
//            // verify command is added
//            argumentCaptor<Command<CommandData>>().apply {
//                verify(transactionBuilderMock).addCommand(capture())
//                assertThat(firstValue.value).isInstanceOf(ProposeAgreementContract.Commands.Create::class.java)
//                assertThat(firstValue.signers).contains(ourIdentity.owningKey)
//                assertThat(firstValue.signers).contains(otherSide.owningKey)
//            }
//        }
//    }
//}
